#!/usr/bin/env python3
"""
测试完整任务流程
"""

import sys
import os
import tempfile
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from scheduler_day_allapp_optimized import TaskScheduler

def test_complete_task_script_generation():
    """测试完整任务脚本生成"""
    print("测试完整任务脚本生成...")
    
    try:
        scheduler = TaskScheduler()
        
        # 生成脚本内容
        script_content = scheduler._generate_complete_task_script(
            session_cgi_label="test_cgi",
            use_multiprocess=True,
            max_workers=2,
            chunk_size=1000
        )
        
        print("✅ 脚本生成成功")
        print(f"脚本长度: {len(script_content)} 字符")
        
        # 检查脚本内容是否包含关键步骤
        required_steps = [
            "Step 1: Determining the generated file name",
            "Step 2: Running xml_decode_b_understand_all_app.py",
            "Step 2.5: Copying file to destination",
            "Step 3: Running group_by_session_id.py",
            "Step 4: Copying",
            "Step 5: Cleaning up generated files"
        ]
        
        for step in required_steps:
            if step in script_content:
                print(f"✅ 包含步骤: {step}")
            else:
                print(f"❌ 缺少步骤: {step}")
                return False
        
        # 检查参数是否正确传递
        if "test_cgi" in script_content:
            print("✅ session_cgi_label 参数传递正确")
        else:
            print("❌ session_cgi_label 参数传递失败")
            return False
            
        if "--max_workers=2" in script_content:
            print("✅ max_workers 参数传递正确")
        else:
            print("❌ max_workers 参数传递失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 脚本生成测试失败: {e}")
        return False

def test_script_syntax():
    """测试生成的脚本语法是否正确"""
    print("\n测试生成脚本的语法...")
    
    try:
        scheduler = TaskScheduler()
        
        # 生成脚本内容
        script_content = scheduler._generate_complete_task_script(
            session_cgi_label="test_cgi",
            use_multiprocess=True,
            max_workers=2,
            chunk_size=1000
        )
        
        # 写入临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script_content)
            temp_script_path = f.name
        
        try:
            # 尝试编译脚本检查语法
            with open(temp_script_path, 'r') as f:
                compile(f.read(), temp_script_path, 'exec')
            
            print("✅ 脚本语法检查通过")
            return True
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_script_path):
                os.remove(temp_script_path)
        
    except SyntaxError as e:
        print(f"❌ 脚本语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 脚本语法测试失败: {e}")
        return False

def test_task_script_creation():
    """测试任务脚本创建和清理"""
    print("\n测试任务脚本创建和清理...")
    
    try:
        scheduler = TaskScheduler()
        
        # 模拟创建任务脚本
        task_id = "test_task_001"
        script_path = f"temp_task_{task_id}.py"
        
        # 生成并保存脚本
        script_content = scheduler._generate_complete_task_script(
            session_cgi_label="test_cgi",
            use_multiprocess=False,
            max_workers=None,
            chunk_size=500
        )
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        if os.path.exists(script_path):
            print(f"✅ 临时脚本创建成功: {script_path}")
        else:
            print(f"❌ 临时脚本创建失败")
            return False
        
        # 测试清理功能
        scheduler._cleanup_temp_script(task_id)
        
        if not os.path.exists(script_path):
            print(f"✅ 临时脚本清理成功")
        else:
            print(f"❌ 临时脚本清理失败")
            # 手动清理
            os.remove(script_path)
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务脚本创建测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始完整任务流程测试...")
    
    success = True
    
    # 运行测试
    success &= test_complete_task_script_generation()
    success &= test_script_syntax()
    success &= test_task_script_creation()
    
    if success:
        print("\n🎉 所有测试通过！完整任务流程已实现。")
        print("\n现在异步任务将执行完整的处理流程：")
        print("1. XML 解码")
        print("2. 文件复制到 pickle 目录")
        print("3. 会话分组处理")
        print("4. JSON 文件复制到 session 目录")
        print("5. 清理临时文件")
    else:
        print("\n❌ 部分测试失败，请检查代码。")
        sys.exit(1)
