#!/usr/bin/env python3
"""
测试优化后的状态管理逻辑
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from scheduler_day_allapp_optimized import TaskScheduler, TaskInfo, TaskStatus

def test_state_loading_once():
    """测试状态只加载一次"""
    print("测试状态只加载一次...")
    
    try:
        scheduler = TaskScheduler()
        
        # 第一次加载
        initial_loaded = scheduler._state_loaded
        scheduler._load_task_state()
        after_first_load = scheduler._state_loaded
        
        # 第二次加载（应该被跳过）
        scheduler._load_task_state()
        after_second_load = scheduler._state_loaded
        
        if not initial_loaded:
            print("✅ 初始状态：未加载")
        else:
            print("❌ 初始状态应该是未加载")
            return False
            
        if after_first_load:
            print("✅ 第一次加载后：已加载")
        else:
            print("❌ 第一次加载后应该标记为已加载")
            return False
            
        if after_second_load:
            print("✅ 第二次加载后：仍然是已加载状态")
        else:
            print("❌ 第二次加载后状态异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_save_frequency_limit():
    """测试保存频率限制"""
    print("\n测试保存频率限制...")
    
    try:
        scheduler = TaskScheduler()
        
        # 记录初始保存时间
        initial_save_time = scheduler._last_save_time
        
        # 第一次保存
        scheduler._save_task_state(force=True)
        first_save_time = scheduler._last_save_time
        
        # 立即再次保存（应该被跳过）
        scheduler._save_task_state()
        second_save_time = scheduler._last_save_time
        
        # 等待一段时间后保存
        time.sleep(1)
        scheduler._save_task_state()
        third_save_time = scheduler._last_save_time
        
        if first_save_time > initial_save_time:
            print("✅ 强制保存成功更新时间戳")
        else:
            print("❌ 强制保存未更新时间戳")
            return False
            
        if second_save_time == first_save_time:
            print("✅ 频繁保存被正确跳过")
        else:
            print("❌ 频繁保存未被跳过")
            return False
            
        # 注意：由于频率限制是5秒，1秒后的保存仍会被跳过
        if third_save_time == first_save_time:
            print("✅ 短时间内的保存被正确跳过")
        else:
            print("❌ 短时间内的保存未被跳过")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_cleanup_finished_tasks_efficiency():
    """测试清理完成任务的效率"""
    print("\n测试清理完成任务的效率...")
    
    try:
        scheduler = TaskScheduler()
        
        # 模拟添加一些已完成的任务
        for i in range(3):
            task_info = TaskInfo(
                task_id=f"completed_task_{i}",
                session_cgi_label="cgi7",
                start_time=datetime.now(),
                end_time=datetime.now(),
                status=TaskStatus.COMPLETED
            )
            scheduler.completed_tasks.append(task_info)
        
        # 记录初始保存时间
        initial_save_time = scheduler._last_save_time
        
        # 多次调用清理方法（模拟正常运行时的频繁调用）
        for i in range(5):
            scheduler._cleanup_finished_tasks()
            time.sleep(0.1)  # 短暂间隔
        
        final_save_time = scheduler._last_save_time
        
        # 由于没有运行中的任务完成，保存时间应该没有变化（或变化很少）
        if final_save_time == initial_save_time:
            print("✅ 没有变化时避免了频繁保存")
        else:
            # 检查是否只保存了一次
            print(f"ℹ️ 保存时间有变化，但应该是合理的频率限制")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_force_save_override():
    """测试强制保存覆盖频率限制"""
    print("\n测试强制保存覆盖频率限制...")
    
    try:
        scheduler = TaskScheduler()
        
        # 第一次强制保存
        scheduler._save_task_state(force=True)
        first_save_time = scheduler._last_save_time
        
        # 立即再次强制保存（应该成功）
        scheduler._save_task_state(force=True)
        second_save_time = scheduler._last_save_time
        
        if second_save_time > first_save_time:
            print("✅ 强制保存成功覆盖频率限制")
        else:
            print("❌ 强制保存未能覆盖频率限制")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def cleanup():
    """清理测试文件"""
    try:
        if os.path.exists('scheduler_state.json'):
            os.remove('scheduler_state.json')
        print("✅ 清理测试文件成功")
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")

if __name__ == "__main__":
    print("开始优化后的状态管理测试...")
    
    success = True
    
    # 运行测试
    success &= test_state_loading_once()
    success &= test_save_frequency_limit()
    success &= test_cleanup_finished_tasks_efficiency()
    success &= test_force_save_override()
    
    # 清理
    cleanup()
    
    if success:
        print("\n🎉 所有测试通过！状态管理逻辑已优化。")
        print("\n优化效果：")
        print("✅ 状态文件只在启动时加载一次")
        print("✅ 避免频繁保存状态文件（5秒内最多保存一次）")
        print("✅ 重要操作时可以强制保存")
        print("✅ 减少了不必要的文件I/O操作")
    else:
        print("\n❌ 部分测试失败，请检查代码。")
        sys.exit(1)
