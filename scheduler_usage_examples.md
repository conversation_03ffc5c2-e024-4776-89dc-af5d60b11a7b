# 优化版调度器使用说明

## 主要改进

1. **非阻塞任务执行**：任务在后台异步执行，不会阻塞调度器
2. **并发控制**：支持同时运行多个任务（默认最多2个）
3. **日志文件管理**：所有任务输出自动保存到日志文件
4. **任务状态跟踪**：完整的任务生命周期管理
5. **简洁高效**：移除复杂的超时管理，专注核心功能

## 基本用法

### 1. 启动单次任务（异步模式）
```bash
python scheduler_day_allapp_optimized.py --run_once --session_cgi_label cgi7
```

### 2. 启动定时任务（异步模式）
```bash
python scheduler_day_allapp_optimized.py --start_time 00:00 --end_time 00:10 --session_cgi_label cgi7
```

### 3. 查看任务日志
```bash
# 直接查看日志文件
tail -f logs/cgi7_20231215.log

# 或者使用其他日志查看工具
less logs/cgi7_20231215.log
```

## 高级配置

### 并发设置
```bash
python scheduler_day_allapp_optimized.py \
  --max_concurrent 3 \
  --session_cgi_label cgi7
```

### 多进程优化
```bash
python scheduler_day_allapp_optimized.py \
  --run_once \
  --use_multiprocess \
  --max_workers 4 \
  --chunk_size 2000 \
  --session_cgi_label cgi7
```

## 日志功能详解

### 1. 日志文件位置
- 所有任务日志保存在 `logs/` 目录下
- 文件名格式：`{task_id}.log`
- 例如：`logs/cgi7_20231215.log`

### 2. 日志查看命令
```bash
# 查看任务日志
tail -f logs/cgi7_20231215.log

# 查看最近的日志
tail -n 50 logs/cgi7_20231215.log
```

## 任务状态管理

### 任务状态类型
- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 成功完成
- `failed`: 执行失败
- `cancelled`: 被取消

### 状态持久化
- 任务状态保存在 `scheduler_state.json`
- 调度器重启后会恢复状态信息
- 支持查看历史任务记录

## 故障处理

### 1. 并发限制
- 默认最大并发：2个任务
- 达到限制时新任务会被拒绝
- 可通过 `--max_concurrent` 调整

### 2. 优雅关闭
- 使用 Ctrl+C 优雅关闭调度器
- 会终止正在运行的任务

## 监控和调试

### 日志分析
```bash
# 查看调度器日志
tail -f scheduler.log

# 查看特定任务日志
tail -f logs/cgi7_20231215.log
```

## 兼容性说明

### 同步模式（兼容旧版本）
```bash
# 使用同步模式（阻塞执行）
python scheduler_day_allapp_optimized.py --run_once --sync_mode --session_cgi_label cgi7
```

### 原有函数接口
- `run_task_optimized()`: 现在默认使用异步模式
- `run_task_sync()`: 新增的同步模式函数
- `run_task()`: 保持兼容性的原始接口

## 最佳实践

1. **生产环境**：使用异步模式，设置合理的并发数
2. **调试阶段**：使用 `tail -f` 查看任务执行日志
3. **监控运维**：定期检查日志文件
4. **资源管理**：根据服务器性能调整 `--max_workers` 和 `--chunk_size`
