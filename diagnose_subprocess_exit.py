#!/usr/bin/env python3
"""
诊断子进程退出原因的脚本
"""

import os
import subprocess
import time
import signal
import psutil
import resource
import sys
from datetime import datetime

def check_system_resources():
    """检查系统资源状况"""
    print("=" * 50)
    print("系统资源检查")
    print("=" * 50)
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / (1024**3):.2f} GB")
    print(f"可用内存: {memory.available / (1024**3):.2f} GB")
    print(f"已用内存: {memory.used / (1024**3):.2f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    
    # 交换空间
    swap = psutil.swap_memory()
    print(f"交换空间总量: {swap.total / (1024**3):.2f} GB")
    print(f"交换空间使用: {swap.used / (1024**3):.2f} GB")
    print(f"交换空间使用率: {swap.percent:.1f}%")
    
    # CPU信息
    print(f"CPU核心数: {psutil.cpu_count()}")
    print(f"CPU使用率: {psutil.cpu_percent(interval=1):.1f}%")
    
    # 磁盘空间
    disk = psutil.disk_usage('.')
    print(f"磁盘总空间: {disk.total / (1024**3):.2f} GB")
    print(f"磁盘可用空间: {disk.free / (1024**3):.2f} GB")
    print(f"磁盘使用率: {(disk.used / disk.total) * 100:.1f}%")

def check_process_limits():
    """检查进程资源限制"""
    print("\n" + "=" * 50)
    print("进程资源限制检查")
    print("=" * 50)
    
    # 获取资源限制
    limits = [
        (resource.RLIMIT_AS, "虚拟内存"),
        (resource.RLIMIT_DATA, "数据段"),
        (resource.RLIMIT_STACK, "栈大小"),
        (resource.RLIMIT_RSS, "常驻内存"),
        (resource.RLIMIT_NPROC, "进程数"),
        (resource.RLIMIT_NOFILE, "文件描述符"),
        (resource.RLIMIT_CPU, "CPU时间"),
    ]
    
    for limit_type, name in limits:
        try:
            soft, hard = resource.getrlimit(limit_type)
            if soft == resource.RLIM_INFINITY:
                soft_str = "无限制"
            else:
                if limit_type in [resource.RLIMIT_AS, resource.RLIMIT_DATA, resource.RLIMIT_STACK, resource.RLIMIT_RSS]:
                    soft_str = f"{soft / (1024**2):.0f} MB"
                else:
                    soft_str = str(soft)
            
            if hard == resource.RLIM_INFINITY:
                hard_str = "无限制"
            else:
                if limit_type in [resource.RLIMIT_AS, resource.RLIMIT_DATA, resource.RLIMIT_STACK, resource.RLIMIT_RSS]:
                    hard_str = f"{hard / (1024**2):.0f} MB"
                else:
                    hard_str = str(hard)
                    
            print(f"{name}: 软限制={soft_str}, 硬限制={hard_str}")
        except (OSError, ValueError):
            print(f"{name}: 无法获取限制信息")

def monitor_subprocess_execution():
    """监控子进程执行"""
    print("\n" + "=" * 50)
    print("子进程执行监控")
    print("=" * 50)
    
    # 创建一个简单的测试脚本
    test_script = '''
import time
import psutil
import os
import sys

def log_memory():
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / (1024 * 1024)
    print(f"[{time.strftime('%H:%M:%S')}] PID={os.getpid()}, Memory={memory_mb:.1f}MB")
    return memory_mb

print("测试子进程开始执行")
log_memory()

# 模拟一些内存使用
data = []
for i in range(10):
    # 每次分配约10MB内存
    chunk = [0] * (1024 * 1024)  # 约4MB (每个int 4字节)
    data.append(chunk)
    memory_mb = log_memory()
    
    # 检查内存是否超过限制
    if memory_mb > 1000:  # 1GB限制
        print("内存使用超过1GB，退出")
        sys.exit(1)
    
    time.sleep(1)

print("测试子进程正常完成")
'''
    
    script_path = "test_memory_subprocess.py"
    with open(script_path, 'w') as f:
        f.write(test_script)
    
    try:
        print("启动内存测试子进程...")
        
        # 启动子进程并监控
        process = subprocess.Popen(
            [sys.executable, script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"子进程PID: {process.pid}")
        
        # 监控子进程
        start_time = time.time()
        max_memory = 0
        
        try:
            psutil_process = psutil.Process(process.pid)
        except psutil.NoSuchProcess:
            psutil_process = None
        
        while process.poll() is None:
            if psutil_process:
                try:
                    memory_info = psutil_process.memory_info()
                    memory_mb = memory_info.rss / (1024 * 1024)
                    max_memory = max(max_memory, memory_mb)
                    
                    # 读取子进程输出
                    if process.stdout:
                        line = process.stdout.readline()
                        if line:
                            print(f"子进程输出: {line.strip()}")
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    break
            
            time.sleep(0.5)
            
            # 超时保护
            if time.time() - start_time > 30:
                print("子进程执行超时，终止")
                process.terminate()
                break
        
        # 等待进程结束
        try:
            stdout, stderr = process.communicate(timeout=5)
            if stdout:
                print("剩余输出:")
                print(stdout)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
        
        print(f"子进程退出码: {process.returncode}")
        print(f"子进程最大内存使用: {max_memory:.1f} MB")
        print(f"子进程运行时间: {time.time() - start_time:.1f} 秒")
        
        # 分析退出原因
        if process.returncode == 0:
            print("✅ 子进程正常退出")
        elif process.returncode == 1:
            print("❌ 子进程因错误退出")
        elif process.returncode == -9:
            print("❌ 子进程被SIGKILL信号杀死（可能是OOM）")
        elif process.returncode == -15:
            print("❌ 子进程被SIGTERM信号终止")
        else:
            print(f"❌ 子进程异常退出，退出码: {process.returncode}")
            
    except Exception as e:
        print(f"监控子进程时出错: {e}")
    finally:
        # 清理测试文件
        if os.path.exists(script_path):
            os.remove(script_path)

def check_oom_killer():
    """检查系统OOM killer日志"""
    print("\n" + "=" * 50)
    print("OOM Killer 检查")
    print("=" * 50)
    
    try:
        # 在Linux系统上检查dmesg
        result = subprocess.run(['dmesg', '-T'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            oom_lines = [line for line in lines if 'killed process' in line.lower() or 'out of memory' in line.lower()]
            
            if oom_lines:
                print("发现OOM killer活动:")
                for line in oom_lines[-5:]:  # 显示最近5条
                    print(f"  {line}")
            else:
                print("未发现最近的OOM killer活动")
        else:
            print("无法访问dmesg（可能需要权限）")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("无法检查OOM killer日志（可能不是Linux系统）")

def main():
    """主函数"""
    print(f"子进程退出原因诊断 - {datetime.now()}")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"当前进程PID: {os.getpid()}")
    
    # 1. 检查系统资源
    check_system_resources()
    
    # 2. 检查进程限制
    check_process_limits()
    
    # 3. 检查OOM killer
    check_oom_killer()
    
    # 4. 监控子进程执行
    monitor_subprocess_execution()
    
    print("\n" + "=" * 50)
    print("诊断完成")
    print("=" * 50)
    
    print("\n可能的子进程退出原因:")
    print("1. 内存不足 (OOM) - 检查内存使用情况和OOM killer日志")
    print("2. 进程资源限制 - 检查ulimit设置")
    print("3. 程序逻辑错误 - 检查应用程序日志")
    print("4. 外部信号 - 检查是否有其他进程发送信号")
    print("5. 磁盘空间不足 - 检查磁盘使用情况")

if __name__ == "__main__":
    main()
