#!/usr/bin/env python3
"""
测试调度器状态持久化功能
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from scheduler_day_allapp_optimized import TaskScheduler, TaskInfo, TaskStatus

def test_running_task_state_save():
    """测试正在运行任务的状态保存"""
    print("测试正在运行任务的状态保存...")
    
    try:
        # 创建调度器
        scheduler = TaskScheduler()
        
        # 模拟添加正在运行的任务
        task_info = TaskInfo(
            task_id="test_running_001",
            session_cgi_label="cgi7",
            start_time=datetime.now(),
            status=TaskStatus.RUNNING,
            log_file="logs/test_running_001.log"
        )
        
        scheduler.running_tasks["test_running_001"] = task_info
        
        # 保存状态
        scheduler._save_task_state()
        print("✅ 正在运行任务状态保存成功")
        
        # 检查保存的内容
        if os.path.exists('scheduler_state.json'):
            with open('scheduler_state.json', 'r') as f:
                state = json.load(f)
                
            running_tasks = state.get('running_tasks', [])
            if len(running_tasks) == 1:
                print(f"✅ 保存了 {len(running_tasks)} 个正在运行的任务")
                
                task_data = running_tasks[0]
                if task_data['task_id'] == 'test_running_001':
                    print("✅ 任务ID保存正确")
                else:
                    print(f"❌ 任务ID不正确: {task_data['task_id']}")
                    return False
                    
                if 'script_path' in task_data:
                    print("✅ 脚本路径信息已保存")
                else:
                    print("❌ 脚本路径信息缺失")
                    return False
            else:
                print(f"❌ 保存的运行任务数量不正确: {len(running_tasks)}")
                return False
        else:
            print("❌ 状态文件未创建")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_state_recovery_and_cleanup():
    """测试状态恢复和清理功能"""
    print("\n测试状态恢复和清理功能...")
    
    try:
        # 创建一些临时脚本文件模拟孤儿文件
        temp_scripts = [
            "temp_task_orphan_001.py",
            "temp_task_orphan_002.py"
        ]
        
        for script in temp_scripts:
            with open(script, 'w') as f:
                f.write("# 临时测试脚本\nprint('test')\n")
        
        print(f"✅ 创建了 {len(temp_scripts)} 个临时脚本文件")
        
        # 创建新的调度器实例（模拟重启）
        new_scheduler = TaskScheduler()
        
        # 测试孤儿脚本清理
        new_scheduler._cleanup_orphaned_scripts()
        
        # 检查脚本是否被清理
        remaining_scripts = [script for script in temp_scripts if os.path.exists(script)]
        if len(remaining_scripts) == 0:
            print("✅ 所有孤儿脚本已被清理")
        else:
            print(f"❌ 还有 {len(remaining_scripts)} 个脚本未被清理")
            # 手动清理
            for script in remaining_scripts:
                os.remove(script)
            return False
        
        # 测试状态加载
        new_scheduler._load_task_state()
        
        # 检查之前运行的任务是否被正确处理
        completed_tasks = new_scheduler.completed_tasks
        cancelled_tasks = [task for task in completed_tasks if task.status == TaskStatus.CANCELLED]
        
        if len(cancelled_tasks) > 0:
            print(f"✅ {len(cancelled_tasks)} 个之前运行的任务被标记为已取消")
            
            # 检查取消原因
            for task in cancelled_tasks:
                if "调度器重启" in task.error_message:
                    print("✅ 取消原因记录正确")
                else:
                    print(f"❌ 取消原因不正确: {task.error_message}")
                    return False
        else:
            print("ℹ️ 没有发现之前运行的任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_state_file_structure():
    """测试状态文件结构"""
    print("\n测试状态文件结构...")
    
    try:
        if not os.path.exists('scheduler_state.json'):
            print("❌ 状态文件不存在")
            return False
        
        with open('scheduler_state.json', 'r') as f:
            state = json.load(f)
        
        # 检查必要的字段
        required_fields = ['running_tasks', 'completed_tasks']
        for field in required_fields:
            if field in state:
                print(f"✅ 包含必要字段: {field}")
            else:
                print(f"❌ 缺少必要字段: {field}")
                return False
        
        # 检查数据类型
        if isinstance(state['running_tasks'], list):
            print("✅ running_tasks 是列表类型")
        else:
            print(f"❌ running_tasks 类型错误: {type(state['running_tasks'])}")
            return False
            
        if isinstance(state['completed_tasks'], list):
            print("✅ completed_tasks 是列表类型")
        else:
            print(f"❌ completed_tasks 类型错误: {type(state['completed_tasks'])}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def cleanup():
    """清理测试文件"""
    try:
        files_to_clean = [
            'scheduler_state.json',
            'temp_task_orphan_001.py',
            'temp_task_orphan_002.py'
        ]
        
        for file_path in files_to_clean:
            if os.path.exists(file_path):
                os.remove(file_path)
        
        print("✅ 清理测试文件成功")
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")

if __name__ == "__main__":
    print("开始状态持久化测试...")
    
    success = True
    
    # 运行测试
    success &= test_running_task_state_save()
    success &= test_state_recovery_and_cleanup()
    success &= test_state_file_structure()
    
    # 清理
    cleanup()
    
    if success:
        print("\n🎉 所有测试通过！状态持久化功能正常工作。")
        print("\n现在调度器可以：")
        print("✅ 保存正在运行任务的基本信息")
        print("✅ 在重启时清理孤儿脚本文件")
        print("✅ 将中断的任务标记为已取消")
        print("✅ 避免重复启动相同任务")
    else:
        print("\n❌ 部分测试失败，请检查代码。")
        sys.exit(1)
