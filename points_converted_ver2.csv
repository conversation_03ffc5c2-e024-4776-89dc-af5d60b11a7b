问题,答案
腾讯云金融风控大模型,"{""青云课题背景"": ""基于机器学习的风险量化是金融风控的重要组成部分，信贷业务受限于样本积累速度，和模型复杂程度，传统的风控模型在风险识别能力和迭代速度上都存在明显的不足。基于生成式网络结构和深度学习的AI大模型，经过海量的风控知识预训练之后，能学习到更长周期，更大复杂度的风险模式，并通过迁移学习技术自适应的匹配垂直场景，能够提供更好的风险识别准确率，并大大降低样本要求。在此业务背景下，我们依托腾讯安全20多年沉淀的海量欺诈样本和多场景下的丰富风控模型能力，启动金融风控大模型研发项目，旨在帮助客户提升金融风控模型的风险量化准确度和动态风险管理。"", ""青云课题价值"": ""1、推动金融风控大模型的建立，构建行业标准和影响力，成为行业领先标准；\n\n2、帮助客户提升金融风控模型的风险量化准确度和动态风险管理。"", ""青云课题挑战"": ""1. 探索金融风控大模型的行业标准和模型生成范式（包含AI网络结构、训练方法）：要求能够高效融合上万维度风险异构特征、上亿规模的欺诈样本、数千个跨场景风控模型等海量的多模态风控知识；\n\n2. 设计金融风控大模型的迭代模式：要求能继承上个版本的模型能力同时又能融入新的欺诈知识，从而实现自我进化；\n\n3. 基于金融风控大模型和不同金融客户的风控需求，针对小样本场景（几百或上千建模样本），自适应迁移生成符合客户需求的风控模型。"", ""BG"": ""CSIG"", ""部门"": ""安全产品二部"", ""中心"": ""金融风控产品中心""}"
基础安全领域智能体研究与应用,"{""青云课题背景"": ""随着人工智能技术的飞速发展，安全领域正在迎来前所未有的智能化升级机遇。在安全能力方面，大语言模型可助力解决海量告警、复杂事件关联分析、威胁处置决策等关键工作；在产品交互方面，基于大模型智能体构建安全助手，可提升安全运营的智能化水平，帮助企业实现“可解释、可信赖、自闭环”的智能防御体系。"", ""青云课题价值"": ""1、提升安全运营的智能化水平，实现内部安全运营效率的整体提升\n2、通过提升安全运营的智能化水平，为客户提供更好的产品体验"", ""青云课题挑战"": ""1. 安全领域对响应准确性和稳定性要求极高，Agent必须在提供智能建议时确保输出的可靠性，避免引入新的安全风险；\n2. 安全场景高度复杂且数据量较大，Agent需要能高效地理解安全术语、告警日志、威胁情报，精准地提取关键信息，形成正确推理。"", ""BG"": ""CSIG"", ""部门"": ""安全产品三部"", ""中心"": ""主机安全产品中心""}"
基于多模态（轨迹、卫星图、图片、拓扑图）大模型的路网自动生成和维护方法,"{""青云课题背景"": ""传统路网作业依赖采集车采集和人工作业，面临效率低、时效慢、成本高的问题，越来越难以满足实际场景的需求。\n随着手机、车机等各种传感器的广泛使用，多种模态的海量道路数据资料采集成为现实，结合大模型的多模感知能力，本项目旨在能够更加高效、快速、低成本的实现图结构路网数据的自动生成和维护。"", ""青云课题价值"": ""1. 地图制作的精度与效率突破- 生成精度提升：多模态融合解决单一数据盲区，数据精度更高；- 维护实时性：自动检测路网变更（如新修道路），更新周期从天周级缩短至小时级。\n2. 地图制作降本增效- 降低数据采集成本：减少采集车定向数据采集的成本；- 替代人工成本：减少人工标注和作业工作量；"", ""青云课题挑战"": ""1. 数据层：异构融合难题\n(1) 时空对齐（轨迹点、卫星图像、众包图像、已有路网）；\n(2) 多模态标注稀缺。2. 模型层：跨模态理解瓶颈\n(1) 轨迹大模型理解：基础大模型对NLP、图像理解较好，但轨迹理解缺少成熟可用模型；\n(2) 语义鸿沟问题：卫星图“像素块”与拓扑图“节点关系”难以关联；\n(3) 推理算力与数据覆盖问题：大模型部署算力问题；\n(4) 模型的幻觉问题：训练出的大模型产生幻觉会影响真实世界的路网生成。3. 应用层：场景泛化与鲁棒性\n(1) 地图制作的人工规范：模型生成的路网需要考虑数据的制作规范；\n(2) 地图逻辑控制：引入交通规则知识库约束生成结果。"", ""BG"": ""CSIG"", ""部门"": ""地图平台部"", ""中心"": ""动态应用技术中心""}"
基于多模态融合识别的具身智能方法研究,"{""青云课题背景"": ""腾讯自动驾驶多模态感知项目，研究前沿自动驾驶感知技术的车端落地转化，利用车载传感器如激光雷达、车载相机、毫米波雷达、惯导等；拓展自动驾驶车端感知的要素感知类别、提升感知识别精度、降低感知时延、提升多模态感知鲁棒性，为自动驾驶决策提供精确的环境理解结果；研究前融合BEV动态障碍物感知、通用障碍物识别、BEV局部实时建图、Occupancy占用网络、端到端自动驾驶等前沿课题。"", ""青云课题价值"": ""本课题旨在运用大模型技术深化物理建模，聚焦两大方向：基于生成式大模型构建高精度渲染仿真系统以生成物理合规的合成数据；建立四维时空（三维空间+时间）全场景动态重建与数字孪生体系。其价值体现在理论层面突破传统物理建模局限，实现虚实映射；应用层面为AI训练提供可扩展数据源，为具身智能提供4D时序仿真与现实结合的真值数据链。两项技术的融合将推动人工智能在物理模拟与虚实交互领域的突破。"", ""青云课题挑战"": ""课题挑战：\n\n1. 如何在有限的标注数据前提下，利用自监督、半监督、主动学习等算法，进行开集场景数据标签化；\n\n2. 如何设计抗噪能力强的感知算法模型，降低算法模型对于传感器扰动、异常的影响，提升算法鲁棒性；\n\n3. 如何结合生成式大模型，结合渲染与仿真系统，为自动驾驶提供逼真的人造数据；\n\n4. 如何构建时序4D空间下的全场景还原，进而进行空间体素的占有真值生成。\n\n\n职位要求：\n\n1. 在自动驾驶领域具有相关研究经历，熟悉激光、相机、融合等感知算法；\n\n2. 熟悉自动驾驶感知的原理，具备自动驾驶感知的实际算法、算法工程经历；\n\n3. 熟练运用Pytorch深度学习框架，熟练运用Python，C++为加分项，具备良好的编程和软件开发能力；\n\n4. 具备良好的问题解决能力、团队合作精神和沟通能力；\n\n5. 有领域具备业界影响力的顶会文章者优先，如CVPR、ICCV、ECCV、ICLR、NeuralPS等。"", ""BG"": ""CSIG"", ""部门"": ""地图平台部"", ""中心"": ""自动驾驶实验室""}"
二进制软件安全领域的关键技术研究,"{""青云课题背景"": ""随着软件规模的不断扩大和网络攻击手段的日益复杂化，二进制程序的安全分析面临着前所未有的挑战。传统的静态分析等技术在处理大规模、高度混淆的二进制文件时，往往存在效率低、误报率高、自动化程度不足等问题。与此同时，人工智能技术的快速发展为二进制软件安全分析提供了新的可能性，如基于AI大模型的二进制代码语义理解、大模型辅助的漏洞挖掘、基于 AI Agent 技术的自动逆向分析工具等。课题旨在结合程序分析（反编译等）与人工智能（AI 大模型等）技术，解决威胁情报业务中海量二进制安全分析的业务需求。"", ""青云课题价值"": ""1、结合程序分析（反编译等）与人工智能（AI 大模型等）技术，解决威胁情报业务中海量二进制安全分析的业务需求。\n2、结合程序分析（反编译等）与人工智能（AI 大模型等）技术，也为客户以及行业提供逆向分析效率更高以及准确性更高的产品"", ""青云课题挑战"": ""1. 探索海量二进制文件的成分分析与Embedding语义比对技术，结合高效的样本家族分类与聚类检索算法，全面提升恶意软件识别与追溯能力；\n2. 二进制分析智能体构建，对恶意二进制文件进行分析，自动化地提取二进制代码的关键信息，识别潜在的恶意行为或漏洞。通过智能化的分析流程，提高逆向分析的效率和准确性。"", ""BG"": ""CSIG"", ""部门"": ""科恩实验室"", ""中心"": ""安全威胁分析运营中心""}"
探索AI在漏洞研究领域的应用,"{""青云课题背景"": ""漏洞研究能力是团队的核心竞争力，也是产品的技术壁垒。随着团队发展和产品迭代，团队对漏洞挖掘和利用的效率有了更高要求。在人工智能浪潮之下，团队需要更加积极地探索利用LLM技术提升团队的产出和效率，构建AI赋能的下一代漏洞研究技术体系。该体系将提升团队对CPU/GPU算力的利用率，为产品保持高水准和高竞争优势提供重要保障。"", ""青云课题价值"": ""1、随着攻击手段演进，传统防御失效，AI行为分析与异常检测将主导，通过大模型在安全领域的应用，系统可实时学习并更新策略；\n2、通过AI的应用，推动探索实现智能漏洞利用，提升漏洞发现和防御的能力。"", ""青云课题挑战"": ""1. 探索AI在不同模糊测试场景的应用。研发基于大语言模型的上下文感知样本生成技术，通过代码语义解析生成符合目标程序语法结构的初始样本；结合强化学习算法构建动态变异策略引擎，实时根据代码覆盖率反馈和崩溃模式分析优化变异权重分配；\n2. 探索AI在代码审计场景的应用。建立图神经网络赋能的跨语言代码审计系统，通过代码属性图（CPG）构建程序语义的全局表征模型，融合控制流、数据流及函数调用关系实现复杂漏洞模式的深度挖掘；\n3. 探索AI在漏洞分析和利用过程中的应用。聚焦二进制逆向和利用链构造等核心场景，包括而不限于智能逆向辅助、Gadget搜索优化和关键对象定位增强等。"", ""BG"": ""CSIG"", ""部门"": ""科恩实验室"", ""中心"": ""东风溯源产品中心""}"
量子算法及量子计算系统实现,"{""青云课题背景"": ""量子计算最主要的方向可以分为算法和硬件实现两个部分，腾讯量子实验室在持续增强硬件实现、量子纠错容错、量子算法设计和分析的基础能力，并积极研究量子算法、量子电路、量子模拟、量子AI等领域的基础理论及全栈量子系统中的技术问题；我们通过与腾讯内部团队、学术界及其他量子机构广泛开展合作来探索药物研发、材料设计等产业实际应用机遇，基于此背景，实验室期望寻找在量子算法/代数/量子物理/微纳加工/电子学/微波系统领域有研究或实践经历的同学，一起推进基础科技，探索行业价值。"", ""青云课题价值"": ""本课题聚焦量子计算核心方向，推动算法与硬件协同发展，助力突破量子纠错、系统集成与实际应用等方向难题，对实现量子计算实用化具有重要意义。"", ""青云课题挑战"": ""在理想情况下，量子计算理想需要融合物理的直觉和数学/计算机科学的严谨和深度，同时融合科研创新和软硬件结合的工程化时间。"", ""BG"": ""CSIG"", ""部门"": ""量子实验室"", ""中心"": ""超导硬件组""}"
生命科学大模型的研究与应用,"{""青云课题背景"": ""开发人工智能和大模型技术以解决生命科学问题，与腾讯云的医疗健康业务密切相关。该研究负责开发和应用先进的机器学习和大模型技术，以解决生命科学领域的复杂问题。该职位将涉及生物数据的处理、分析和建模，旨在推动生物医学研究和临床应用的发展。\n主要职责：\n1.大模型开发：设计、训练和优化用于生命科学数据分析的大规模机器学习模型。\n2.数据处理：处理和分析来自基因组学、蛋白质组学、代谢组学等领域的大规模生物数据。\n3.算法研究：研究和开发新的算法，以提高模型的性能和准确性。\n4.结果发布：撰写研究报告和学术论文，展示研究成果。"", ""青云课题价值"": ""近年来，生物医疗技术迅速发展，临床精准医学和生物研究产生了大量数据，这些数据需要更强大的AI算法来实现精准的诊断和分析。未来，大部分生物医疗数据将在云端进行存储和分析，研究生物医疗大模型将帮助我们建立技术储备和竞争壁垒，从而推动腾讯云医疗健康业务的发展。"", ""青云课题挑战"": ""生物医疗行业的特殊性决定了其对AI算法模型的准确性、鲁棒性和泛化性有着极高的要求。生物医疗数据的处理、分析和建模过程极为复杂，不仅需要卓越的AI算法开发能力，还需要深厚的生物医疗背景知识。因此，这一课题需要顶尖的跨领域人才参与。"", ""BG"": ""CSIG"", ""部门"": ""生命科学实验室"", ""中心"": ""科学大模型组""}"
基于大模型的拼音输入法长句预测,"{""青云课题背景"": ""我们正在寻找一位对大模型技术充满热情且具备创新能力的优秀人才加入输入法部算法团队，共同探索大模型在拼音输入法领域的革命性应用。投身基于大模型的长句预测优化项目，通过改造拼音转文字的大模型架构，设计高效的Tokenizer方案，并深度融合拼音信息、场景知识信息，实现长句预测高覆盖和高准确的双重突破。具体目标包括：\n\n1.模型架构创新：设计适合拼音输入的专用大模型结构，优化拼音信息注入方式，确保关键实体词和领域词的精准识别；\n\n2.训练策略优化：制定数据采样与训练方法，结合场景和意图信息进行强化学习，提升模型对用户表达意图的匹配度；\n\n3.端侧性能平衡：在保证高准确率的同时，优化推理效率，实现实时响应与低资源消耗；\n\n4.用户体验升级\n：\n在高触发率的基础上，实现高准确率的整句补全，并确保生成结果的风格与格式标准化。"", ""青云课题价值"": ""通过基础算法升级，实现下一代输入法体验的突破，对增强输入法的市场竞争力具备明确的战略价值，会成为未来各家输入法竞品共识的核心主路径之一。"", ""青云课题挑战"": ""此岗位是一次技术攻坚的机遇，也是定义下一代输入法核心竞争力的关键角色，期待你具备卓越的算法能力与创新思维。\n\n1. 技术复杂性：需在庞大拼音-汉字映射空间中实现高精度预测，同时兼顾用户个性化输入习惯的建模；\n\n2.数据与算法融合：面向输入法的跨平台、多场景的复杂度，构建覆盖多场景的训练数据集，并设计动态反馈机制，持续优化模型对用户行为的适应性；\n\n3.实时性与准确性平衡：在端侧部署中突破计算资源限制，确保模型响应速度与预测质量的双重达标；\n\n4.创新落地：将前沿大模型技术转化为实际产品功能，推动输入效率的颠覆性提升，提升输入法用户粘性和智能品牌口碑。"", ""BG"": ""CSIG"", ""部门"": ""输入法部"", ""中心"": ""智能输入技术中心""}"
基于大模型的搜索召回与排序,"{""青云课题背景"": ""我们正在寻找一位对大模型技术充满热情且具备创新能力的顶尖校招生，加入搜索应用部，探索RAG前沿技术的研究和应用落地。投身基于大模型的搜索召回和排序方向，通过query改写，LLM-based召回和排序优化、检索-生成协同优化等手段，实现RAG技术的创新突破和业务效果的显著提升。"", ""青云课题价值"": ""我们期望通过query改写、LLM-based召回和排序优化、检索-生成协同优化等手段，实现RAG技术的创新突破和业务效果的显著提升。"", ""青云课题挑战"": ""课题挑战：\n1. Query Rewriting：研究LLM-based的query改写技术，通过query拓展、query拆解，将用户复杂prompt改写为适配检索系统特性的query发起检索；\n2. Deep Retrieval：基于生成式召回、类Search-R1等召回技术，提升好结果的召回率；\n3. DeepRanking：研究LLM-based ranking、LLM Scaling Up 、Continued Pre-Training、Reasoning Ranking等方向，提升多目标排序效果；\n4. 检索-生成协同优化：协同优化query改写、召回排序和生成模型，提升RAG系统最终输出的效果和用户体验。职位要求：\n候选人大模型技术、NLP技术和机器学习技术基础扎实，精通LLM后训练技术（包括SFT和强化学习），精通多目标排序技术。"", ""BG"": ""CSIG"", ""部门"": ""搜索应用部"", ""中心"": ""搜索算法中心""}"
会议语音理解大模型方向研究,"{""青云课题背景"": ""1. 会议是一个连续、长时音频输入的场景，同时涉及多人对话、远场场景、文档分享等外部输入源。本研究旨在研发超长音频，超长上下人，多说话人，连续语流场景下，全面理解会议内容的大模型方案；更好的识别会议内容(semantic info)和富信息（paralinguistic info）；\n2. 实时In-Context Learning：充分利用会议的会前文档、会中屏幕文档共享、会议历史信息等，提升会议内容识别率，在识别错误时可以方便做Interactive Learning，通过和用户不断的交互，影响下一次的识别准确率。"", ""青云课题价值"": ""改善复杂场景下，利用会议各类周边信息，在长音频，多人讲话远场时，提升会议内容的识别准确率，并根据会议类型，提升会议总结，待办，话题等核心智能化输出任务的质量。"", ""青云课题挑战"": ""本职位承接腾讯会议会议实时字幕，会后流式和离线纪要，上传文件，录音笔，实时和离线多语种语音内容理解和翻译等工作。在当前已有模型下，需要进一步提升内容识别和理解质量，这里就需要针对会议的特点，从长度和内容的丰富度上，研发超长音频，超长上下文，多说话人，连续语流场景下，全面理解会议内容的大模型方案；同时，从可利用的信息上，充分利用会中会外各类多媒体信息，通过和用户不断的交互，提升会议场景内容理解的质量。"", ""BG"": ""CSIG"", ""部门"": ""腾讯会议产品部"", ""中心"": ""天籁实验室""}"
会议语音生成大模型方向研究,"{""青云课题背景"": ""会议是一个多人多源的实时对话场景，对于多说话人区分、理解和多语种翻译的能力均需要低延迟处理方案。本方向聚焦于端到端语音大模型的模型训练和推理工程化，优化多个输入模态之间的信息融合方案， 改进多个语种之间的理解能力，优化多语种翻译效果， 提升输出语音的自然度、准确度、情感表现力，提升会议对话中多人语音内容的分割、理解和总结能力。"", ""青云课题价值"": ""改善会议中端到端模型的输出能力， 提高对多人场景的区分和确认能力，增强流式输出语音的稳定性和速度。"", ""青云课题挑战"": ""端到端多模态大模型中不同模态数据对齐和信息融合困难，数据不均衡导致对低频模态的建模能力弱，输出语音的风格多样性和可控制性挑战较大，同时保持端到端模型的理解能力仍然是个挑战，长序列多模态数据带来的计算开销增加和实时性要求仍需优化。"", ""BG"": ""CSIG"", ""部门"": ""腾讯会议产品部"", ""中心"": ""天籁实验室""}"
后量子密码相关技术的研究与研发,"{""青云课题背景"": ""密码技术作为底层基础设施，广泛应用于包括密钥管理系统（KMS）、虚拟专用网络（VPN）、浏览器以及各类移动端与桌面端应用等场景。随着量子计算的发展，传统的公钥密码逐渐变得不再安全（目前相关单位对于量子计算机破解经典公钥密码的时间共识是2035年之前）。后量子密码（Post-Quantum Cryptography）是为应对量子计算时代潜在威胁而推出的新一代密码算法，用于替代或补充目前广泛使用的传统公钥加密方案。"", ""青云课题价值"": ""1. 通过后量子相关技术的研究与实践，推动后量子加密技术在我国的落地与应用，为我国网络空间安全体系的升级贡献力量。\n\n2. 探索腾讯云后量子迁移的最佳实践，为腾讯云在后量子时代的安全提供强有力的技术保障，确保云服务的在后量子时代领先优势与技术可靠性。\n\n3. 挖掘后量子时代全新的商业机会，为客户提供前沿、安全的服务解决方案，助力客户在本次技术革新中抢占先机。"", ""青云课题挑战"": ""课题挑战：\n本项目将研究和开发相关技术应对当前PQC落地面临的挑战：\n1. 标准和法规繁多，合规成本高；\n2. 密钥长度剧增（部分算法密钥较RSA增长20倍）；\n3. 密码资产错综复杂难以全面盘点；\n4. 密码迁移专业性太强，易引发业务中断与安全风险等挑战。职位要求：\n1. 密码学：熟悉后量子密码算法，具有密码学相关的研究经验，能够独立开展算法分析与优化工作；\n2. 编程与软件工程：拥有扎实的软件工程能力和丰富的实践经验，至少熟练掌握 C 和 Python 编程语言。熟悉软件开发流程及代码优化，具备良好的代码规范意识；\n3. 计算机基础：深厚的计算机科学基础，熟悉计算机体系结构、网络协议和操作系统，能够快速理解和解决跨领域的技术问题；\n4. 研究能力：具备优秀的学习能力和信息检索能力，能够高效阅读和理解英文技术文献。拥有良好的学术素养和团队协作精神，能推进技术研究并撰写高质量的技术文档。加分项\n1. 参与过后量子密码国际标准化的制定和分析工作；\n2. 参与过密码学相关的开源项目或发表过学术论文；\n3. 曾经发现过密码相关的严重漏洞。"", ""BG"": ""CSIG"", ""部门"": ""玄武实验室"", ""中心"": ""基础安全研究组""}"
AI搜索LLM算法研究,"{""青云课题背景"": ""1. 负责大语言模型的预训练、指令微调、强化学习等算法的研究，针对业务问题开展算法调优。\n2. 研究和实现搜索相关的NLP技术，包括但不限于意图识别、自动摘要、查询改写、RAG等，提升搜索结果的准确性。\n3. 定期收集产品反馈，建立合理的LLM评价机制，根据问题及时对算法架构和特定模型进行升级。\n4. 跟进NLP前沿技术，结合业务应用场景，提供创新性的解决方案。"", ""青云课题价值"": ""随着大模型技术的发展，人们获取海量信息的方式正在被重构。相对于传统的搜索引擎，AI搜索以其精准、直接、个性化等特点，受到越来越多的用户的关注。本课题聚焦于提升AI搜索整体质量，针对当前AI搜索的难点，探索对复杂用户输入的意图理解能力。针对搜索引文中信息繁杂多样，提升对AI搜索模型的自动辨别和总结能力。 此外针对部分用户对自身产品信息的要求，AI搜索结合RAG技术，提升在个性化场景下的信息输出能力。上述一系列问题的探索和解决，将显著提升AI搜索技术的用户体验。"", ""青云课题挑战"": ""1. 熟悉NLP领域的基础理论和主流大模型原理，具备大模型实战经验者优先。\n2. 在ACL、NAACL、EMNLP、TACL、NeurIPS、ICML、AAAI等学术会议或期刊发表过文章者优先。\n3. 熟练掌握Python编程，熟悉至少一种深度学习框架，如Pytorch、Tensorflow等。 有较强的代码能力优先，如ACM金牌、NOI银牌等，或代码开源产生了较大影响力。\n4. 对搜索场景的用户需求有深刻理解，具备强业务敏感性和技术前瞻性。\n5. 良好的自驱力，优秀的分析和解决问题的能力，对解决具有挑战性的问题充满激情。"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""伏羲研究中心""}"
人脸识别安全研究,"{""青云课题背景"": ""人工智能生成内容（AIGC）技术的突破性进展，显著提升了文本、图像及视频等多模态内容的生成效率与质量。然而，该技术在安全认证场景（如人脸识别、证件核验等）中的滥用风险日益凸显，伪造内容可能引发身份冒用、信息欺诈等安全隐患。现有检测技术普遍面临两大挑战：一是生成模型的快速迭代导致检测系统泛化能力不足；二是单模态分析方法难以应对跨模态伪造内容的复杂性。本研究旨在面向开放性的AIGC生成内容的检测技术研究，基于图像、频域、文本等多种模态信息，探索多模态模型、大模型方法，提升检测模型能力，推动该技术在多领域应用。"", ""青云课题价值"": ""突破当前AIGC检测方法指标，有效应对多种场景下的AIGC攻击，支持 人脸核身、AI防护盾、证件PS等业务场景，进一步提升安全性。"", ""青云课题挑战"": ""1、探究针对AIGC检测的多模态信息利用，如何设计有效融合图像、视频、频域、文本等信息的多模态模型，引导模型对生成特征的学习；\n2、探究生成技术间的关联和差异，生成和检测任务统一；如何关注GAN、Diffusion等技术在生成过程中引入的生成痕迹，以及探究基于生成技术进行AIGC检测的方法；\n3、探究大模型技术在AIGC检测中的应用，如何利用大模型知识，通过任务微调，提升模型能力和可解释性。"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""盘古研究中心""}"
多模态内容理解研究,"{""青云课题背景"": ""随着大语言模型（LLM）及多模态大模型（MLLM）技术的突破性发展，通用场景下的图文内容语义理解能力得到显著提升。然而，在开放业务场景中，现有技术仍面临两大核心挑战：\n1. 在复杂场景下，细粒度特征（如局部属性、跨模态语义关联等）的精准建模能力不足；\n2. 直接应用预训练大模型容易导致灾难性遗忘、幻觉输出和泛化能力欠缺等问题，难以满足业务需求。\n为此，本研究致力于面向复杂开放场景下的多模态内容理解，探索基于大模型架构的高效细粒度理解方案，突破模型在业务场景中的适配瓶颈，推动在多业务场景下的规模化落地。"", ""青云课题价值"": ""1. 提升内容安全、支付场景识别等业务案例准召水平，解决大模型适配的技术难点，支持业务大模型技术升级；\n\n2. 在多模态大模型关键方向攻坚（post-training、幻觉优化等），沉淀技术成果，提升优图在MLLM领域的技术影响力。"", ""青云课题挑战"": ""1. 优化MLLM在细粒度理解中的效果，研究如何设计跨模态细粒度对齐方法、优化视觉表征能力，实现更精准的语义理解与交互；\n2. 研究如何解决大模型在业务适配中的灾难性遗忘与泛化性损失等问题；探索基于DPO、GRPO等人类反馈对齐技术，降低模型幻觉；\n3. 研究模型结构优化，知识蒸馏等方法提升大模型推理效率，在满足适配效果同时降低推理成本。"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""盘古研究中心""}"
高性能大语言模型研究,"{""青云课题背景"": ""针对大尺寸大语言模型成本高、推理慢等弊端，探索通过剪枝、知识蒸馏、模型结构优化等预训练技术，训练效果优异的小尺寸大语言模型。"", ""青云课题价值"": ""- 直接赋能腾讯云/QQ浏览器/ima等核心业务，实现专业领域问答效率倍增\n- 显著降低大模型推理成本，突破产业落地算力瓶颈，推动AI普惠化\n- 攻关Transformer架构优化等前沿课题，产出突破性成果\n- 掌握从算法研发到亿级产品落地的全链条能力，开放专利/论文署名权\n- 提供国际学术交流、技术标准制定等高端发展通道"", ""青云课题挑战"": ""- 熟悉大模型的重点研究成果和开源项目，有较强学术经验者（如顶级会议/期刊第一作者或知名学术竞赛前三名）优先；\n- 较强的算法实现能力，熟练掌握 Python、C/C++ 编程，熟悉常用深度学习平台和算法框架；\n- 优秀的分析问题和解决问题的能力，对解决具有挑战性的问题充满激情，同时具备良好的团队合作精神；\n- 有LLM相关工作和项目经验的优先"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""神农研究中心""}"
多模态理解与交互研究,"{""青云课题背景"": ""针对图文、音频、视频混合场景进行多模态内容的感知、理解和交互输出，探索多模态视觉推理、视觉编码器设计、长视频交互理解等多模态大模型的技术方案和应用。"", ""青云课题价值"": ""- 研究成果可应用于需要图文多模态和视频全模态理解和交互的业务场景，包括腾讯云智能体开发平台、ima文档解析和问答、QQ浏览器视频Agent、OCR大模型等核心业务，并为更广泛的多模态业务需求做好技术储备和支撑\n- 课题跟进多模态大模型的前沿方向，比如多模态预训练、原生多模态模型、多模态推理等，结合业务场景探索创新解决方案，完成技术突破，构建业内领先的多模态大模型\n- 成果可通过开源、技术报告等方式向业界做价值输出，打造学术和品牌影响力"", ""青云课题挑战"": ""- 熟悉多模态大模型的重点研究成果和开源项目，有较强学术经验者（如顶级会议/期刊第一作者或知名学术竞赛前三名）优先；\n- 较强的算法实现能力，熟练掌握 Python编程，熟悉常用大模型深度学习平台和算法框架；\n- 优秀的分析问题和解决问题的能力，对解决具有挑战性的问题充满激情，同时具备良好的团队合作精神；\n- 有LLM相关工作和项目经验的优先"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""神农研究中心""}"
大语言模型Agent研究,"{""青云课题背景"": ""针对LLM-based Agent在复杂任务场景中的执行路径拆解与多agent协作问题，研究基于反思记忆的agent框架优化机制与基于强化学习的模型优化方法，推动Agent智能决策与环境互动在各领域的落地应用。要求熟悉大模型思考框架以及强化学习方法，有LLM Agent模型优化经验优先。"", ""青云课题价值"": ""- 研究成果可直接应用于腾讯核心业务场景，包括提升\""腾讯云智能体开发平台\""的复杂任务处理能力、优化\""QQ浏览器智能体\""的多轮交互效率、增强\""ima知识库问答\""的精准推理与协作能力，推动产品智能化升级。\n- 构建可复用的Agent优化框架，突破多步骤任务拆解与协作瓶颈，大幅提升复杂场景任务成功率；通过强化学习与反思记忆机制，探索LLM决策效率的SOTA方案。\n- 深度参与LLM Agent前沿技术探索，接触亿级用户规模的真实业务场景，获得顶尖团队导师资源与高性能算力支持，加速学术与工程能力双提升。"", ""青云课题挑战"": ""- 熟悉大模型以及Agent的重点研究成果和开源项目，有较强学术经验者（如顶级会议/期刊第一作者或知名学术竞赛前三名）优先；\n- 较强的算法实现能力，熟练掌握 Python编程，熟悉常用大模型深度学习平台和算法框架；\n- 优秀的分析问题和解决问题的能力，对解决具有挑战性的问题充满激情，同时具备良好的团队合作精神；\n- 有LLM或Agent相关工作和项目经验的优先"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""神农研究中心""}"
视频生成模型推理加速研究,"{""青云课题背景"": ""1. 负责视频生成模型的推理加速研究，包括但不限于模型蒸馏技术、模型结构优化、模型量化技术等，降低推理耗时及成本。\n2. 优化算法，以尽可能低的效果损失持续降低推理耗时，以满足核心业务的需求\n3. 跟进模型加速方向的前沿成果，结合实际业务需求，提出创新性的解决方案"", ""青云课题价值"": ""视频生成模型的加速研究是推动人工智能内容生产（AIGC）迈向工业级应用的核心技术方向，视频生成模型正重塑影视、游戏、广告等行业的生态格局。加速技术的突破将进一步释放创作者潜能，使其聚焦于创意设计而非重复性劳动。当前视频生成模型的算力需求是制约商业化的关键瓶颈，通过创新性加速技术，可显著优化计算资源利用率，降低用户的使用成本并提升用户的体验和生产效率。"", ""青云课题挑战"": ""1. 熟悉图像/视频生成领域的基础原理及主流生成模型，具备图像/视频生成经验或模型蒸馏/量化加速实战经验者优先。\n2. 在CVPR、ECCV、ICCV、neurons等学术会议或期刊发表过文章者优先。\n3. 熟练掌握Python编程,熟悉至少一种深度学习框架,如Pytorch、Tensorflow等。有较强的代码能力优先,如ACM金牌、NOI银牌等,或代码开源产生了较大影响力。\n4. 良好的自驱力,优秀的分析和解决问题的能力,对解决具有挑战性的问题充满激情。"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""轩辕研究中心""}"
高保真人像视频生成及应用研究,"{""青云课题背景"": ""1. 近年来视频生成领域取得了很大的进展，在数字人、泛娱乐、艺术创作等场景有很大的应用潜力，但是在人物结构合理性、细节质量、人与其他主体交互的合理性、时许稳定性等方面仍然面临许多挑战，限制了其应用落地。这个项目旨在提升人像视频生成模型的整体质量，并将其应用到下游任务中，比如多模态人像视频驱动、个性化人像适配生成、人与物交互等。\n2. 你将跟进前沿的生成技术，并参与到文生视频/图生视频等基础生成模型的设计、开发和优化过程中。针对人像视频这个特定领域，完善量化评估，建立benchmark。在此基础之上，将视频生成底模改造适配到实际的下游任务中，结合文本、音频、视觉等多种模态地控制信号，探索新技术在实际应用中的有效性和商业潜力。"", ""青云课题价值"": ""本课题聚焦于高保真人像视频的生成，针对当前视频生成领域在结构合理性、细节质量、人与物体交互以及时序稳定性等方面存在的技术瓶颈，致力于通过前沿生成模型的设计与优化，显著提升人像视频的真实感、细腻度和动态表现力，增强人与环境及物体的自然交互能力。课题紧跟多模态视频生成与编辑的最新技术进展，注重技术的实际适配与商业化潜力的挖掘。通过将底层生成模型与实际下游任务深度融合，推动前沿AI技术从实验室走向产业应用，助力数字内容生产、虚拟人、影视娱乐等行业的创新与升级。"", ""青云课题挑战"": ""1.熟悉视频生成领域的基础理论和主流大模型原理，具备大模型实践经验者优先。\n2.在CVRP、ICCV\\NeurIPS、ICML等学术会议或期刊发表过文章者优先。\n3.熟练掌握Python编程，熟悉至少一种深度学习框架，如Pytorch等。有较强的代码能力优先，如获得过ACM奖牌或代码开源产生了较大影响力。\n4.熟悉人脸人体相关的生成编辑任务，有实际项目经验的优先。\n5.良好的自驱力，优秀的分析和解决问题的能力，对解决具有挑战性的问题充满激情。"", ""BG"": ""CSIG"", ""部门"": ""优图实验室"", ""中心"": ""轩辕研究中心""}"
元宝Deep Research项目,"{""青云课题背景"": ""元宝作为腾讯的原生AI产品，覆盖广泛的用户请求，包括知识问答，文创，图创，解题等等。Deep Research项目承接用户复杂的请求任务，需要模型有较强的Planning、深度思考能力，并能调度多专家协同来解决不同的子任务。本项目主要通过定义清晰的奖励机制，实现多种Rule-Base和Model-Base奖励模型来驱动RL训练，做自动空间探索方式的模型能力优化，来提升复杂场景用户任务的解决能力。"", ""青云课题价值"": ""以自动空间探索的后训练技术为支撑，研究复杂任务的自主进化的训练范式，提升元宝整体产品体验"", ""青云课题挑战"": ""1. 具有扎实的后训练实践经验，Reward Model、RL经验丰富；\n2. 保持对前沿技术有敏感的嗅觉，并能思考解决业务做落地实践；\n3. 掌握端到端的多模态模型训练的技术栈；\n4. 具备扎实的LLM理论基础。"", ""BG"": ""CSIG"", ""部门"": ""元宝产品部"", ""中心"": ""策略产品中心""}"
语音和视频多模态大模型研究,"{""青云课题背景"": ""方向一：实时对话大模型的语音、音频和视频Encoder和Modality Adaptor的模型结构和微调FT技术研究，改善多模态token的表征能力以及和 LLM 文本 Embedding 的映射关系，避免实时对话大模型的降智问题。方向二：实时对话大模型的音频指令遵循IF和多轮对话中的历史相关信息的存储、检索和应用的记忆能力研究和评估。方向三：实时对话大模型的不同上下文情况下的回复风格和情感表现力的研究和评估，LLM 的Linguistic and semantic 知识，Speaker Embedding信息和Audio Decoder联合训练生成更加丰富的音频和语音输出。"", ""青云课题价值"": ""改进元宝中对话大模型的效果，提升其遵循指令能力、记忆能力和输出风格内容控制能力。"", ""青云课题挑战"": ""1. 具有语音识别、语音翻译、语音合成转换、说话人识别等方向的研究经验；\n2. 掌握端到端多模态大模型的训练和调优技术；\n3. 熟悉实时对话模型的流式处理实现方案和多种模态之间对齐的技术实现；\n4. 掌握改善模型遵循复杂指令能力的技术。"", ""BG"": ""CSIG"", ""部门"": ""元宝产品部"", ""中心"": ""多模态技术中心""}"
大数据AI平台能力关键技术,"{""青云课题背景"": ""在云计算+大模型双驱时代，现代大数据系统需要高效支持数据科学，模型训练和推理应用等场景，已经成为重要的AI Infra设施。而传统大数据系统的在应对AI场景工作负载时，越来越难以满足数据处理的性能要求，迫切需要吸引在湖仓格式存储格式，高性能计算引擎等方面有深入研究的人才，针对AI场景进行深入的优化。"", ""青云课题价值"": ""1.结合AI场景的负载特征，在存储格式和元数据管理引等方面持续创新，提升产品在性能，成本等方面的竞争力\n\n2.结合大数据负载，持续优化系统AIOPS能力，降低系统的维护成本"", ""青云课题挑战"": ""1.高性能存储格式：面向AI训练场景的高效数据存储格式设计，解决传统Parquet，OCR等在机器学习、大模型训练场景的流量放大问题，并支持结构化/非结构化形式的多模态数据管理和访问。\n2.ABD（AI+Big Data+Database）一体化引擎：集成结构化/无结构化数据的统一元数据管理，支持DATA和ML任务的统一解析、优化和调度执行，支持TP和AP，支持SQL for ML一体化处理，实现数据、特征和模型的统一治理。\n3.自治大数据系统：聚焦大规模在线复杂系统的自治核心技术突破，在自治系统框架、系统自配置（参数调优/视图推荐/索引推荐/key 推荐/混合调优等）、智能化 RBO/CBO/CHO优化器、智能分布式调度/执行引擎、Schema自演进（学习型索引/学习型数据组织）、智能存储、LLMOPS以及 SQLCopilot 等方向上进行探索和落地。"", ""BG"": ""CSIG"", ""部门"": ""云产品二部"", ""中心"": ""大数据基础产品中心""}"
大数据智能分析关键技术,"{""青云课题背景"": ""在GenAI时代，传统数据分析方式在快速变化，通过AI实现智能化与自动化的数据分析处理已经变成大数据产品的核心竞争力。在新时代的大数据智能应用中，提供准确高效的自动化数据分析处理能力是构建技术门槛的关键，其中Text2SQL、多模态语义理解、数据合规和隐私等是核心基础能力，需要吸收相关垂直算法领域的优秀研究人才加入。"", ""青云课题价值"": ""1.结合大数据领域知识持续增强模型能力，拉开与竞品在语言及数据在理解力方面的差距，构建智能化门槛；\n\n2.结合大数据场景的安全要求，系统化建立模型应用的安全保护机制。"", ""青云课题挑战"": ""1.TexT2SQL：单纯基于LLM的TexT2SQL无法处理复杂分析场景的SQL生成需求，在准确性和安全合规方面均存在不足，需结合业务推理增强，AI Trust Layer等技术，打造增强的并实现企业级可信的SQL生成能力；\n2.多模态语义理解：结合大数据业务领域知识，消除自然语言的歧义性，以及统一不同用户的语言口径，建立领域知识与数据逻辑抽象的桥梁，通过提高大数据智能应用的意图理解准确性，提升数据分析的准确度；\n3.Data Guardrails：基于通用大模型构建智能大数据应用，在数据隐私，数据安全合规方面均存在隐患，通过构建应用内置Guardrails能力，实现数据层，模型层的防护，同时确保生成内容符合行业和监管要求。"", ""BG"": ""CSIG"", ""部门"": ""云产品二部"", ""中心"": ""大数据基础产品中心""}"
LLM大模型高效部署方案,"{""青云课题背景"": ""1. 参与基于GPU的高性能计算（HPC）项目设计与开发，负责GPU芯片的底层性能优化与调优。\n2. 针对大模型推理、训练等场景，优化和扩展vLLM、SGLang等框架的核心模块，提升计算效率与资源利用率。\n3. 深入分析GPU硬件架构特性（如Tensor Core、显存带宽、通信机制等），设计并实现高性能算子与算法。\n4. 与算法训练团队协作，解决分布式推理下模型并行（Model Parallelism）、数据并行（Data Parallelism）等场景下的性能瓶颈问题。\n5. 探索前沿技术方向（如混合专家模型MoE、动态计算图优化等），推动AI工程化落地的效率提升。"", ""青云课题价值"": ""LLM大模型已成为当前AI发展的趋势，其高性能部署方案直接影响商业竞争力、用户体验与成本效益。在行业普遍陷入价格战的背景下，从源头进行人才布局，对提升业务效率与优化用工成本至关重要。"", ""青云课题挑战"": ""1. 精通CUDA/OpenCL等GPU编程语言，熟悉GPU芯片的底层优化技巧。\n2. 熟悉vLLM、SGLang等大模型推理框架，有实际性能调优经验（如KV Cache优化、动态批处理、Attention算子定制等）。\n3. 扎实的高性能计算基础，熟悉并行计算、内存优化、通信优化等技术。\n4. 熟练使用C/C++、Python，具备良好的算法设计与代码实现能力。"", ""BG"": ""CSIG"", ""部门"": ""云产品三部"", ""中心"": ""计算加速中心""}"
类似MoE的transformer网络架构优化,"{""青云课题背景"": ""训练scaling law已经放缓，推理时的test-time scaling凸显。推理速度、推理成本成为越来越重要的技术探索领域，对AI应用更是如此。\n\nLLM由于参数量大，降低计算所需参数是个非常有价值的命题。MoE已经在这个命题上实践出一些成果。期望基于此基础，探索出一种更高效的网络结构。"", ""青云课题价值"": ""1. 切实降低模型inference成本。在确保大模型推理性能不受影响的前提下，通过动态调整模型推理过程中激活的Transformer block模块，有效减少平均推理开销，从而实现降低计算成本的目标。\n2. 提升模型整体性能。基于PP、TP等技术，将大模型按block深度灵活分布于不同计算资源之上，并根据具体场景需求动态调整所使用的block数量。通过这种方式，实现计算资源的高效利用，进一步提升多个模型同时部署时的整体性能。"", ""青云课题挑战"": ""MoE、ResNet有效的网络优化方法的成果总数比较少，需要大量实验、探索大量细节，才能找到优化的线索。对知识掌握的深度和广度要求更高，同时需要较强的研究探索能力。"", ""BG"": ""CSIG"", ""部门"": ""云产品四部"", ""中心"": ""开发者产品中心""}"
音视频语音识别与画质增强方向研究,"{""青云课题背景"": ""在视频和语音成为信息传播主流的今天，内容质量直接影响用户体验与价值。视频领域面临着噪声、模糊、信息缺失等质量缺陷，传统修复方法在复杂场景下效果有限，亟需新一代AI技术突破。本项目聚焦于高质量视频生成模型、强时序一致性约束算法与多模态融合理解，研发高效高质的视频修复引擎、时空连贯性保障机制及多模态协同增强框架，全面提升视频清晰度、细节还原与真实性，助力构建智能化视频处理与增强平台。与此同时，语音识别（ASR）系统在多轮/多人对话、口音变化、背景噪声等复杂语音场景下，依然存在准确性和鲁棒性不足的问题。为此，本课题也将探索如何将大语言模型的优势融入ASR系统，提升其在实际应用中的表现和潜力。我们诚邀视频修复与生成、时序建模、多模态学习、语音识别与大模型融合等领域的优秀人才加入，共同推动AI驱动的音视频内容处理技术在影视制作、数字传播等行业的创新应用！"", ""青云课题价值"": ""依托多模态大模型的前沿技术，全面提升腾讯云音视频产品在视频画质增强、图像修复、质量评估及音频ASR等核心能力，不仅显著优化产品性能，更将驱动行业技术革新，持续巩固和提升腾讯云在音视频领域的市场竞争力与技术影响力。"", ""青云课题挑战"": ""课题挑战\n1. AIGC视觉生成模型的应用研究，在多模态图像和视频生成等方向进行前沿算法探索及模型训练； \n2. AIGC视觉生成模型轻量化调优算法的探索，在垂直业务中使用轻量数据释放大模型潜力；\n3. AIGC视觉领域相关算法在云视频业务各领域的落地，包括但不限于视频增强和超分、视频补全、风格迁移、人脸人体的生成和修复等；\n4.音频特征到文本特征纬度的有效对齐，如何利用多轮对话以及充分发挥长上下文理解能力，做好方案的选型；\n5. 对话领域高质量音频的稀缺程度，如何高效的获取、标注、清洗也是重要的挑战。职位要求\n1. 计算机、人工智能、数学等相关专业的全日制研究生及以上学历；\n2. 熟练掌握机器学习和深度学习的基本原理，熟悉常见的生成模型框架，包括GAN、VAE、VQGAN和Diffusion Model或者熟悉ASR语音识别、LLM模型项目；\n3. 具备 Python/C++ 的算法实现能力，有深度学习框架 PyTorch/TensorFlow 经验； \n4. 熟悉ControlNet、LoRA、Text Inversion等生成模型配套能力优先；\n5. 在CV领域顶会（CVPR、ICCV、ECCV、ICML、NeurIPS、ICLR等）发表过论文者更佳。"", ""BG"": ""CSIG"", ""部门"": ""云产品五部"", ""中心"": ""音视频平台产品中心""}"
高性能大模型训练/推理网络,"{""青云课题背景"": ""超大规模AI集群高性能网络项目，基于腾讯自研星脉网络2.0，打通端侧计算节点协同网侧交换机构建端到端高性能网络，提升GPU集群性能，应用在AI场景(大模型训练，推理)。腾讯云在该项目投入了集技术创新、产品开发和线上运营为一体的专业技术团队，并且在芯片、硬件、软件、集群网络算法等关键技术全栈自研，广泛应用在腾讯自研和腾讯公有云等业务AI应用场景。"", ""青云课题价值"": ""1. 不断提升大模型训练推理场景下的网络吞吐，降低网络延迟；\n2. 使用自研端网配合的拥塞控制算法解决云上GPU集群多租户，多逻辑集群，多种模型训推混跑等复杂网络环境下的拥塞问题；\n3. 通过多路径容灾，主动探测调度等技术解决网络故障和硬件故障对大模型训推的影响，大幅提高GPU集群的可用性。"", ""青云课题挑战"": ""课题挑战：\n1.高性能计算网络极致性能（带宽&时延），在大规模集群通信场景通过端侧协同网侧实现集群流量均匀性，充分发挥网络物理带宽；\n2.高性能计算网络云化部署弹性，故障迁移等能力；\n3.在网计算能力，深度结合机器学习框架，实现端网协同计算，降低大规模梯度累加时带来的网络拥塞，提升训练性能。职位要求：\n1.熟悉高性能网络相关技术，包括RDMA, IB, libfabric, MPI​​等；\n2.熟悉机器学习训练过程，特别是训练中的网络通信过程及相关技术和组件。包括NCCL, DP/PP/TP, Megatron等；\n3.追求极致性能，主动寻求突破。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""高性能网络产品中心""}"
分布式数据库 TDSQL 数据动态自适应调度,"{""青云课题背景"": ""目前 TDSQL 架构主要面向金融核心场景，在技术选型过程中侧重数据库性能和低延迟以及延迟抖动控制，数据在存储节点之间的分布与分片算法（Hash/Range/List）有较强的绑定关系来保证最大的数据局部性，扩缩容场景下动态平滑迁移和数据自适应调度上做出部分妥协，该项目尝试在当前架构下研究如何确保TDSQL在拥有高性能的同时拥有优秀的数据弹性。"", ""青云课题价值"": ""从业务价值上，该项目将探索在基于分片粒度的数据组织方式下，如何优化数据分布的空间局部性和时间局部性，从而突破系统动态性的限制，实现更灵活的数据组织，以应对SaaS、电商等存在数据倾斜的更广泛业务场景。此外，也需探索在数据存在冷热差异的情况下，如何利用外部存储进行offload，从而降低整体存储成本。\n\n此外，通过本项目的研究合作亦可提升腾讯云数据库在学术领域的影响力，提升自身的技术影响力和品牌效应。同时也希望借此建立与顶尖高校及实验室的合作桥梁，为双方长期合作奠定基础，吸引优秀人才、建立沟通渠道，为后续人才引进创造机会。"", ""青云课题挑战"": ""TDSQL现有的一体化分布式架构为追求极致的延迟稳定性和性能，采用了较大分片规模并维持较高的数据局部性，导致数据调度过程耗时较长，易引发系统延迟抖动，限制了系统的动态性。本项目旨在探索如何通过合理方式实现数据的弹性调度，使数据在不同存储节点间实现动态、自适应、平滑的迁移，对业务透明无感知且无影响。项目涉及的技术链路较长，从数据调度到计算层、存储层均需深入研究，确保在数据迁移过程中不影响数据查询结果的正确性，不破坏事务属性，控制迁移流量以避免对在线业务造成任何扰动，同时保证迁移速度。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""国产数据库产品中心""}"
数据库智能诊断推理大模型,"{""青云课题背景"": ""为腾讯云数据库研究具备智能诊断能力的推理大模型，模型具备成熟的数据库运维经验和诊断推理能力，并可针对腾讯云自身监控数据、日志、文档等，生成精准的诊断建议和运维操作，未来可支持全自动化智能诊断运维。"", ""青云课题价值"": ""通过构建数据库智能诊断推理大模型，显著提升腾讯云数据库运维的效率和智能化水准。\n其价值体现在三方面：\n1.​​降本增效​​：模型融合数据库领域知识与通用推理能力，自动分析监控数据、日志等多元信息，生成精准、可解释的诊断建议，减少人工排查时间，降低运维成本；\n2.​​技术突破​​：攻克领域知识与大模型结合的挑战，设计兼具通用性和专业性的架构，为数据库运维提供可落地的AI解决方案，推动行业智能化升级；\n3.​​业务扩展性​​：支持未来全自动化运维，增强腾讯云数据库服务的竞争力；其方法论可复用于其他云计算或企业级场景，形成技术壁垒与商业价值。\n该研究不仅解决实际运维痛点，更探索大模型在垂直领域的深度应用，具有学术与商业双重意义。"", ""青云课题挑战"": ""课题需要设计和构架一类兼顾通用推理能力与数据经验的推理大模型，可根据腾讯云数据库积累的各类结构化与非结构化数据，产生精准可解释的诊断推理结论，有效减轻运维、客服人员的负担。\n要求候选人推理大模型基础扎实，精通常见的推理大模型及关键技术和强化学习算法，精通推理大模型的训练方法、训练数据收集与构造、并行设计技巧等。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""数据库SaaS产品与技术平台中心""}"
基于表格多模态大模型的监控数据异常分析,"{""青云课题背景"": ""研究面向腾讯云数据库的通用监控数据分析大模型，依托表格多模态大模型技术，实现对结构化监控数据的直接分析与精准结论生成，助力异常风险快速定位。"", ""青云课题价值"": ""通过构建一个通用的表格多模态大模型，实现对腾讯云数据库监控数据的智能化分析。在融合结构化数据与多模态理解能力后，模型能够直接解析多样化的表结构和数据类型，自动识别异常模式并生成精准结论，显著提升运维效率。其价值体现在三方面：\n1.突破传统规则或单模态分析的局限，利用大模型的泛化能力适配不同监控场景；\n2.结合时序、数值、文本等多模态特征，挖掘数据深层关联，提升异常检测的准确性与可解释性；\n3.为数据库运维提供自动化决策支持，降低人工成本，快速响应潜在风险。研究成果将推动多模态大模型在结构化数据分析领域的落地，形成可复用的技术框架，不仅服务于腾讯云数据库的稳定性保障，未来还可扩展至金融、医疗等行业的表格数据处理场景，具有广阔的产业应用前景。"", ""青云课题挑战"": ""课题需要设计和构建一类适应性较强的表格多模态大模型，可兼容多种表结构和数据类型，且能从结构化数据中，生成有价值的分析内容。\n要求候选人大模型基础扎实，精通常见的多模态大模型及关键技术，精通多模态大模型的训练方法、训练数据收集与构造、并行设计技巧等。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""数据库SaaS产品与技术平台中心""}"
基于大模型异构分布式推理性能优化,"{""青云课题背景"": ""随着大模型技术的发展，部署模型的规模越来越大，相关服务的QPS也在不断增加，服务的任务种类也在​​不断增多且各不相同​​，例如文本摘要服务、对话服务、图片/视频生成服务、文本解释服务等。各种服务对推理引擎的挑战​​也​​各不相同。同样是长序列，文本摘要服务的输入Prompt通常很长，​​因此​​TTFT是关键；对话服务更关注TPOT；而图片/视频生成​​服务​​则更强调E2E Latency。即使是短序列，遇到长序列时也会产生P99问题。​​ 这些问题​​结合当前Prefill和Decode两阶段不同的计算特性，以及日益增大的模型体量与部署规模，​​给大模型推理引擎​​和​​服务带来了​​巨大​​挑战。本课题的目标是​​设计开发​​一款面向通用场景的​​分布式大模型推理引擎​​。"", ""青云课题价值"": ""1.参与大模型推理引擎项目的开发并取得成果，其成果的产出能够直接提升我们的大模型推理引擎技术的产品、技术竞争力，从而获得极致的成本优化，带来直接的经济收益；\n\n2.在参与公司、知名厂商的合作中，建立行业技术影响力与标杆项目，最终吸引企业用户使用腾讯云的产品；\n\n3.在AI浪潮中，参与AI重构移动互联网应用，推动行业深刻变革。"", ""青云课题挑战"": ""课题挑战：\n1.在通用场景下，面对不同长度和不同种类的大模型请求服务，满足各自的TTFT、TPOT及P99要求；\n2.在上述前提条件下，做到最大化资源利用率，将单Token的成本降到最低。职位要求：\n1. 熟悉大模型模型结构，包括但不限于Transformer，MHA/GQA/MQA/MLA，MOE，VIT，VAE等；\n2. 熟练使用C/C++、Python, 至少掌握Cuda/Cutlass/Triton中的一种；\n3.熟悉分布式推理集群部署及PD分离等不同架构模式；\n4. 熟悉高性能网络通信相关技术，包括但不限于RDMA、IB、NVSwitch、NVLink等；\n5. 熟悉常用的并行优化技术，包括但不限于 Tensor Parallelism、Sequence Parallelism、Expert Parallelism、Ulysses等；\n6. 熟悉常见的推理引擎和推理服务框架，包括但不限于vLLM、TensorRT-LLM、Nvidia-TritonServer、TGI等；\n7. 熟悉常见Kernel优化技术和量化技术，包括但不限于FlashAttention V1/V2、PageAttention、FlashDecoding、FlashInfer、GPTQ、AWQ、Marlin等。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""虚拟化产品中心""}"
腾讯云下一代虚拟化预研项目,"{""青云课题背景"": ""虚拟化团队负责腾讯云虚拟化宿主机内核以及虚拟化套件的开发与维护工作，支撑腾讯自研云平台、公有云及私有云场景下的虚拟化底层技术，支持大规模业务的稳定运营。下一代虚拟化平台的主要方向涵盖：\n1. 轻量虚拟化，重构 QEMU/KVM 架构，支持在高密服务器下的轻量级部署，减少虚拟化损耗；\n2. 可维护性提升，支持任何组件的热升级和热替换能力；\n3. HostOS 与 GuestOS 的协同优化，实现性能和资源消耗的极致优化。"", ""青云课题价值"": ""1. 腾讯云虚拟化平台有着深厚的技术积累与成熟的培养方案，有着资深的导师团队，能够让有想法，有基础的同学快速成为业务专家、技术专家，在云行业实现价值。\n2. 虚拟化团队与上下游有着紧密的联系和周期性的交流，紧跟业界软硬件发展，可以接触并且参与到相关的开发，影响未来云行业的发展方向。\n3. 虚拟化团队在AI、大模型时代，作为智算底座，也有大量、有前景的探索方向和优化方向，可以深度参与，提升虚拟化技术的整体竞争力。"", ""青云课题挑战"": ""课题挑战：\n1.虚拟化属于计算机工程范畴里复杂性最大的软件程序之一，对操作系统的开发、维护需要足够的熟悉与全局化思维；\n2.底层硬件不断地发展，单机规格越来越大，进而带来对虚拟化和操作系统扩展性的挑战，底层软件需要和CPU体系结构深度优化，解决扩展性和稳定性的问题。职位要求：\n1.熟悉虚拟化相关技术，熟悉以下模块中的一个或者多个：KVM、调度器、内存管理、网络、存储(有开源贡献、实际代码开发项目经验优先)；\n2.了解/熟悉Linux系统下通用的问题分析工具链使用，如eBPF，perf，ftrace，kprobe等；\n3.熟悉x86/ARM CPU架构或者异构硬件架构，有相关的调研、调优经验；\n4.分析问题有专研精神，做到刨根问底。解决问题有长远意识，底层组件需要做到足够优雅。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""虚拟化产品中心""}"
腾讯自研操作系统（Tencent OS）项目,"{""青云课题背景"": ""随着芯片技术的发展，单台服务器的核心规模不断增加。如何保证操作系统和硬件体系结构的扩展性、性能、稳定性成为一个重要的研究方向。该课题聚焦在软硬件结合领域，解决几方面的问题：\n1. 操作系统本身在多核架构的扩展性问题，保证性能的水平扩展\n2. 解决大核心的稳定性问题，调研软硬件结合领域，研究CPU计算核心、Cache、内存、PCIE、GPU等领域RAS业界前沿技术，提升整体的稳定性"", ""青云课题价值"": ""1.参与TencentOS Server操作系统的开发，特别是在内核层面针对云、数据库、容器场景下各种性能优化，其成果的产出能够直接应用到腾讯相关产品与技术竞争力的提升。\n2.在参与公司、知名厂商的合作中，建立行业技术影响力与标杆项目，最终吸引企业用户使用腾讯云的产品；\n3.在云原生和AI趋势下，打造TencentOS Server作为云原生基础设施和AI基础设施\n4.在开源社区提出有创意的技术方案，提升TencentOS产品在操作系统领域的影响力。"", ""青云课题挑战"": ""课题挑战：\n1.操作系统属于计算机工程范畴里复杂性最大的软件程序之一，对操作系统的开发、维护需要足够的熟悉与全局化思维；\n2.底层硬件不断地发展，单机规格越来越大。进而带来对操作系统扩展性的挑战，底层软件需要和CPU体系结构深度优化，解决扩展性和稳定性的问题。职位要求：\n1.熟悉Linux操作系统内核实现，熟悉以下模块中的一个或者多个：kvm、调度器、内存管理、网络、存储(有开源贡献、实际代码开发项目经验的优先)；\n2.了解/熟悉Linux系统下通用的问题分析工具链使用，如ebpf，perf，ftrace，kprobe等；\n3.深入理解x86/arm cpu架构或者异构硬件架构，有相关的调研、调优经验；\n4.分析问题有专研精神，做到刨根问底。解决问题有长远意识，底层组件需要做到足够优雅。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""虚拟化产品中心""}"
云原生Serverless数据库智能弹性算法研究,"{""青云课题背景"": ""腾讯云数据库团队聚焦云原生与Serverless技术融合，致力于打造下一代智能弹性数据库TDSQL-C。该岗位聚焦于Serverless架构内核研发，解决智能弹性伸缩、AI驱动的性能稳定性与成本优化的核心挑战，推动数据库服务向极致弹性，自动运维的终极形态演进。"", ""青云课题价值"": ""TDSQL-C Serverless数据库主要解决传统数据库在云原生场景下的资源管理、成本效率与运维复杂度三大核心问题:\n1. 实现实例资源自动弹性，同时满足负载均衡和性能保障；\n2. 实现按需付费，资源利用率最大化；\n3. 隐藏底层节点配置、备份容灾、版本升级等运维操作，内置部分自愈与自治能力，大大降低运维复杂度。"", ""青云课题挑战"": ""1. Serverless架构要求资源能够根据负载动态扩缩容，但突发流量的快速响应与资源预测的精准性之间存在矛盾，如何结合时序预测（如LSTM/Transformer）与强化学习（RL），实现​​亚秒级扩缩容决策​​，同时避免过度扩容导致的成本浪费；\n2. Serverless的弹性机制可能引入性能波动，影响高并发事务的稳定性，如何实现端到端的AI驱动的性能稳定性控制​；\n3. 如何在Serverless架构下兼顾弹性效率与性能稳定性，支撑行列一体引擎、HTAP混合负载等复杂场景的工业化落地。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""云原生数据库产品中心""}"
分布式推理框架的传输优化,"{""青云课题背景"": ""基于​​Transformer​​的大模型推理架构下，KV Cache和PD 分离是两项非常关键的优化技术，Prefill和Decode阶段对GPU有不同的能力诉求，非常适合用异构算力分别承载。而作为推理阶段的核心数据结构KV Cache，如何在Prefill节点和Decode节点间高效扭转， 对推理的性能和效率有着巨大的影响。我们着力于研究KV Cache在分布式推理集群中的高效复用、传输及调度，提升分布式推理的性能，降低推理成本，助力推理服务的大众普惠。"", ""青云课题价值"": ""1. 解决PD分离架构下KV Cache的传输效率问题，提升推理服务的性能；\n2. 解决集合通信动态改变通信组的问题，为故障/动态调度场景提供更灵活的集合通信服务，提升分布式推理服务的灵活性。"", ""青云课题挑战"": ""课题挑战：\n1.KV Cache亲和的调度及高性能传输：高效的调度算法，提升KV Cache的本地复用率， 减少KV Cache的计算量； 更高效的KV Cache传输设计，完全消除传输时延对推理性能的影响；\n2. 动态改变通信组的集合通信能力：动态改变集合通信的拓扑， 单节点故障/动态调度对推理服务完全无影响。职位要求：\n1. 熟悉 RDMA 等高性能网络协议的原理及使用方法， 对性能有极致的追求；\n2. 熟悉集合通信的原理及常用的集合通信软件NCCL、DeepEP等；\n3. 熟悉推理服务的整个流程， 熟悉常用的推理框架软件，譬如SGLang、vLLM；\n4. 思维敏捷，追求突破，不拘常规。"", ""BG"": ""CSIG"", ""部门"": ""云产品一部"", ""中心"": ""IaaS前沿技术组""}"
基于多模态大模型的创意/内容理解,"{""青云课题背景"": ""1.腾讯广告系统目前每天新增千万级创意，需要基于创意的文案、图片、视频等组件信息等多模态的信息中实时提取商品、美感、人物、场景等标签信息及隐式Embedding，作为模型和策略的数据输入来提升广告推荐效果\n2. 多模态大模型作为当前主流技术已经应用于创意/内容理解的大部分场景，需要持续基于COT，RL等方式持续提升多模态大模型的效果同时保证线上实时infer的效率"", ""青云课题价值"": ""腾讯广告系统目前每天新增千万级创意，需要基于创意的文案、图片、视频等组件信息等多模态的信息中实时提取商品、美感、人物、场景等标签信息及隐式Embedding，作为模型和策略的数据输入来提升广告推荐效果"", ""青云课题挑战"": ""1. 需要基于深入理解多模态大模型的post-training技术，兼顾效率和效果持续提升创意/内容理解的水平"", ""BG"": ""CDG"", ""部门"": ""商业数据部"", ""中心"": ""数据挖掘中心""}"
广告全场景基础大模型,"{""青云课题背景"": ""课题背景：\n1. 腾讯广告流量丰富多样（微信、QQ/新闻/视频、联盟等），期望聚合全流量场景数据，构建广告域全场景基础大模型，为下游任务注入全场景的用户和广告表征信息，提升用户-广告匹配效率，改善单场景新小广告冷启动问题，提升广告推荐精准度"", ""青云课题价值"": ""1、技术上：广告推荐模型预估准确性和泛化性\n\n2、业务上：提升个性化推荐效果，提升用户体验和商业变现效率"", ""青云课题挑战"": ""课题挑战：\n\n1. 不同流量场景下，广告形态、用户分布、行为类型差异巨大，融合建模存在严重的负迁移效应，探索生成式、大规模稀疏MOE等技术在多场景多任务建模的应用，避免跷跷板问题；\n\n2. 如何在统一大模型中，输出用户多兴趣表征以及广告多粒度表征，供下游模型灵活应用"", ""BG"": ""CDG"", ""部门"": ""商业推荐部"", ""中心"": ""模型算法中心""}"
全域兴趣增强模型,"{""青云课题背景"": ""课题背景：\n1. 用户-广告行为数据相对稀疏，已有模型基于后验数据学习，易导致信息茧房、马太效应，期望借助 LLM 大模型能力，建模全域兴趣增强模型，为广告推荐模型注入世界知识，实现 “破圈”"", ""青云课题价值"": ""1、技术上：广告推荐模型预估准确性和泛化性\n2、业务上：提升个性化推荐效果，提升用户体验和商业变现效率"", ""青云课题挑战"": ""课题挑战：\n1. 广告推荐模型通常为 ID-Base 稀疏大模型，如何将 LLM 多模态理解以及世界知识有效注入推荐模型，训练出具有强推理推荐效果的用户兴趣模型\n2. 探索利用 AI 大模型合成样本，以及大模型作为 backbone 等前沿方向，提升广告个性化推荐效果"", ""BG"": ""CDG"", ""部门"": ""商业推荐部"", ""中心"": ""模型算法中心""}"
广告生成式预训练大模型,"{""青云课题背景"": ""课题背景：\n1，广告推荐模型面临样本稀疏、跨流量跨场景迁移挑战大、线上低延时要求复杂模型上限有限等挑战，期望基于融合跨场景、跨域的数据，构建万亿级别近线的超大规模预训练模型，广泛应用于广告的下游模型，提升广告推荐的效果，同时降低建设多套用户理解模型的成本。\n2，引入世界知识，使用生成式的训练范式注入广告域知识，提高模型知识上限，并具备广告域的专业能力。"", ""青云课题价值"": ""基于推荐域数据为核心的外部信息，构建完善数据集，提高模型知识上限。训练广告生成式预训练大模型，融入世界知识，建立内容/商品/广告间的常识性关系。"", ""青云课题挑战"": ""课题挑战：\n1，如何融合跨域、跨场景的数据，在统一的预训练大模型完成建模，使得模型可以既可以建模用户的长期兴趣，也可以覆盖用户的短期兴趣。\n2，如何将广告知识注入世界知识后，既能保留生成式模型的世界知识，也能改善广告域的专业能力。"", ""BG"": ""CDG"", ""部门"": ""商业推荐部"", ""中心"": ""AI 推荐基础中心""}"
广告生成式推荐,"{""青云课题背景"": ""课题背景：\n1.突破广告推荐算法领域传统广告召回/排序模式，结合大模型世界知识和广告领域知识，持续提升其在广告推荐场景下对用户行为语义理解和生成能力，在广告场景下建设落地基于大模型的生成式召回/排序算法能力。并引入强化学习框架，提升广告推荐的有效性。\n2.基于用户在广告、内容域的序列行为建模，研究从0开始训练的id-based生成式推荐大模型，应用于广告推荐全链路的召回、排序，显著提升广告推荐效果。"", ""青云课题价值"": ""通过广告交互数据精调预训练大模型既有常识又懂广告推荐，建立生成式推荐范式。"", ""青云课题挑战"": ""课题挑战：\n1，大模型在广告推荐，如何对齐文本语义与推荐语义；广告库动态变化，大模型如何感知并生成精准的推荐结果；大模型的推理速度制约了在需要快速响应场景的应用方式，如何通过算法与工程的联合设计，来加速大模型的推理过程。\n2，生成式序列建模叠加用户的长行为序列，对于对于广告的低延时场景应用有挑战，如何优化推理性能；如何建模用户跨域行为、用户基础属性等异构序列，从而能全面的捕获用户的长期兴趣与实时兴趣。"", ""BG"": ""CDG"", ""部门"": ""商业推荐部"", ""中心"": ""AI 推荐基础中心""}"
广告生成式CoT推理,"{""青云课题背景"": ""课题背景：\n1，基于广告后验数据的统计值拟合模型，缺乏推理和探索能力，依赖很多策略规则提升体验。该课题在广告推荐场景中，显式引入推理思维链，提升模型常识推理和自我检查能力，推荐出更合理的广告。\n2，模型的CoT推理能力需要较高的时延，该课题研究如何平衡CoT的时延与效果，使得在广告推荐的低时延限制下发挥CoT的能力，有针对性的解决业务问题，包括但不限于提升推荐结果的常识性、突破茧房效应。"", ""青云课题价值"": ""通过RL使模型形成CoT思维链增强模型推荐逻辑，使得用户体验更合理"", ""青云课题挑战"": ""课题挑战：\n1，在广告推荐领域构建CoT推理思维链属于业界的空白，该课题需要将LLM领域的CoT思想与广告推荐的场景创新性的结合，大胆突破。\n2，CoT思维链的效果与推理可应用的时长有强相关性，如何做到算法与平台的Co-Design，在广告真实业务场景时延可接受的情况下，落地CoT思维链。"", ""BG"": ""CDG"", ""部门"": ""商业推荐部"", ""中心"": ""AI 推荐基础中心""}"
基于大模型的广告数据智能分析及DI平台构建（Data Intelligence）,"{""青云课题背景"": ""广告业务数据量庞大且场景复杂，传统分析方法及BI工具门槛高、效率低。大模型技术的突破（如NL2SQL、CoT、多模态数据理解等）为数据智能分析提供了新的可能性。"", ""青云课题价值"": ""​​1、实现零门槛取数：通过NL2SQL技术将自然语言转化为精准查询语句，减少技术依赖，提升业务人员自助取数效率。\n2、场景化分析能力沉淀​​：抽象广告核心分析场景，沉淀分析方法论知识库，通过大模型落地场景化分析Agent。\n3、数据智能体构建​​：打通经分工具平台核心链路（如看板生成、监控告警等），实现从传统BI到DI的跨越。"", ""青云课题挑战"": ""1、在严肃的数据分析场景中，严控大模型幻觉，提高NL2SQL的准确性。\n2、广告的业务场景及分析方法复杂度高，需要大模型对业务和数据有更深的理解，从而提高CoT的可靠性。\n3、BI功能模块众多，基于大模型的全链路打通需要攻坚大量工程问题，对大模型的多模态理解能力也提出了极高的要求。"", ""BG"": ""CDG"", ""部门"": ""商业运管部"", ""中心"": ""经营数据应用中心""}"
基于多模态大模型进行广告素材违规识别,"{""青云课题背景"": ""腾讯广告审核需要识别广告法和平台流量媒体关注的多种违规情况，保障平台安全和用户体验。广告素材涉及多种模态（文本/图片/视频），面对规则的调整、广告主行为的变化，如何高质高效的建设并迭代多模态模型能力识别违规，是一项非常棘手且极具挑战性的任务"", ""青云课题价值"": ""1. 技术上：探索大模型技术在大规模广告素材识别场景的落地与应用。\n2. 业务上：识别广告场景违规点，保障广告业务健康持续发展。"", ""青云课题挑战"": ""1. 借助大模型技术准确地识别广告场景的各种违规点（如低俗、夸大、恶心不适等）；\n2. 结合大模型技术的演进趋势，思考在广告审核业务的落地场景；\n3. 通过持续学习应对广告主行为的快速变化；"", ""BG"": ""CDG"", ""部门"": ""商业运管部"", ""中心"": ""审核与效率研发中心""}"
【课题名称】妙思AIGC商业内容生成研究与应用,"{""青云课题背景"": ""【课题背景】\n在商业营销领域，AIGC技术正引发生产力革命，妙思致力于构建智能商业内容平台，围绕多模态内容生成、跨行业营销逻辑解构，通过可观的行业素材库与跨模态生成模型，共同探索多模态大模型内容生成、营销策略强化学习建模等前沿方向，重塑商业营销内容生产范式。"", ""青云课题价值"": ""解决多行业用户零门槛快速生成高质量商业内容并高效分发的难题，降低创意制作门槛，提升营销效率。"", ""青云课题挑战"": ""【课题挑战】：\n1 、跨行业营销逻辑解构与重组技术：针对电商、汽车、金融等若干垂直领域，需建立动态知识图谱与营销策略解构模型。通过深度行业语料挖掘，构建包含商品卖点、用户心理、转化路径的营销原子库，实现从行业特征识别到创意元素智能组合的闭环。\n2 、多模态生成技术：突破现有单模态生成局限，研发多模态引擎，重点攻克图文、视频、3D等跨模态技术。\n3 、智能混剪技术：研发具备营销效果预判能力的混剪算法。通过实时捕捉社交媒体热点，结合转化率预测模型，实现素材的智能重组。"", ""BG"": ""CDG"", ""部门"": ""商业AI部"", ""中心"": ""智能生成算法中心""}"
【课题名称】妙问Agent商业营销助手研究与应用,"{""青云课题背景"": ""【课题背景】\n在数字经济时代，企业营销面临数据孤岛、渠道碎片化与用户需求动态化的三重挑战。传统营销工具依赖人工经验驱动，存在响应滞后、策略同质化、长尾需求覆盖不足等痛点。本课题致力于研发具备自主决策能力的AI Agent系统，通过多模态感知、动态策略生成与跨平台执行能力，构建覆盖市场洞察-用户触达-效果追踪的全流程智能营销闭环。"", ""青云课题价值"": ""基于AI Agent技术，自动化完成分析、触达、生成及效果优化全流程，提升营销效率与精准度，实现个性化智能营销服务闭环。"", ""青云课题挑战"": ""【课题挑战】\n1 、多智能体协同架构，实现市场环境感知、用户意图解析与投放策略制定的分层决策。\n2 、基于深度强化学习的动态优化引擎，支持跨渠道策略调优。\n3 、生成式AI驱动的个性化内容工厂，融合用户行为轨迹与企业营销目标，自动辅助营销闭环。\n4 、自主进化机制，通过在线学习持续迭代用户需求与响应机制。 \n5 、项目技术挑战涉及跨平台数据融合、异构信息表征学习、营销决策因果推断等前沿领域。"", ""BG"": ""CDG"", ""部门"": ""商业AI部"", ""中心"": ""智能应用算法中心""}"
【课题名称】AI驱动的高真数字人直播研究与应用,"{""青云课题背景"": ""【课题背景】\n直播电商市场规模超万亿，但传统模式面临主播人力成本高昂、直播时长受限、内容同质化严重等痛点。基于此，我们聚焦研发新一代高真数字人直播解决方案，通过多模态生成、实时决策算法与高效算力调度技术，打造低成本、高转化、可7*24小时运行的AI直播系统，重构电商交互范式。"", ""青云课题价值"": ""解决传统直播电商高成本、人力及时间限制，通过高真数字人和智能互动实现低成本24/7实时带货服务。"", ""青云课题挑战"": ""【课题挑战】1 、高保真数字人实时生成：需突破3D神经渲染、超写实表情驱动与跨模态动作同步技术，实现毫米级唇形匹配与微表情控制，确保虚拟主播的拟真度与感染力。\n2 、动态场景化话术生成：构建多层级知识图谱与商品特征向量空间，研发基于用户行为实时反馈的强化学习对话模型，实现话术内容与商品卖点、用户画像、互动场景的三维匹配，支持细分行业的个性化内容生成。\n3 、低延迟交互决策引擎：设计多模态信号融合架构，整合视觉关注度分析、语义意图识别与情感计算模块，开发轻量化决策树与联邦学习框架，快速完成从用户提问到数字人表情-语音-动作的端到端响应。"", ""BG"": ""CDG"", ""部门"": ""商业AI部"", ""中心"": ""智能应用算法中心""}"
金融推理大模型指令遵循能力提升与场景自进化学习,"{""青云课题背景"": ""1.要求高：金融场景（如投研、投顾、风险等）面临着复杂、动态变化的数据环境和严谨、专业的合规要求，其决策高度依赖专业、低幻觉的金融推理能力\n2.基座弱：大语言模型（LLM）尤其是以R1为代表的推理大模型在NLP任务中展现出强大的潜力，但在金融场景中面临指令遵循差、金融专业知识不足、幻觉高的挑战\n3.应用难：受制于基座能力的不足，基于通用LLM构建的金融Agent也面临专业度低、合规要求难以满足、无法自我进化实现动态环境自适应和协作效率低等一系列问题。"", ""青云课题价值"": ""1. 技术上：设计指强化学习算法，打造业界一流金融推理大模型，设计高效自进化算法提升动态变化金融环境适应能力，探索金融场景最优的多Agent协作范式\n2. 业务上：打造最佳效果和最优体验的的金融智能体应用，提供金融用户的统一、有温度的专业体验"", ""青云课题挑战"": ""1、推理大模型推理能力强但指令遵循能力不足、幻觉率高，过于强调指令遵循性又会削弱其推理效果。需探索金融知识的高效注入方式（如CPT或post-training阶段）并设计指令跟随约束强化学习算法，在降低推理幻觉、保持推理灵活性的同时提升其指令遵循能力，满足金融场景严格约束的需求\n2、基于LLM的Agent在复杂多变的金融环境中存在调整速度慢、协作效率低的问题。需探索Agent高效自进化算法，通过持续学习满足场景高动态性要求，并通过金融多Agent协作范式的设计优化满足高准确性的场景应用需求"", ""BG"": ""CDG"", ""部门"": ""支付金融平台与数据部"", ""中心"": ""智能平台中心""}"
金融投资Agent,"{""青云课题背景"": ""课题背景：\n1. 金融市场是一个复杂、动态且充满不确定性的环境。传统的投资策略往往依赖于人类经验和直觉，容易受到情绪和认知偏差的影响。投资者在面临复杂的市场时，需要处理来自各种来源的大量信息并迅速做出决策，这对投资者的专业知识、经验和心理素质都提出了很高的要求。为了更理性、更精准地做出投资决策，投资者和金融机构需要借助更先进的工具和技术7*24小时为其服务。"", ""青云课题价值"": ""1.技术上：Agent链路上的模型训练。\n2.业务上：打造要给投资顾问Agent。"", ""青云课题挑战"": ""1 、Agent中的规划、工具调用、生成等模型优化。\n2、多模态的输入输出。\n3、解决模型幻觉问题，如张冠李戴、无中生有等。"", ""BG"": ""CDG"", ""部门"": ""FiT-支付金融平台与数据部"", ""中心"": ""智能平台中心""}"
游戏场景内的三维生成,"{""青云课题背景"": ""该课题旨在探索和优化将自然语言或图像输入高效转化为可用于游戏场景的结构化三维模型的能力，推动三维生成技术在实际游戏开发流程和玩法拓展中的落地应用。"", ""青云课题价值"": ""1. 大幅降低三维资产生产门槛与成本\n2. 赋能玩家内容创作\n3. 加速游戏原型设计与迭代"", ""青云课题挑战"": ""1. 生成的网格存在面数冗余、拓扑混乱、结构不规则等问题，不利于后续优化\n2. 缺乏对模型语义结构的识别与分割，导致后续调整难以进行\n3. 对用户输入的响应不够精准，难以支持细粒度控制"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
多模态模型在游戏场景中的应用,"{""青云课题背景"": ""近年来视觉语言模型展现出了非常强的理解能力，包含自回归类和扩散类的生成式模型展现出比较强的生成能力。同时，近期统一理解和生成的多模态模型，为游戏场景中的应用提供了全新范式和可能性。"", ""青云课题价值"": ""1. 通过理解玩家的多模态输入，实现更自然的交互方式\n2. 基于多模态数据生成高质量的游戏资产提高内容创作效率\n3. 跨模态生成和理解，如从玩家的文本描述生成多种模态的关卡设计"", ""青云课题挑战"": ""1. 收集和标注高质量数据集是一大挑战\n2. 如何提升模型在计算资源有限情况下的部署和训练效率存在挑战\n3. 游戏场景内实时性要求极高，对模型推理速度提出了很大挑战"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
基于大规模三维模型的通用物体自动绑定与结构理解,"{""青云课题背景"": ""近年来，三维大模型在跨类别三维理解与生成方面展现出强大能力，为通用物体的结构推理与自动绑定提供了全新范式。"", ""青云课题价值"": ""1.大模型与自动绑定融合，对任意物体的结构进行推理与自动绑定\n2.支持静物、机械、器具等非生物体的自动绑定\n3.多模态知识驱动结构理解与绑定优化，提升对复杂物体的语义适应能力"", ""青云课题挑战"": ""静物、器具、机械等在几何结构、拓扑关系、功能部件等方面高度多样，缺乏统一的骨架或绑定范式。相比人体、动物等生物体，静物的运动方式、结构层级和功能语义更加复杂且不规则，如何自动推断出合理的绑定结构极具挑战"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
深度学习/强化学习算法在游戏场景中的应用,"{""青云课题背景"": ""传统的游戏智能体技术在面对复杂和动态的游戏环境时，往往显得力不从心。深度学习和强化学习能够处理大量的数据并从中提取复杂的模式，能够模拟更真实的人类行为和进行智能决策。"", ""青云课题价值"": ""1. 提高游戏中的智能体的智能水平，能够适应玩家的策略变化并进行更复杂的交互\n2. 增强玩家沉浸感\n3. 创新游戏设计"", ""青云课题挑战"": ""1. 游戏往往具有复杂的规则和多变的场景，需要处理多样化的情况并进行适应性学习\n2. 玩家的行为通常是不可预测的，需要具备灵活性和适应能力，以应对玩家的各种策略和风格\n3. 需要大量计算资源和时间，探索更高效的算法，减少训练时间和资源消耗，同时保持模型性能"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
大语言模型在游戏场景的应用,"{""青云课题背景"": ""传统游戏NPC依赖大量的人工干预，在复杂场景下表现受限，难以满足玩家对高智能、沉浸式交互的需求。大语言模型具备远超传统模型的语言理解与生成能力，为游戏领域带来新的变革机遇。"", ""青云课题价值"": ""1. 优化游戏对局内外的智能对话体验\n2. 为游戏内的 NPC 提供自主决策和行为能力，增强游戏的真实感\n3. 助力游戏剧情创作及动态任务生成等场景，从多方面提升游戏品质与玩家体验"", ""青云课题挑战"": ""1. 如何让大语言模型的输出兼具逻辑性与趣味性、如何控制模型的知识边界\n2. 如何让模型深刻理解游戏，在不同游戏之间具有一定的泛化性\n3. 经过语音识别的对话文本通常具有较多的噪声，如何根据上下文准确理解玩家真实意图并做出回答"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
游戏场景下的语音技术研究,"{""青云课题背景"": ""快速、准确的语音识别及高效、生动的语音合成技术可以极大地提升玩家-玩家、玩家-NPC之间的交互体验。近年来，基于大语言模型的语音技术不断涌现，使得游戏中的语音体验革新成为可能。"", ""青云课题价值"": ""1. 快速、准确的语音识别\n2. 拟人的语言合成技术\n3. 文本-语音端到端多模态大模型"", ""青云课题挑战"": ""1. 实际场景中的实时语音噪声多、连贯性差、口语化特征明显、热词更新快\n2. 游戏场景内的角色众多，如何快速合成符合角色设定的语音\n3. 语音交互的时延要低，如何训练轻量化、高表现力的端到端语音对话模型"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
基于大模型的生成式推荐在游戏中的应用,"{""青云课题背景"": ""玩家对游戏内个性化内容的诉求日益加深，传统的深度学习推荐系统指标提升越发困难，需要探索全新的生成式推荐框架，进一步提升游戏的核心指标转化，优化玩家在对局、社交、内容消费中的个性化体验。"", ""青云课题价值"": ""1. 提升游戏的核心指标转化，强化玩家在对局、社交、内容消费个性化体验\n2. 实现大模型内容生成到内容消费的个性化体验，伴随模型能力和架构的增强而持续拓展"", ""青云课题挑战"": ""1. 生成式推荐对于传统特征工程的改造，大模型embedding参与特征交叉的改造\n2. 生成式推荐框架与传统推荐框架的融合探索\n3. 模型训练和在线推理如何平衡模型效果和计算性能"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
大模型在游戏社交场景中的研究与应用,"{""青云课题背景"": ""传统游戏社交系统依赖固定话术库和规则引擎，存在交互模式单一、内容同质化、难以适应玩家个性化需求等问题。多模态大模型为重构游戏社交生态提供了新机遇，可构建具备情感温度、动态适应性、高自由度的新型社交场景。"", ""青云课题价值"": ""1. ​重塑虚拟社交交互体验​\n2. ​赋能用户生成内容生态​\n3. ​构建智能社交生态​\n4. ​推动社交玩法创新​"", ""青云课题挑战"": ""1. ​上下文感知与长期一致性​\n2. ​多模态生成的质量与可控性​\n3. ​实时性与资源开销的平衡​\n4. ​个性化与通用性的矛盾​"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
融合AI优化与人机协同的游戏资产几何处理管线,"{""青云课题背景"": ""传统几何优化流程（如LOD生成、遮挡剔除、Mesh重拓扑等）需要依赖复杂工具链和大量人工。AI可显著降低操作复杂度，但当前缺乏任务感知的多任务几何优化框架。"", ""青云课题价值"": ""构建一个支持多任务（减面、剔除，重拓扑）和可控性强的智能几何处理系统，不仅能替代大量底层重复工作，还能实现人机协同的可解释AI工作流，对推动下一代游戏工业资产流水线具有广泛价值。"", ""青云课题挑战"": ""1. 如何在统一框架中融合多个优化目标（如质量/性能/视觉）\n2. 缺乏数据与通用模型支撑\n3. 需要打通从模型输入、结构理解、优化决策到效果验证的全链路\n4. 模型推理过程必须具备稳定性和人可控性，避免“黑盒”"", ""BG"": ""IEG"", ""部门"": ""光子工作室群"", ""中心"": ""光子技术发展部""}"
游戏语音大模型研究,"{""青云课题背景"": ""大语言模型与实时语音处理技术相结合，为语音交互技术带来了新的突破，不仅能理解你的文字，还能听懂你的话，并能够实现流畅的实时语音对话功能。 我们期待语音合成、语音识别和大模型背景，具备优秀科研素质和实操能力的你加入，一起探索游戏场景语音大模型。"", ""青云课题价值"": ""旨在构建基于游戏语音对话能力， 研究先进高效低延迟的游戏语音对话系统相关技术。"", ""青云课题挑战"": ""随着大模型技术发展和应用， 游戏语音交互技术的进步正也在改变游戏的玩法，但传统语音助手受限于高延迟、打断受限和缺乏对情感理解。 实现像人类一样“边听边说”的自然对话，低延迟，低复杂度的全双工对话的端到端语音模型受到游戏开发者的关注。"", ""BG"": ""IEG"", ""部门"": ""基础技术产品部"", ""中心"": ""GVoice开发中心""}"
游戏实时语音交互系统研究,"{""青云课题背景"": ""近些年随着深度学习发展，实时语音增强技术已经获得长足的进步，但是手机游戏语复杂的音频场景下，我们面临依然面对一些挑战。随着大模型技术的出现并， 游戏语音交互场景的也不断扩大， 并且不断的涌现出新的玩法， 这对语音增强技术提出了更高的要求。 同时近些年随着深度学习发展，特别是大模型技术的出现，语音识别和语音合成技术也得到了飞速进步。特别是语音合成音频在自然度和可懂度方面都有了极大的提升。我们期待语音信号处理、语音识别、语音合成背景，具备优秀科研素质和实操能力的你加入，一起探索游戏场景下全链路的语音增强、语音识别和语音合成技术。"", ""青云课题价值"": ""同时旨在构建基于游戏语音语音的对话/通讯/审核能力， 研究基于游戏场景中，实现单麦/多麦克风阵列，回声消除，远场/特定人声降噪， 语音识别， 语音合成能力，达到/超越目前SOTA算法能力。"", ""青云课题挑战"": ""在游戏实时语音场景中，面临着重大挑战，包括语音通话质量和语音识别/大模型交互。游戏音效的高音量和持续性导致用户语音信号与噪声比较低，而游戏环境的多样性则带来更多噪声和干扰。此外，移动端算力限制了增强算法的部署。在端到端语音大模型与游戏结合的人机交互中，对语音增强算法提出了新的功能需求。随着大模型技术的发展，游戏语音交互技术不断改变游戏玩法，语音识别和语音合成的性能直接影响智能交互体验。因此，对技术指标提出更高要求。挑战还在于研究和实现端上低成本高质量的语音识别和语音合成系统，以确保在计算资源受限和低延迟的实时语音通讯场景下仍能提供优质的语音识别和合成效果，以保证实时游戏语音交互体验。"", ""BG"": ""IEG"", ""部门"": ""基础技术产品部"", ""中心"": ""GVoice开发中心""}"
游戏匹配算法研究,"{""青云课题背景"": ""游戏中匹配算法不同能力的玩家能够在一个相对均衡的水平上一起游玩，以提高游戏的乐趣和公平性，从而保证留存率。 以ELO和TrueSkill 为代表的基于统计理论的传统算法已经广泛地应用到游戏匹配过程中。 近些年随着深度学习发展，以深度学习基础的算法逐渐展露头脚， 针对传统算法的建模能力不足进行了不小的改进。"", ""青云课题价值"": ""1. 腾讯游戏新一代匹配系统的算法设计和业务落地工作；\n2.分析游戏数据，实现实时胜率预测、玩家状态判定、角色平衡性分析及⽤户体验分析算法；\n3.参与腾讯游戏项目，推动匹配系统的创新"", ""青云课题挑战"": ""根据观测玩家在实际游戏中战斗局信息，挖掘游戏对局内更细致的\n隐藏关系和隐藏变量，保证对局公平的情况下， 实现更个性化和精准的匹配。"", ""BG"": ""IEG"", ""部门"": ""基础技术产品部"", ""中心"": ""GVoice开发中心""}"
LLM在游戏场景的应用与研究,"{""青云课题背景"": ""在DeepSeek驱动的大模型发展浪潮下，LLM作为解决方案的核心技术已成共识。对于游戏行业而言，如何有效地将LLM融入游戏场景，构建切实可行的高效方案，正成为引领未来发展的核心课题。"", ""青云课题价值"": ""​​1.构建全域智能对话系统​​，优化玩家在游戏对局内外的自然语言交互体验\n2.构建游戏语境的实时分析系统​​，解析玩家交互内容，实现玩家言论的​​风险动态监测与细粒度观点萃取​"", ""青云课题挑战"": ""1.多目标优化，逻辑一致性约束下的创意内容生成及幻觉抑制\n2.游戏本体驱动的意图理解架构，基于角色状态机与任务图谱的联合推理\n3.噪声鲁棒性对话系统，融合声学特征与对话历史的意图蒸馏算法"", ""BG"": ""IEG"", ""部门"": ""天美L1工作室"", ""中心"": ""技术发展中心""}"
用于游戏帧生成和材质压缩的实时神经网络渲染技术,"{""青云课题背景"": ""随着GPU架构的快速迭代，现代显卡的AI算力实现了革命性突破。NVIDIA Ampere/Ada/Blackwell架构集成的越来越先进的Tensor Core，可实现数百TOPS的实时张量运算；AMD RDNA 3的AI矩阵单元同样显著提升混合精度计算效能。这为神经网络在图形管线中的实时部署提供了硬件基石。与此同时，神经渲染技术正突破传统光栅化与路径追踪的局限。在超分辨率技术、外插帧技术，以及对渲染材质的神经表征方面等方面表现出了巨大的学术与游戏工业的巨大发展潜力。游戏产业面临4K/120Hz高帧率渲染与次世代材质资产爆发的双重压力。传统渲染管线在硬件资源消耗与视觉保真度间存在根本矛盾，亟需通过神经渲染实现两大革新：\n1. 实时帧生成：利用AI帧插值突破物理渲染算力瓶颈；\n2. 神经网络材质压缩：将传统多Texture材质和光照(Probe/Lightmap)编码为紧凑神经网络，替代传统纹理映射，实现存储与带宽的指数级优化。"", ""青云课题价值"": ""本课题旨在融合前沿硬件AI能力与实时神经渲染技术，构建新一代游戏内容生成与传输范式。解决游戏产业在高帧率需求+高精度材质资产膨胀双重压力下的产业技术痛点。让下一代游戏在全平台上实现帧率和画质的同步显著提升。关键技术难点：1. 显卡实时渲染中的AI能力发挥：充分释放Tensor Core/光流加速器与Shader渲染协同的实时AI推理能力，特别是算力的充分释放\n2. 基础技术难点：对新的训练数据采集和生成、轻量编码的网络结构、神经网络的压缩技术、神经材质表征技术等亟需进一步发展\n3. 硬件厂商合作难点：需要与前沿硬件厂商深入合作，形成软硬件协同的创新闭环，用硬件最新技术来赋能AI技术，从而解决游戏产业的技术痛点"", ""青云课题挑战"": ""现代显卡的Tensor Core与光追硬件虽持续升级，但*实时*神经渲染对算力、延迟提出极致要求。训练轻量级神经网络模型需在远低于16ms的时间内完成高保真帧生成，同时满足游戏场景的动态交互性。这对于：\n1. 模型压缩（如权重量化）可能削弱渲染质量；\n2. 多光源下的光流预测存在挑战，对动态物体、角色运动以及物体变形的运动预测存在挑战，对光流与运动预测结合的混合表达上存在的挑战更大；\n3. 对显存带宽、对shader计算与神经网络并行计算的软件架构提出了巨大的挑战；\n4. 计算复杂度挑战巨大，现有的论文在帧生成和神经网络材质压缩方面的性能都难以直接满足120fps以上需求，并且难以下沉到4060/5060级别的桌面显卡上；\n5. 材质压缩泛化能力挑战，神经材质编码（如神经纹理）需兼容不同光照与视角，以及不同时间上的变化，同时跨场景迁移易引发失真，并且在shader中实时解压计算的挑战很大。"", ""BG"": ""IEG"", ""部门"": ""效能产品部"", ""中心"": ""引擎技术中心""}"
3D生成大模型,"{""青云课题背景"": ""利用生成式 AI 技术从文本或图像生成 3D 模型，应用场景包括：\n1. 游戏行业：快速生成角色、道具和环境，加速开发，增强玩家沉浸感。\n2. 影视制作：用于概念艺术和特效，减少手工建模时间。\n3. VR/AR：创建沉浸式 3D 环境，提升用户体验。"", ""青云课题价值"": ""1. 大幅提升 3D 建模效率：传统 3D 建模需要数周甚至数月，而 3D 生成工具可在几秒到几分钟内生成高质量、可编辑的模型。\n2. 降低创作门槛，赋能独立开发者：通过提供直观界面和API支持，无需专业建模技能即可生成游戏资产（如角色、道具、场景）。这让小型团队或独立开发者也能创建高质量 3D 内容，降低进入门槛，释放更多创意潜力。\n3. 无缝集成现有工作流：3D 工具生成的模型可以直接导入 Unity、Unreal Engine 等主流游戏引擎，与现有开发流程无缝衔接，减少技术适配成本。"", ""青云课题挑战"": ""1. 形状编码：如何将 3D mesh 的形状有效地编码到隐空间，并且能够有效地保持原来 3D mesh 中精细的形状细节。\n2. 结构保持：艺术家制作的 3D mesh 中包含了丰富的结构信息，比如分离的部件，能够有效地区分这些结构是一个比较重要的技术挑战。\n3. 拓扑生成质量：工业界中使用的 3D mesh 一般对拓扑质量要求较高，如何在保持 3D mesh 的形状的基础上，还能生成高质量的拓扑是一个需要解决的问题。\n4. 纹理生成质量：从文本或者图片中还原模型的纹理，并且保持一致性以及高质量的细节是一个很有挑战的问题。\n5. 3D 模型的可控生成：通过多种条件来对模型进行全局或者局部的可控生成，帮助艺术家更好的生成符合他们要求的 3D mesh。"", ""BG"": ""IEG"", ""部门"": ""效能产品部"", ""中心"": ""游戏AI中心""}"
基于游戏的AGI研究,"{""青云课题背景"": ""将大语言模型、强化学习等技术应用于游戏，增强 bot、NPC 的语言智能和决策智能，提升玩家体验。"", ""青云课题价值"": ""1. 对战 bot：游戏中作为玩家对手方，通过针对不同的玩家投放不同难度的对手，改善玩家体验，为新手玩家或易流失玩家提供温暖局体验。\n2. 陪玩 bot：游戏中作为玩家队友方，玩家通过语音或文字与之沟通互动，理解玩家意图，根据玩家需求完成一系列操作，帮助玩家实现战术部署和团队配合等，改善游戏体验，提供情绪价值。\n3. 剧情 NPC：使游戏中 NPC 具有更加丰富的互动话术，强化游戏的世界观，辅助剧情和任务演进。"", ""青云课题挑战"": ""1. 降低强化学习的应用门槛，低成本训练和部署，短时间将 bot/NPC 的行为指标优化至满足游戏要求。\n2. 提升 bot/NPC 的拟人度和个性化。\n3. bot/NPC 对游戏世界观、游戏当前环境有充分的理解与感知，确保几乎任何情形下其行为和语言符合预期。"", ""BG"": ""IEG"", ""部门"": ""效能产品部"", ""中心"": ""游戏AI中心""}"
多模态面部动画生成大模型,"{""青云课题背景"": ""（1）3D 面部动画离线制作，10s 动画需一周制作，耗时耗力；\n（2）3D 面部实时驱动方面，当前生成效果当前无法达到逼真、高性能的实时驱动要求。"", ""青云课题价值"": ""可根据不同的模态的输入，如文本、语音等，可离线生成高质量、丰富的面部动画。实时驱动方面，可根据输入，实时生成逼真人脸动画，在游戏实时场景中驱动数字角色（智能 NPC)。"", ""青云课题挑战"": ""1. 跨模态对齐与语义一致性\n异质数据融合：语音、文本、视频等模态的时空尺度差异大（如语音帧率 vs 视频帧率），需精确对齐唇形、表情与语音节奏。\n弱相关性建模：语音与面部上半部动作（如眉毛）、头部姿态关联性弱，模型易忽略细节动作的自然性。\n2. 数据稀缺与质量瓶颈\n高质量4D数据集匮乏：公开数据集规模小，且多为中性表情，缺乏情感、极端姿态样本，导致生成动画僵硬。\n3. 实时性与物理约束\n自回归模型因自注意力机制导致二次计算复杂度，难以满足游戏实时交互需求（需>30FPS）。生成动画需符合面部解剖学（如肌肉联动、皮肤弹性），否则易出现不自然的扭曲。"", ""BG"": ""IEG"", ""部门"": ""效能产品部"", ""中心"": ""DCC算法研究中心""}"
多模态肢体动画生成大模型,"{""青云课题背景"": ""业界肢体动画常通过手 K、动捕的方式来进行离线制作，无法实时合成，且离线制作 10s 钟动画制作需要 5人日，耗时耗力。当前无可应用的，可实时驱动的角色肢体动画模型。"", ""青云课题价值"": ""离线制作方面，可根据不同模态的输入，如文本、视频、关键帧等，生成高质量（接近于光学动作捕捉的）的肢体动画；实时驱动方面，可根据输入，生成实时的角色肢体动作。"", ""青云课题挑战"": ""1. 多模态对齐与语义一致性对齐\n文本、音频、视频等模态的时空尺度差异显著（如音乐节拍与动作节奏的异步性），需精确建模跨模态关联\n2. 动作表征与建模\n当前不论 diffusion 还是 transformer 在动作生成领域并不是一种完美的表征方案，未取得非常高质量的效果\n3. 物理真实性与多样性\n生成动作常违反物理规律（如滑步、浮空）。另外存在生成风格与多样性不足的问题。"", ""BG"": ""IEG"", ""部门"": ""效能产品部"", ""中心"": ""DCC算法研究中心""}"
骨骼生成大模型,"{""青云课题背景"": ""当前业界骨骼制作方式仍然通过手工制作，单套服装的骨骼架设时间需要 1天左右，耗时耗力。"", ""青云课题价值"": ""用户输入 mesh，直接生成可用的高质量骨骼，且支持编辑，极大降低制作成本。"", ""青云课题挑战"": ""1. 拓扑结构泛化  任意骨骼适配：非人形生物（如多足兽、机械体）的骨骼拓扑复杂，模型需动态适应不同关节数与层级。语义对应学习：自动识别Mesh关节功能（如“膝关节”vs“机械轴”），避免权重绑定错误。\n2. 几何保真、编辑优化\n生成骨骼需驱动Mesh时避免皮肤穿插或过度拉伸，需联合优化蒙皮权重与骨骼位置。用户手动编辑骨骼可能破坏模型生成的生物力学合理性，需设计约束保护机制"", ""BG"": ""IEG"", ""部门"": ""效能产品部"", ""中心"": ""DCC算法研究中心""}"
基于游戏的AGI研究,"{""青云课题背景"": ""传统NPC依赖预设脚本，行为固定单一，缺乏动态响应能力，导致玩家交互体验割裂，无法应用到3A游戏项目中。通过LLM及情感建模，赋予NPC自主决策、动态行为调整及个性化交互能力。"", ""青云课题价值"": ""实现玩家与NPC的情感连接，推动游戏从“任务驱动”转向“叙事沉浸”，重塑虚拟世界真实感"", ""青云课题挑战"": ""1. \n如何获取高质量数据用于训练。\n\n2. \n如何设计新的模型，生成可控的角色性格。\n\n3. \n如何在对话中，NPC不出戏。"", ""BG"": ""IEG"", ""部门"": ""Advanced Data Group"", ""中心"": ""AI Accelerator""}"
多语言ASR研究,"{""青云课题背景"": ""随着游戏全球化和多人在线游戏的普及，实时语音交互需求激增。传统ASR在跨语言、方言和游戏场景中准确性不足，而多语言ASR技术通过统一模型支持多语种识别，提升了跨文化玩家间的沟通效率，增强了游戏体验。"", ""青云课题价值"": ""规模化模型突破语言壁垒，统一架构实现高资源带低资源，显著提升跨语言识别效率与准确性"", ""青云课题挑战"": ""1. \n如何获取高质量数据用于训练。\n\n2. \n如何设计新的模型，实现效果的提升。\n\n3. \n如何解决ASR中的鸡尾酒问题。"", ""BG"": ""IEG"", ""部门"": ""Advanced Data Group"", ""中心"": ""AI Accelerator""}"
"游戏市场宏观指标动态预测系统
​​","{""青云课题背景"": ""当前游戏市场受宏观环境调整、文化潮流和技术突破等多因素交织影响。传统预测模型依赖结构化数据，难以捕捉非结构化数据中所蕴含的经济因素与游戏市场之间的隐性关联。"", ""青云课题价值"": ""通过融合宏观环境变量等多模态信息，构建可解释性因果推理框架，辅助理解关键驱动因素，把握当前市场动态和趋势。"", ""青云课题挑战"": ""1. 数据异构性​​：结构化经济数据与非结构化文本的特征对齐需解决语义鸿沟问题；\n\n​​2. ​​因果推理偏差​​：宏观环境与市场指标的相关性可能掩盖真实因果关系。"", ""BG"": ""IEG"", ""部门"": ""Advanced Data Group"", ""中心"": ""Data Insight""}"
"大语言模型赋能游戏智能运营​
​","{""青云课题背景"": ""玩家注重游戏内的流畅沟通体验，本课题聚焦于构建基于大语言模型与向量检索的智能对话系统，帮助提供高效和沉浸式的客服与活动咨询体验。"", ""青云课题价值"": ""1. 及时智能回复，大幅缩短等待时间；\n\n2. 精准知识检索，提升答复准确度；\n\n3. 降低客服成本并提升反馈效率。"", ""青云课题挑战"": ""1. 多模态知识库构建\n\n2. 对话流与上下文管理\n\n3. 反馈推送机制\n\n4.输出安全性控制"", ""BG"": ""IEG"", ""部门"": ""Publishing Technology Group"", ""中心"": ""SmartLink&CRM Tech""}"
高品质3D资产生成,"{""青云课题背景"": ""游戏研发过程中，3D资产的制作需要耗费大量人力资源。目前现有的3D资产生成模型得到的资产，模型精度不高，无法应用到3A游戏项目中。"", ""青云课题价值"": ""降低3D资产开发周期，整体缩短3A游戏研发周期。"", ""青云课题挑战"": ""1. \n如何获取高质量资产库用于训练。\n\n2. \n如何设计新的模型，生成高质量的资产。\n\n3. \n如何在生成中，细节可以更加可控。"", ""BG"": ""IEG"", ""部门"": ""Publishing Technology Group"", ""中心"": ""UA&MKT Tech""}"
社交媒体多模态内容理解洞察研究,"{""青云课题背景"": ""目前社媒上有很多游戏相关的内容，主要是以视频形式呈现。需要有一套高效的多模态内容理解和分析方案，从海量内容中快速找到业务洞察，指导后续决策。"", ""青云课题价值"": ""内容洞察作为高层决策依据，会影响宣发预算分配，宣发卖点，KOL选取偏好等决策。"", ""青云课题挑战"": ""1. \n海量内容如何高效处理。\n\n2. \n多模态如何融合。\n3. 如何提取正确的洞察。"", ""BG"": ""IEG"", ""部门"": ""Publishing Technology Group"", ""中心"": ""UA&MKT Tech""}"
基于LLM的UA投放智能Agent,"{""青云课题背景"": ""结合强化学习与LLM的Agent正在成为趋势，项目目标是构建一个能够读取投放数据、分析市场变化并提出优化建议或直接操作平台的智能Agent。"", ""青云课题价值"": ""1.形成半自动化或全自动化的投放闭环。\n2. 大幅度减少人力成本。\n3. 可作为“人机协同”的智能助手辅助投放团队决策。"", ""青云课题挑战"": ""1.需要具备长期记忆和稳定策略决策能力。\n2. 如何与现有投放系统API/平台集成。\n3. 安全性、误操作的防范机制。"", ""BG"": ""IEG"", ""部门"": ""Publishing Technology Group"", ""中心"": ""UA&MKT Tech""}"
PCG-DataAgent架构设计与评测方法,"{""青云课题背景"": ""随着大模型能力的持续提升，在商业数据分析领域正在重塑分析工具，结合模型的DataAgent、ChatBI等新形态在持续探索和实践中。\n本项目致力于研发业界领先的、基于大语言模型及传统BI工具结合的智能数据分析应用。当前，实现该目标面临一系列领域前沿挑战，包括高准确度要求的智能对话系统、复杂分析路径规划、海量领域知识的有效学习与融合以及NL2Code等。现有解决方案在准确性、智能化程度和场景适应性方面存在显著局限，难以满足深度的产业增强分析（Augmented Analytics）的需求。本项目旨在针对增强分析流程中的关键环节进行核心技术攻关，重点突破上述瓶颈问题，以期快速推进具备业界领先水平的智能数据分析应用的产业化落地"", ""青云课题价值"": ""通过对DataAgent架构、测评方法、前沿模型研究等，解决当前产业增强分析能力在准确性、智能化程度和场景适应性方面存在显著局限，实现具备业界领先水平的智能数据分析应用的产业化落地"", ""青云课题挑战"": ""1、DataAgent架构设计与优化：如何构建先进的DataAgent系统架构，确保其可扩展性、鲁棒性与高效性，以满足数据分析场景的复杂任务通过率、准确性及灵活性要求\n2、DataAgent性能测评方法： 如何建立一套科学、系统的测评体系与方法论，用于客观评估DataAgent在准确性、效率、鲁棒性、用户交互体验等维度的综合性能，确保其达到业界领先标准。\n3、多模态数据理解及知识库高效构建： 如何结合多种模态的企业内部数据资产，高效构建、清洗、完善数据分析知识库，并应用于DataAgent中"", ""BG"": ""PCG"", ""部门"": ""大数据平台部"", ""中心"": ""分析平台中心""}"
PCG-AI智能体自主进化机制研究和应用,"{""青云课题背景"": ""随着大模型能力的不断提升，AI智能体技术得到快速发展，并开始有较多通用和垂类的智能体产品发布。在模型固定的情况下，如何让智能体具备自主学习和自我进化的能力，是一个非常重要的研究方向。"", ""青云课题价值"": ""AI智能体自主进化机制具有重大的科研价值和应用前景。AI智能体是大模型应用落地的主流技术方向，它借助大模型的能力，来做任务规划和工具调用，从而能够完成复杂任务。大模型的知识和能力是相对固定的，但在不同领域尤其是私域内，对模型和智能体的要求并不相同。自主进化可以建立从环境反馈到能力迭代的闭环体系，使得智能体能够自主适应不同场景的要求，逐步演化为各个领域内的专家，从而达到应用标准。"", ""青云课题挑战"": ""1、评估和反馈机制：在复杂任务场景下，任务效果的评估非常困难。如何建立起一套可用的自我评估机制，是该课题的核心难题\n2、记忆机制：构建自主进化的高效记忆系统面临诸多挑战，具体包括：经验识别和价值评估，经验的表征和触发机制，经验的自我进化能力\n3、迁移学习：同领域内的不同用户和不同任务，以及相似领域之间，是否能建立起有效的知识迁移机制，在个性化和能力提升之间达到好的平衡"", ""BG"": ""PCG"", ""部门"": ""大数据平台部"", ""中心"": ""机器学习平台中心""}"
PCG-生成式推荐大模型研究与应用（含多模态理解）,"{""青云课题背景"": ""随着近年来Transformer模型在NLP和CV领域大获成功，在推荐领域有越来越多的学者开始研究新的推荐范式：把推荐问题定义为序列推导问题，利用自回归模型来做排序和召回、序列评价等任务。在生成式推荐范式下，多场景、多目标、多兴趣、冷启动优化等问题的建模方式也需要新的设计，本课题主要研究和探索以上问题在生成式推荐范式下的新方法，助力提升业务的推荐效果提升。"", ""青云课题价值"": ""通过LLM模型的思想去重构排序模型和召回模型，对产品带来效果提升"", ""青云课题挑战"": ""1. 更有效的模型结构，刻画用户个性化兴趣，提升模型泛化能力。\n2. 生成式推荐范式的多场景建模：在生成式推荐范式下，针对不同应用场景，研究如何设计和构建模型（比如Lora+MoE结合等），以适应各类场景需求，确保推荐系统的灵活性和可扩展性。\n3. 多目标和多兴趣建模：研究如何在生成式推荐范式下，对用户的多重兴趣和不同目标进行建模，确保推荐结果能够满足用户的多元化需求。\n4. 冷启动优化：针对新用户和新物品的冷启动问题，探索在生成式推荐范式下的建模和优化方法，确保推荐系统能够快速、准确地为新用户和新物品提供合适的推荐结果。\n5. 生成式模型如何在召回上应用"", ""BG"": ""PCG"", ""部门"": ""大数据平台部"", ""中心"": ""数据挖掘中心&推荐系统中心""}"
PCG-AIGC在用增营销领域可控生成应用研究,"{""青云课题背景"": ""近年来，人工智能生成内容（AIGC）技术通过多模态大模型与生成式算法的突破，正在快速渗透至数字营销领域以重构创意素材生产全链路。在增长营销场景中，素材创意质量与迭代效率对营销效果至关重要，而传统人工制作模式面临两大核心瓶颈：1）创意对个体经验高度依赖，易受知识边界限制，导致同质化问题；2）创意素材制作流程冗长，从策划到视觉设计实现跨多角色协作，时间与人力成本高。本课题旨在基于AIGC技术的智能化营销，实现营销创意过程的提效和增强，让人摆脱繁杂的执行实现，聚焦更高质量和个性化的创意灵感思考。"", ""青云课题价值"": ""本课题旨在基于AIGC技术的智能化营销，实现营销创意过程的提效和增强，让人摆脱繁杂的执行实现，聚焦更高质量和个性化的创意灵感思考。"", ""青云课题挑战"": ""1.对领域海量密集知识的整合应用，同时解决知识覆盖率，准确利用低幻觉等问题；\n2.可控生成，包括商品、ip、角色等一致性保真，场景风格、人物动作可控问题；"", ""BG"": ""PCG"", ""部门"": ""公共数据科学部"", ""中心"": ""大禹素材中心""}"
PCG-多模态大型语言模型的研发与应用,"{""青云课题背景"": ""1.跟进多模态大型语言模型前沿技术，对模型相关训练，强化学习，调试，评估进行前置研究，实现多模态内容的统一理解和生成技术方案，进而提升基础大模型的内容理解和生成能力，提升图文，图像，视频精准理解、交互式处理与个性化生成能力。\n2. 推动在实际业务场景中进行应用落地，研发业界领先的垂类模型，通过论文发表专利申请等方式来扩大技术影响力；\n3. 推广复用到内部更多场景，提升整体研发效率；"", ""青云课题价值"": ""面向QQ亿级别用户的内容业务生态，包括QQ基础、腾讯频道、QQ群，QQ空间，QQ表情等各种形式的内容，构建基础的多模特大模型基座并且针对不同场景针对性的创新，提升整体效率和改善业务效果。"", ""青云课题挑战"": ""职位要求：\n图像处理/机器学习/模式识别/计算机/电子信息/数学等相关专业，博士优先，在计算机视觉、深度学习，多模态等方面有一定研究基础\n具备以下任一经验者优先：\n1.熟悉多模态领域主流模型和算法框架；\n2.优秀的分析问题和解决问题的能力，对新技术充满好奇心，爱挑战高难度，善于提出解决方案并快速验证；\n3.研究能力强，如在CVPR/ICCV/ECCV/NIPS/ICML/SIGGRAPH等会议上发表过论文或者相关比赛获奖者优先考虑；\n挑战:\n1. 多模态大模型的幻觉消除，个性化内容理解能力\n2. 多模态大模型支持多分辨率、多语义尺度任务\n3. 多模态思维链技术研究"", ""BG"": ""PCG"", ""部门"": ""社交平台架构中心"", ""中心"": ""/""}"
PCG-对2D/3D/4D内容的生成过程加以控制,"{""青云课题背景"": ""内容生成技术已经取得了长足的进步，但是场景级别的高质量2D/3D/4D内容生成仍然面临着弱可控性的挑战，且现有框架依赖数据驱动的伪三维建模，导致物理失真与低可控性。完全物理可控的生成过程将极大地提升我们对世界的生成能力，促进世界模型的发展，对下一代人工智能的发展有至关重要的意义。"", ""青云课题价值"": ""突破场景级高质量内容生成的物理可控性瓶颈，通过融合物理规则与生成模型，实现内容形状、运动、交互的精准控制，解决现有技术的弱可控性与物理失真问题。既能提升影视、游戏，广告素材等领域的内容生产的可用性与效率，也增强 AI 对物理世界的因果推理与泛化能力，为通用人工智能（AGI）提供关键底层技术。"", ""青云课题挑战"": ""课题挑战：物理可控的2D/3D/4D内容生成职位要求：\n- 良好的数学，3D视觉或者图形学基础\n- 扎实的代码能力\n- 了解视频/图像生成的主流算法，熟悉3D/4D的常见表征和重建方法\n- 发表过视觉/图形学/AI领域的顶会/顶刊者优先\n- 有开源项目者优先"", ""BG"": ""PCG"", ""部门"": ""应用研究中心"", ""中心"": ""/""}"
PCG-可控视频生成,"{""青云课题背景"": ""随着人工智能生成内容(AIGC)技术的突破性发展，图像和视频内容生成的质量也在飞速提升。在基础生成模型质量不断提升的过程中，我们也在思考如何让这些技术能实际改变人们的工作和生活。当前基于文本或图像条件的视频生成模型虽能生成高质量视频，但无法满足人们个性化创意的需求。而传统视频编辑和制作高度依赖人工操作，从素材筛选，剪辑到后期合成均需耗费大量时间与专业资源。因此，研究如何通过更加可控的视频生成技术，重构已有视频编辑工具的逻辑和能力，使其迈向智能化创作的新阶段，具有很高的价值。"", ""青云课题价值"": ""学术价值：目前基于aigc的视频生成和编辑属于新兴领域，有许多可以探索的问题，可以帮助填补研究空白，促进行业发展。\n应用价值：该技术能帮助用户进行高度自动化的图像和视频创作，减少大量人力和时间成本，产生巨大应用价值。\n创作价值：利用该技术能更好的发挥个人想象力，更容易将用户的想法转化为创新性视频内容，让创作更加容易。"", ""青云课题挑战"": ""1. 精确的条件控制：如何将这些控制条件注入模型，实现精确的条件控制？\n2. 有效的训练策略：高质量的视频数据具有稀缺性，如何在训练中解决这个问题？\n3. 统一的生成框架：视频编辑和控制的类型具有多样性，如何处理不同类型的编辑？\n4. 技术应用：如何将新的技术应用到实际生产，使其能发挥更大价值？"", ""BG"": ""PCG"", ""部门"": ""应用研究中心"", ""中心"": ""/""}"
PCG-多模态可控视频生成在影视制作中的应用,"{""青云课题背景"": ""生成式技术在影视内容拍摄在影视制作当中越来越重要，能够加速制作周期，提升制作质量。课题围绕如何内容制作者的意图，通过多中控制手段，生成相应的视频、资产等内容展开。通过这类技术，可以打破当前内容生产的方式与形式，公司与行业在未来内容制作领域探索新机遇。"", ""青云课题价值"": ""探索影视制作的新范式，进而提升制作内容多样性，降低内容制作成本与周期，解放制作者的创造力"", ""青云课题挑战"": ""1. 了解影视制作流程，并从中寻找切合行业需求的内容控制方法。\n2. 提升生成内容的质量，包括一致性，真实度，保真度等\n3. 后期制作中，实现对视频、资产实时渲染与重打光。"", ""BG"": ""PCG"", ""部门"": ""智能创作与内容平台部"", ""中心"": ""AI技术中心""}"
PCG-大语言模型在影视剧本创作和运营决策的应用,"{""青云课题背景"": ""Deepseek等释放了LLM推理和创作能力。对于影视综垂类应用场景，我们希望使用模型推理能力，在业务垂类数据去挖掘LLM在决策场景的使用机会以及剧本创作应用场景。然而一方面垂类数据往往是敏感且封闭的，另外一方面对于专业的剧本创作仍然需要很高的效率和可靠性的要求，那么LLM自有推理能力与业务封闭知识的可靠结合是我们业务期望解决的问题。"", ""青云课题价值"": ""探索影视剧本创作和垂直领域场景的大模型应用，进而提升内容创作、运营效率，降低内容制作成本与周期，解放制作者的创造力"", ""青云课题挑战"": ""1. 如何在保持推理效果的前提下压缩思考过程，加速结果输出速度。\n2. 如何控制模型处理外部知识与内部知识的冲突与融合，降低最终结果的幻觉。\n3. 如何解耦推理能力与内在知识，实现推理逻辑+外部知识融合的可靠融合。\n4. 如何构建具有发散创造力同时具有可控生成能力的剧本创作工具"", ""BG"": ""PCG"", ""部门"": ""智能创作与内容平台部"", ""中心"": ""AI技术中心""}"
PCG-AI拟真角色通话场景语音对话大模型的研究,"{""青云课题背景"": ""近年来，随着AI大模型技术的发展，高拟真的陪伴类AI产品逐渐涌现。陪伴类大模型智能体因为具有制造成本低、代入感高等优点，得到了业内广泛的关注。\n为了优化智能体语音对话能力，提升人与智能体的交互体验，我们申请 基于多模态大模型的超自然语音对话技术研究 项目。具体的，我们将针对以下3个问题开展研究工作：1、语音对话中拟真口语输出的研究；2、语音对话中微情绪的感知、理解和反馈的研究；\t\n3、角色扮演语音对话系统的研究。"", ""青云课题价值"": ""本课题旨在通过对语音对话中拟真口语输出、微情绪的感知 理解和反馈2个方面的研究，实现面向AI拟真角色通话场景的高拟真语音交互，从而提升人与AI扮演角色语音互动的代入感和真实感。"", ""青云课题挑战"": ""1、人类真实对话语音具有多样性高、随机性强的特点，例如口误、反复或咳嗽等，甚至是有个人特色的发音（例如周星驰的笑声）。高质量样本少、收集难度大，需要在有限资源下实现拟真语音的建模和合成。\n2、在角色扮演、陪伴场景下，除了对语音中的“字面意思”进行理解和反馈，还需要对语音中的超文本信息（如微情绪）进行感知、理解和反馈。\n3、以上能力需要在低延迟条件下实现。\n1、人类真实对话语音具有多样性高、随机性强的特点，例如口误、反复或咳嗽等，甚至是有个人特色的发音（例如周星驰的笑声）。高质量样本少、收集难度大，需要在有限资源下实现拟真语音的建模和合成。\n2、在角色扮演、陪伴场景下，除了对语音中的“字面意思”进行理解和反馈，还需要对语音中的超文本信息（如微情绪）的感知、理解和反馈。\n3、以上能力需要在低延迟条件下实现，模型结构需要适配流式推理要求。"", ""BG"": ""PCG"", ""部门"": ""C项目中心"", ""中心"": ""/""}"
PCG-完全可控的拟真数字生命的形象生成大模型的研究与应用,"{""青云课题背景"": ""根据ARK Invest报告显示，“AI+情感陪伴”的市场规模将从现在的全球年收入3000 万美元增至700 亿至1500 亿美元。当前情感陪伴 App 的核心痛点在于：现有聊天 Bot 的形象生成模型在可控性（如外貌特征精准定制、表情动作实时响应）与拟真度（如面部细节、肢体语言的自然流畅性）上仍存在显著技术缺口，无法满足情感陪伴场景对 “高沉浸式交互” 的核心诉求。本课题以情感陪伴 App 为应用载体，针对以下三个方面展开研究：1）超写实形象生成：通过海量高质量的文+图，文+视频的形象数据，跨模态进行信息数据融合，提升生成形象的光影，皮肤纹理，毛发等细节的妮真效果。\n2）跨模态可控生成：结合文本语义、用户画像、参考图像等多源输入，构建 “语义 - 形象” 映射模型，实现外貌特征、服饰风格等维度的精准调控，参考人像、物品和背景的一致性，多角度生成；\n3）情感驱动动态建模：引入情感计算与强化学习，探索使数字人形象能够根据聊天内容实时生成匹配的微表情、肢体动作的方案，形成 “情感感知 - 形象响应” 的闭环；"", ""青云课题价值"": ""本课题聚焦 “完全可控性” 与 “拟真度” 的技术突破，通过构建专用大模型，实现从静态形象生成到动态情感驱动的全链路优化，填补情感陪伴领域数字人技术空白，为提升用户粘性、拓展商业化场景（如虚拟伴侣、心理陪伴）提供核心技术支撑。"", ""青云课题挑战"": ""1. 形象生成可控性：存在“部分指令不响应或者错响应” 、“个性化程度不足”、“动态表现力薄弱”、“情感共鸣缺失” 等问题，难以通过形象载体构建深度用户情感连接；\n2. 动态驱动与情感表达脱节：动态驱动的形象生成常出现 “口型错位”“表情僵硬” \""快速动作时形象不一致\""等问题，缺乏基于情感语义的动态映射机制；\n3. 生成质量与效率不易兼得：现有的形象生成方案生成效果和效率往往相悖。高质量的生成结果一般需要更多的参数和中间步骤。如果是动态生成，生成时间往往以分钟计，无法满足AI情感陪伴高频沟通、实时响应的需求。"", ""BG"": ""PCG"", ""部门"": ""C项目中心"", ""中心"": ""/""}"
PCG-基于强化学习的角色扮演能力提升,"{""青云课题背景"": ""基于AI大模型的角色扮演产品逐渐走向公众的视野，如国外的characterAI，国内的猫箱、星野。AI陪伴市场正在飞速发展，被认为是人工智能领域增长最快的赛道之一。\n为了不断提升角色扮演产品的用户体验，针对当前角色扮演存在以下问题，希望通过强化学习的相关研究进行改进：\n1）在角色扮演场景下，专业能力如数学、代码能力会存在大幅度的下降，但并不是知识遗忘造成的，而是使用角色扮演的system prompt就会出现，业界Claude、GPT-4等均存在该问题，可以通过基于规则的强化学习等方法增加角色扮演场景下的各种专业能力从而提高角色智商；\n2）目前角色扮演在说话风格上虽然能够遵守角色profile，但是会存在句式单一、随着聊天深入价值观依然会暴露出非常“正义”的助手特性和共情能力较弱等问题，可以通过强化学习进一步提升聊天效果；"", ""青云课题价值"": ""探索强化学习在角色扮演任务中的应用，提高角色智商，增加角色的拟人、共情能力，给用户提高更沉浸的体验。"", ""青云课题挑战"": ""1. 大模型在各种专业能力如数学、代码上表现非常好，但是因为指令隔离等原因导致下游任务中专业能力较弱，如何引入强化学习等手段来提高角色扮演任务中的各种专业能力，并且平衡通用专业能力和角色扮演相关能力的同步学习从而提高角色的智商情商和问题解决能力。\n2. 开放式任务的强化学习一直受限于RM的能力导致训练困难，如何训练角色扮演场景下尽量无偏的RM模型，持续提高角色扮演任务的效果和所涉及到的各项能力，以满足角色扮演的各种复杂场景。"", ""BG"": ""PCG"", ""部门"": ""C项目中心"", ""中心"": ""/""}"
AI安全研究以及AI赋能安全,"{""青云课题背景"": ""安全是每一个大型企业都必须面对的问题，在大模型应用爆发的背景下，各大企业在大模型自身安全和企业内部代码安全方面面临着更大的挑战，腾讯安全平台部专注于前沿安全技术研究，在AI安全与代码安全领域解决业界有挑战性的安全问题，保障腾讯内部及客户的业务安全。"", ""青云课题价值"": ""参与AI安全前沿研究和演练，解决AI安全、代码生成、安全基础设施领域的安全问题，为腾讯业务保驾护航，提升在安全领域的技术影响力。"", ""青云课题挑战"": ""探索一种行之有效的基于大语言模型的白盒工具用于漏洞检测，解决行业与腾讯大模型业务发展中遇到的各类安全风险，将并相关成果落地到腾讯的安全防御体系中。"", ""BG"": ""TEG"", ""部门"": ""安全平台部"", ""中心"": ""朱雀实验室/基础安全中心""}"
腾讯混元基座模型-统一全模态基础pretrain模型结构设计及长文机制调研,"{""青云课题背景"": ""近年来，随着大语言模型（如GPT-4o、LLaMA）和多模态模型的快速发展，构建能够统一理解与生成多模态数据的全模态预训练模型成为人工智能领域的重要方向。传统的多模态模型多聚焦于图文配对数据，而全模态（Omni-modal）模型需进一步整合音频、视频、3D、深度图等更广泛模态，以模拟人类通过视觉、听觉、触觉等多感官认知世界的过程。"", ""青云课题价值"": ""你将参与到统一全模态基础pretrain高效模型结构、训练策略、Scaling Law、关键数据合成以及全模态预训练方法，以提升混元大模型的通用AIGC能力。"", ""青云课题挑战"": ""1. 多模态数据整合与对齐，数据多样性：全模态需处理文本、图像、音频、视频、3D等多源异构数据，其采集、清洗和配对成本极高。例如，MiCo需构建包含图像、深度图、法线图及配对文本的多模态数据集，但如何避免信息冗余与泄露仍是难点；\n2. 统一模型架构设计，模态感知与融合机制：需平衡参数共享与模态特异性。例如，Meta的MoMa架构通过模态感知型专家混合（MoMa）实现文本与图像的分组处理，但其路由机制需解决负载均衡与计算效率问题；\n3. 计算资源与训练效率：全模态预训练需处理海量数据，对分布式训练和并行计算提出极高要求。此外，混合专家架构的稀疏性虽能降低计算量，但需优化升级改造（Upcycling）策略以避免早期路由次优问题；\n4. 长文本建模机制优化，包括位置编码与高效注意力机制：相对位置编码（如ROPE）在跨样本拼接时可能因注意力掩码被误抑制，需设计更灵活的位置感知机制以支持长文本生成与推理；此外高效的注意力机制（如Mamba2），能够大幅降低计算成本，而且通过Hybrid的方式，也能提升模型的performance。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""大语言模型算法中心""}"
腾讯混元基座模型-大模型AI Coding及代码Based Agent研究,"{""青云课题背景"": ""近年来，大语言模型（LLMs）在代码方面取得了显著进展，AI Coding作为新兴热点，已通过代码补全、错误修复、代码生成等功能显著提升了开发效率。然而，当前的代码大模型多局限于函数级、片段级的编程能力，距离实现端到端、项目级的智能开发仍有较大差距。因此，本课题聚焦于赋能LLMs进行项目级编程，典型应用场景例如： 理解项目管理平台（如GitHub Issues）上的问题描述，分析代码库，定位问题根源，并自主生成解决方案、修改代码、提交拉取请求（Pull Request）； 根据用户需求自主生成功能完整、响应式的前后端项目级代码等等。"", ""青云课题价值"": ""你将参与到Code Agent框架设计、代码长文预训练数据优化、复杂指令数据的合成和自动评估、奖励系统建设和强化学习策略优化，提升混元大模型的项目级编程和代码Agent能力。"", ""青云课题挑战"": ""1. 代码Agent框架设计：如何设计完备、清晰的Agent系统流程，能够完成用户真实场景中复杂的代码需求；\n2. 代码长文预训练优化：如何筛选、清洗和组织现有的海量代码数据，或通过创新的方法合成包含丰富项目级上下文的预训练数据，以提升基座模型对项目级代码的理解和建模能力；\n3. Agent指令数据的自动化合成：如何设计有效的策略，自动化合成覆盖多样场景、高质量、高复杂度的代码Agent指令数据；\n4. 前端项目评估系统建设：针对前端项目场景设计自动化的评估系统，包括渲染与视觉一致性检验、交互逻辑与功能完整性检验等，对前端编程效果的评估和指令数据的清洗进行提效；\n5. 奖励系统建设和强化学习策略优化：对于复杂的项目级编程任务，需要结合沙盒环境，设计稀疏或稠密的奖励信号以优化强化学习效果。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""大语言模型算法中心""}"
腾讯混元基座模型-强推理能力模型研究,"{""青云课题背景"": ""当大模型解决复杂问题的能力面临瓶颈，持续scaling 预训练参数规模带来的边际效益正在锐减，而扩大推理算力的PostTrain范式优化，尤其是强化学习，成为了新的破局点。\n长思维链的出现，使得大模型的推理能力从system1的快思考进化到system2的慢思考，极大的提升了模型的推理能力。通过内化工具的使用能力，进一步提升了模型的推理上限，更拓宽了模型的能力边界，真正能够解决现实复杂场景的问题，产生巨大的应用价值。"", ""青云课题价值"": ""你将参与到大模型高阶推理能力优化的全流程，包括长思维链迭代和构建、高难度理科数据挖掘&合成、强化学习策略优化、评估&生成能力循环迭代，全面提升混元大模型的通用推理能力。"", ""青云课题挑战"": ""1. 通用验证器（General Verifier）的构建与优化：针对多领域推理任务，需构建跨学科的鲁棒性基准测试集，覆盖物理、化学、生物、数学、代码等Stem学科领域的高阶推理场景，系统性评估验证器的泛化能力与抗干扰性；\n2. 思维链（CoT）质量的量化评估体系：建立多维度评估指标，包括冗余度（路径重复率、无关节点比例）、丰富度（路径多样性、中间步骤信息熵）、思考深度（逻辑树层级深度与问题复杂度相关性）；\n3. 思维链自举（Bootstrapping）的迭代优化方法：研究动态思维链生成机制，通过强化学习策略实现推理长度的自适应控制，在保证结论正确性的前提下最小化冗余token生成；\n4. 长短思维链的自适应融合机制：在训练阶段引入多任务学习策略，通过课程学习逐步增强模型对复杂问题的长链处理能力。探索分层计算资源分配方案，结合强化学习训练范式，在推理过程中实时优化长短链的协同计算路径，实现效率与精度的帕累托最优；\n5. 外部工具增强的混合推理系统：集成代码验证器（如形式化验证工具）、数学计算库（如SymPy）及领域知识库，实现中间步骤的自动验证与外部知识检索。设计动态工具调用策略，通过基于大语言模型的任务分解模块识别需工具介入的子问题，提升系统最终的复杂问题解决能力。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""大语言模型算法中心""}"
腾讯混元基座模型-大模型开放性前沿技术探索,"{""青云课题背景"": ""大模型过去几年里取得了巨大的进步，但仍需在智能深度和广度上进一步突破，本课题将主要面向大模型的前沿开放性技术展开探索。"", ""青云课题价值"": ""大模型既需要在现有技术范式下脚踏实地提升模型的能力，也需要继续探索下一阶段的新范式，解锁智能的深度和广度上界。本课题主要面向大模型进行新技术攻坚。"", ""青云课题挑战"": ""关于大模型的种种开放性的问题，亟需有激情、乐于共同面对挑战的同学来一起持续探索。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""混元X组""}"
腾讯混元基座模型-大模型基础能力技术研究,"{""青云课题背景"": ""探索stem和长文的能力上限。"", ""青云课题价值"": ""我们会不断探索大模型的能力上限，期待你一起加入探索不同topic，包括但不限于：文创领域，在无标准答案情况下，通过强化学习拉升模型效果；推理模型的思维链进化提升；快慢思考的高效结合。"", ""青云课题挑战"": ""1. 当前长cot的方式，面对cot过长的问题，如何在保证指标的前提下，让cot更短；\n\n2. stem难题解决率还需要提高；\n\n3. 大于256k的上下文，效果衰减大。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""基础算法中心""}"
腾讯混元大模型-大模型业务应用技术研究,"{""青云课题背景"": ""随着人工智能技术的快速发展，大模型已成为推动各行业智能化升级的核心驱动力。然而，在实际业务场景中，企业面临的问题往往呈现出高度复杂性与多样性，需要不断提升通用大模型在业务场景的能力天花板。此外，单一的大模型难以高效解决复杂业务问题，多Agent技术的引入，为突破这一困境提供了新方向。\n\n多个智能体可以根据自身特性和分工，协同调用大模型能力，实现复杂任务的拆解与处理。因此，深入研究大模型在业务场景的效果优化、多Agent技术的应用，探索复杂问题上的技术解决路径，对提升企业业务效率、推动产业智能化变革具有重要的现实意义与广阔的应用前景。"", ""青云课题价值"": ""大模型应用已进入深水区，如何保障基础模型通用能力的情况下，持续提升业务特定能力，是当前业界难题。我们有翻译、代码生成等丰富多元的应用场景，期待你一起深度参与模型迭代，一起助力业务腾飞，让大模型创造更多价值。"", ""青云课题挑战"": ""1. 通用大模型业务场景适配性不足：通用大模型虽具备强大基础能力，但难以直接满足多样化业务场景的特殊需求，同时其缺乏对特定业务场景的深度理解与优化，导致在实际应用中难以达到业务所需的性能标准。如何在不降低大模型通用能力的基础上，将通用大模型高效适配到细分业务场景，是极具挑战性问题；\n\n2. 复杂问题拆解与整合的技术瓶颈：复杂业务问题涉及多领域知识融合、多步骤处理，在拆解为子任务时，缺乏科学合理的拆解标准和方法，易导致子任务划分不合理，增加智能体协同难度；而在子任务完成后，将各部分结果整合为最终解决方案时，也面临着一系列技术挑战，难以保证复杂问题解决的完整性和准确性 。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""模型应用中心""}"
腾讯混元基座模型-混元视觉理解大模型研究,"{""青云课题背景"": ""视觉理解能力是大模型感知真实世界和走向物理世界的基础，混元的视觉理解大模型能力长期位居业界第一梯队。本青云课题主要探索面向未来的视觉理解技术，包括O3类图像推理方法，视觉Agents能力等。"", ""青云课题价值"": ""本课题的目标是持续提升视觉理解模型的能力，包括全面性和可靠性，以及探索面向未来的视觉理解技术，包括类O3的推理能力建设，以及视觉Agents能力等。视觉理解模型有着广阔的应用前景，例如在广告理解、短视频分析等司内场景中正发挥着较大价值，是原生大模型APP例如元宝的重要组成部分，也使得未来机器人看懂这个世界成为可能。"", ""青云课题挑战"": ""目前整个领域的视觉理解能力在全面性和可靠性上还有较大的提升空间，如何进一步提升能力以及展现新的特性仍旧是一个开放性问题。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""视觉大模型算法中心""}"
腾讯混元大模型-基于Agent技术构建深度研究(Deep Research)智能体的调研,"{""青云课题背景"": ""随着大语言模型技术发展，大家对LLM能解决复杂问题预期越来越高, 很多深度研究(Deep Research)类产品不断涌现。传统研究工作多依赖于固定算法编排，难以适应复杂、动态的研究环境。Agent技术能够模拟人类研究者的思维过程，凭借其自主决策、环境交互和持续学习能力，逐渐成为推动各类复杂任务高效执行的关键技术手段。"", ""青云课题价值"": ""你将参与到Deep Research类Agent的模型结构、决策算法、学习策略以及与复杂环境的交互机制的研究，以提升智能系统在复杂场景下的深度分析、创新探索、解决问题等各方面能力，推动混元大模型的Agent技术在更广泛场景中的深度应用。"", ""青云课题挑战"": ""1. Deep Research类的智能体能力的合理评价；\n2. 提升模型的知识推理能力，交互式长思维链能力能稳定且准确的触发；\n3. 提升模型的工具调度及适配能力，满足各类复杂场景下的问题解决能力。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""搜索算法中心""}"
腾讯混元大模型-基于大模型重构生成式搜索系统研究,"{""青云课题背景"": ""随着大语言模型（LLM）的快速发展，用户搜索需求从​​关键词匹配​​转向​​深度理解​​（如问答、多轮交互），传统检索技术难以适应。\n​​重构必要性​​：当前搜索系统依赖​​倒排索引+判别式向量解锁，语义理解能力受限，而LLM可增强语义解析、生成式回答和多模态交互能力。\n​​研究内容​​：本课题聚焦​​大模型重构搜索系统​​，希望推动搜索技术智能化，赋能智能问答、知识图谱等应用，助力企业和研究机构建立技术壁垒。"", ""青云课题价值"": ""你将参与到使用大模型全链路优化信息检索系统，包括生成式检索，大模型标注，以及面向信息检索各类预训训练、后训练技术中。"", ""青云课题挑战"": ""1. 数据挑战​​：标注成本高​​：需高效生成LLM训练数据，探索​​自动化标注​​（如Prompt Engineering、小样本学习）、​​弱监督方法​​或​​用户行为数据挖掘​​。​​数据多样性​​：覆盖长尾查询、时效性内容（新闻、热点）等多样化场景；\n​​2. 技术挑战​​：​​召回阶段​​：如何平衡​​向量检索（语义）​​与​​倒排检索（精确匹配）​​，确保低延迟、高召回率；\n​​排序阶段​​：LLM排序计算成本高，需优化模型轻量化（如蒸馏、稀疏化）或混合排序策略；\n​​多模态处理​​：若涉及图片/视频检索，需解决跨模态语义对齐问题；\n​​3. 系统挑战​​：性能与效率​​：大规模索引（如10亿+文档）的实时检索和排序延迟优化；\n​​系统扩展性​​：兼容传统搜索架构，支持灰度发布和渐进式升级。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""搜索算法中心""}"
腾讯混元基座模型-端到端语音大模型研究,"{""青云课题背景"": ""语言大模型正在重塑语音和音频领域，ASR，TTS，语音通话，同声传译等在语言大模型的加持下相比上一代模型取得了跨越式发展。"", ""青云课题价值"": ""本课题围绕语音和音频大模型进行相关的研究探索，包括但不限于encoder/decoder研发，通用tokenizer，带上下文的ASR，基于LLM的TTS，低延时语音通话，音频音乐分析和生成等，这些问题均有着较大的应用价值，且语音是与大模型交互最自然的方式。"", ""青云课题挑战"": ""ASR，TTS和语音通话的准确性还不够高。如何进一步提升语音的智能性和拟人性也有较大的挑战性。如何同时做好理解和生成，以及语音、通用音频和视频值得进一步研究。"", ""BG"": ""TEG"", ""部门"": ""大语言模型部"", ""中心"": ""语音大模型算法中心""}"
腾讯混元多模态大模型-3D生成大模型研究,"{""青云课题背景"": ""多模态大模型已成为人工智能领域的核心战略方向。腾讯混元多模态模型部作为公司技术创新的前沿阵地，聚焦图像、视频、3D、数字人等多模态生成技术的前沿研究与技术突破，致力于构建具备原生多模态能力的基础模型体系。腾讯混元自研图像/视频/3D生成模型，生成效果处于业界第一梯队，在业界与开源社区有着巨大影响力，是腾讯大模型领域的核心价值体现，并在腾讯丰富的业务场景里得到了落地验证和价值收益。"", ""青云课题价值"": ""3D资产生成广泛用于游戏、工业设计、XR等各领域，但传统建模面临价格高昂、效率低的挑战。通过对3D资产生成大模型、3D管线配套模型的深入研发，可用生成式AI技术重塑CG管线，为游戏、虚拟社交等领域带来巨大价值。"", ""青云课题挑战"": ""研发3D资产生成大模型技术：\n1. 深入研究3D大模型预训练、后训练方法和模型架构，提升生成质量；\n2. 深入研究3D可控生成技术和架构，提升3D资产生成的可控性、可编辑性。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""3D生成中心""}"
腾讯混元多模态大模型-3D场景生成与交互大模型研究,"{""青云课题背景"": ""多模态大模型已成为人工智能领域的核心战略方向。腾讯混元多模态模型部作为公司技术创新的前沿阵地，聚焦图像、视频、3D、数字人等多模态生成技术的前沿研究与技术突破，致力于构建具备原生多模态能力的基础模型体系。腾讯混元自研图像/视频/3D生成模型，生成效果处于业界第一梯队，在业界与开源社区有着巨大影响力，是腾讯大模型领域的核心价值体现，并在腾讯丰富的业务场景里得到了落地验证和价值收益。"", ""青云课题价值"": ""对三维世界的模拟是世界模型的基石，也是游戏、具身智能仿真等各领域的刚需技术。通过3D场景生成大模型、场景交互大模型研究，构建领先的三维世界生成与交互技术，助力空间智能发展，推动游戏等各行业应用。"", ""青云课题挑战"": ""研发3D场景生成大模型、VLA交互大模型，解决如下技术挑战：\n1. 研究3D场景生成模型架构和训练策略，提升视觉质量、速度、物理合理性；\n2. 研发可控的VLA交互大模型架构和方法，降低交互成本、提高交互质量。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""3D生成中心""}"
腾讯混元多模态大模型-图像生成基础模型,"{""青云课题背景"": ""多模态大模型已成为人工智能领域的核心战略方向。腾讯混元多模态模型部作为公司技术创新的前沿阵地，聚焦图像、视频、3D、数字人等多模态生成技术的前沿研究与技术突破，致力于构建具备原生多模态能力的基础模型体系。腾讯混元自研图像/视频/3D生成模型，生成效果处于业界第一梯队，在业界与开源社区有着巨大影响力，是腾讯大模型领域的核心价值体现，并在腾讯丰富的业务场景里得到了落地验证和价值收益。"", ""青云课题价值"": ""图像生成大模型代表着腾讯核心技术体系。混元图像生成开源了首个中文原生DiT架构，其相关DiT生态衍生模型数量处全球第一梯队。混元图像生成2.0模型是业界首个工业级的实时生图模型，带来了交互体验的变革，为业务提供夯实的大模型底座能力。\n当前混元聚焦下一代图像生成模型升级迭代，是本课题的核心价值。"", ""青云课题挑战"": ""预训练、后训练算法研究、编解码器研究、模型基础架构设计、加速算法研究\n例如：生成模型奖励函数设计与研究等。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""基础模型中心""}"
腾讯混元多模态大模型-视频生成基础模型,"{""青云课题背景"": ""多模态大模型已成为人工智能领域的核心战略方向。腾讯混元多模态模型部作为公司技术创新的前沿阵地，聚焦图像、视频、3D、数字人等多模态生成技术的前沿研究与技术突破，致力于构建具备原生多模态能力的基础模型体系。腾讯混元自研图像/视频/3D生成模型，生成效果处于业界第一梯队，在业界与开源社区有着巨大影响力，是腾讯大模型领域的核心价值体现，并在腾讯丰富的业务场景里得到了落地验证和价值收益。"", ""青云课题价值"": ""视频生成大模型代表着腾讯核心技术体系，混元视频是首个参数量超过 100 亿的开源视频生成模型，性能与闭源商业模型媲美，是开源社区最受欢迎的视频模型之一。\n当前混元聚焦下一代视频生成模型升级迭代，是本课题的核心价值。"", ""青云课题挑战"": ""预训练、后训练算法研究、编解码器研究、模型基础架构设计、加速算法研究\n例如：可交互式视频生成研究、实时化视频生成模型研究等。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""基础模型中心""}"
腾讯混元多模态大模型-原生多模态基础模型,"{""青云课题背景"": ""多模态大模型已成为人工智能领域的核心战略方向。腾讯混元多模态模型部作为公司技术创新的前沿阵地，聚焦图像、视频、3D、数字人等多模态生成技术的前沿研究与技术突破，致力于构建具备原生多模态能力的基础模型体系。腾讯混元自研图像/视频/3D生成模型，生成效果处于业界第一梯队，在业界与开源社区有着巨大影响力，是腾讯大模型领域的核心价值体现，并在腾讯丰富的业务场景里得到了落地验证和价值收益。"", ""青云课题价值"": ""原生多模态是大模型领域的未来方向，是混元大模型的战略重点。大模型将从单一模态走向多模态交织，从单一任务走向原生多任务合一，大模型将真正成为智能的生产力伙伴。\n原生多模态大模型不仅代表着腾讯核心技术体系，也在腾讯丰富业务场景中得到落地和价值体现。"", ""青云课题挑战"": ""预训练、后训练算法研究、编解码器研究、模型基础架构设计、加速算法研究\n例如：原生多模态模型基础架构设计、理解生成一体化编解码器研究、多任务统一后训练算法研究等。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""基础模型中心""}"
腾讯混元多模态大模型-高品质3D数字人动作驱动算法研究,"{""青云课题背景"": ""随着虚拟现实、影视特效、游戏开发等领域的快速发展，3D数字人的动作驱动技术已成为行业核心需求，在动作捕捉、动作生成、AI驱动等方面存在很多技术挑战。"", ""青云课题价值"": ""当前学术界的3D数字人动作驱动相关的研究成果与工业级应用标准仍存在显著差距，存在动作生成质量不足、细节与真实感缺失、不符合人体动力学等物理约束、重定向算法会导致动作品质下降等挑战。业界亟需在3D数字人动作驱动方向（包含动作捕捉及动作生成等方向）面向工业级应用的研究，突破现有技术瓶颈，算法能产出达到工业级可用品质的动作数据。"", ""青云课题挑战"": ""当前学术界的3D数字人动作驱动相关的研究成果与工业级应用标准仍存在显著差距，具体表现为：\n1. 动作生成质量不足：现有算法生成的动画普遍存在抖动、滑步、肢体运动不符合物理规律等问题，难以满足工业应用的流畅性要求；\n2. 细节与真实感缺失：动作的力量感、关节联动、发力及传递等细节难以还原，导致数字人表现力不足；\n3. 物理仿真与动作解耦困难：现有方法难以在动作生成中动态平衡物理约束与艺术表达形态美观的需求，导致动作僵硬或不自然；\n4.  重定向导致动作品质下降：现有重定向工具在没有美术人员人工介入的情况下会导致动作品质下降，难以满足高保真实时驱动的要求。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""数字人技术中心""}"
腾讯混元多模态大模型-数字人多模态交互算法研究,"{""青云课题背景"": ""随着AIGC与虚拟人技术的快速发展，数字人正从“功能助手”逐步向“情感伴侣”演进，越来越多地参与到用户日常生活、心理慰藉、社交陪伴等场景中。当前主流数字人系统在任务完成度、对话流畅性等方面已达较高水平，但在构建持久稳定的用户情感连接、塑造具有温度的交互体验方面仍存在显著不足。"", ""青云课题价值"": ""未来的数字人应具备持续陪伴能力，能够理解用户的情绪、记住用户的习惯与经历，并通过语言、语音、表情、行为等多模态方式实现更自然、可信、有温度的交流。本课题聚焦于“具备高情商与情感连接能力的数字人系统”建设，目标是通过AI能力突破，打造真正意义上的“陪伴型AI”。"", ""青云课题挑战"": ""具备高情商与情感连接能力的数字人系统在语言、语音、表情/动作上都有很大挑战，具体表现为：\n1. 语言：缺乏长期记忆、个性理解能力有限、难以感知用户情绪变化、交互目标短视等问题；\n2. 语音：传统ASR和TTS缺少细粒度的情感感知和生成能力；\n3. 表情/动作：表情和动作的驱动在交流中难以保持情感和语义的连贯性。\n建议研究方向：\n1. 个性化长期认知系统：构建可长期维护和动态更新的用户认知模型，支持用户偏好、行为习惯、对话历史等个性要素的持续感知与表达；\n2. 长期规划能力建模：基于陪伴、信任等长期交互目标，构建长期行为规划模型，提升数字人在陪伴场景中的真实性和黏性；\n3. 自进化与自我优化机制：探索通过强化学习、人类偏好反馈等方法，推动数字人对自身交互短板的识别与进化，持续提升其在开放式、多轮、复杂情绪等场景下的应对能力；\n4. 拟人化语音风格：探索如何通过语音语调、语速、情绪表达等方式建立具人格特质的语音交互系统，提升数字人在语音交互中的温度感、陪伴感与亲和力；\n5. 基于语义的多模态驱动：探索如何实现数字人多模态AI驱动像真人一样呈现情感语言语言、表情动作的协调一致性。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""数字人技术中心""}"
腾讯混元多模态大模型-视觉生成模型应用研究与实践-图像视频生成模型,"{""青云课题背景"": ""多模态大模型已成为人工智能领域的核心战略方向。腾讯混元多模态模型部作为公司技术创新的前沿阵地，聚焦图像、视频、3D、数字人等多模态生成技术的前沿研究与技术突破，致力于构建具备原生多模态能力的基础模型体系。腾讯混元自研图像/视频/3D生成模型，生成效果处于业界第一梯队，在业界与开源社区有着巨大影响力，是腾讯大模型领域的核心价值体现，并在腾讯丰富的业务场景里得到了落地验证和价值收益。"", ""青云课题价值"": ""视觉生成（图像，视频）模型在真实图片生成，虚拟图片生成，及风格化图片生成上均具有极强的竞争力，并具备极高的可控性和扩展性。业界不断推出新结构的模型，推动了生成效果的持续提升。\n腾讯在广告、社交、游戏、内容等多个商业领域对视觉生成模型应用均有强烈的诉求和依赖。更优的生成模型能为腾讯多个业务场景业务应用带来显著的收益。"", ""青云课题挑战"": ""图像/视频生成模型应用研究与实践\n例如：深度思考和强化学习在视觉生成模型中的研究和应用、多模态信号组合注入的可控视觉生成模型研究等。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""应用模型中心""}"
腾讯混元多模态大模型-视觉生成模型应用研究与实践-视觉生成Agent,"{""青云课题背景"": ""多模态大模型已成为人工智能领域的核心战略方向。腾讯混元多模态模型部作为公司技术创新的前沿阵地，聚焦图像、视频、3D、数字人等多模态生成技术的前沿研究与技术突破，致力于构建具备原生多模态能力的基础模型体系。腾讯混元自研图像/视频/3D生成模型，生成效果处于业界第一梯队，在业界与开源社区有着巨大影响力，是腾讯大模型领域的核心价值体现，并在腾讯丰富的业务场景里得到了落地验证和价值收益。"", ""青云课题价值"": ""视觉生成（图像，视频）模型在真实图片生成，虚拟图片生成，及风格化图片生成上均具有极强的竞争力，并具备极高的可控性和扩展性。业界不断推出新结构的模型，推动了生成效果的持续提升。\n腾讯在广告、社交、游戏、内容等多个商业领域对视觉生成模型应用均有强烈的诉求和依赖。更优的生成模型能为腾讯多个业务场景业务应用带来显著的收益。"", ""青云课题挑战"": ""视觉生成Agent模型应用研究与实践\n例如：\n1. 视觉生成领域Agent的研究（动漫，游戏，广告等）；\n2. 理解生成一体化的端到端视频对话的研究和应用。"", ""BG"": ""TEG"", ""部门"": ""多模态模型部"", ""中心"": ""应用模型中心""}"
腾讯广告模型-业务应用技术研究,"{""青云课题背景"": ""腾讯广告场景的模型种类较多，并且广告链路对模型推理性能及稳定性的要求非常严格。同时，随着大语言模型在生成式AI方向的成功，工业界和学术界都开始探索生成式模型在推荐场景的可行性和效果收益。因此，广告的推荐模型在多样性，复杂度等方面进行着快速的迭代和演进。本课题旨在研究复杂推荐模型的通用性编译优化能力，生成式模型的推理加速能力等，对传统推荐模型推理引擎和大语言模型推理引擎进行取长补短，在推理性能、模型迭代周期、易用性、通用性等方面对推理引擎进行优化升级，推动相关创新模型在腾讯广告场景的应用落地。"", ""青云课题价值"": ""通过该课题的研究，希望能解决以下几个业界的痛点问题：\n1. 基于定制算子和子图的模型编译系统虽然具备较好的性能，但通用性和扩展性较差，在模型迭代效率上存在较大问题；\n2. 基于规则或表达式的模型编译系统在通用性和扩展性上具备天然优势，但通常情况下发挥不出硬件的极致性能；\n3. 针对生成式推荐模型的推理引擎，目前还处于比较初级的探索阶段，在推理耗时和模型规模的探索上，还存在比较大的研究空间。"", ""青云课题挑战"": ""1. 在通用编译系统下挑战极限性能：腾讯广告的流量场景多，模型迭代周期短，模型的种类及使用的算子多样，在这样的业务背景下，编译系统要具备很强的通用能力，能够通过代数表达式，拓扑规则，或者自定义IR等方法对模型进行编译优化。同时，针对不同硬件的算力，访存等限制，能够进行模型推理性能的极致优化；\n2. 面向生成式推荐场景的推理引擎设计：受限于推荐系统User/Item ID的范式，在推荐场景下的生成式模型往往还需要引入传统推荐模型中对user，item，以及相关交互特征的建模和处理。因此，相比于大语言模型，生成式推荐模型往往还具有迭代周期短，模型种类多样化的特点。同时，在推荐业务不断探索模型scaling law的驱动下，在线系统对推理引擎的性能要求和挑战也就越来越大，需要针对性地进行推理架构设计，包括TP并行，KV-cache，KV-cache aware routing等。"", ""BG"": ""TEG"", ""部门"": ""广告工程部"", ""中心"": ""模型推理中心""}"
基于大模型技术的数据智能优化技术研究,"{""青云课题背景"": ""随着大数据技术发展与业务规模的不断扩大，我们在超大规模分布式计算、数据开发提效、平台智能化等方面，面临巨大挑战。可喜的是AI 已展现了颠覆技术架构、产品形态、研发范式的能力，因此我们期待引入顶尖人才，共同构建新一代 Data+AI 基础设施。研发领先的AI 技术致力于提升大数据平台的高效性、易用性以及稳定性。"", ""青云课题价值"": ""1. 探索新一代数据分析/数据科学交互模式的AI能力：消除从理论方法研究到工业化落地的鸿沟，探索 Data+AI并落地大数据上下游，支持以自然语言的交互方式进行智能找数、取数、数据分析、洞察以及数据可视化等等；构建行业领先的大数据领域大模型，其中涉及研发和优化在意图识别、任务规划、工具选择和使用、代码生成、诊断优化、数据治理等方面的核心能力；构建大数据场景Copilot能力，提升大数据研发效率；此外还涉及模型训练与精调、提示工程、模型推理加速等技术；\n2. 利用 Data+AI 整体提升平台任务执行效率与研发效率、降低平台任务资源消耗：当前海量存量业务（千万级分布式任务/天）、多引擎（批/流）、多云场景等，亟需更加智能的、非侵入性的自动决策能力，持续提升大数据体系智能化水平，包括任务执行性能以及成本自动优化、智能存缓调度、自动化数据清洗以及特征处理等等。为了实现这些目的，需要研发高效的机器学习/深度学习、迁移学习、元学习、强化学习、优化算法、规划算法等基础AI应用技术。"", ""青云课题挑战"": ""1. 大模型技术与数据系统领域知识如何高效地结合；\n2. 大模型在AI Coding 方向上的自动化的生成、优化、诊断，同时包括在不同的引擎有特定的语法规则上处理;\n3. 大模型技术在Data Agent 特定的领域和业务完成优化后，如何快速迁移到其他业务，或提升泛化性；\n4. 结合多模态技术，理解样例数据分析报告、图表，并按照样例，在不同的数据、场景生成同类别的数据；\n5. Agent 的自优化学习、训练：尝试通过reinforce learning 的方式，对Agent进行迭代。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""基础平台中心""}"
基于大模型技术的平台诊断调优,"{""青云课题背景"": ""在数字化转型加速的时代背景下，大数据技术与 AI 的深度融合已成为企业创新发展的核心驱动力。随着业务规模的持续扩张和数据量的爆炸式增长，超大规模分布式计算、数据开发效率提升以及平台智能化管理等方面的挑战日益凸显。传统的大数据平台和机器学习平台在面对复杂多变的业务需求时，逐渐暴露出资源调度不灵活、开发周期长、智能化程度低等问题，难以满足企业快速创新和精细化运营的要求。\n近年来，人工智能技术取得了突破性进展，展现出颠覆传统技术架构、产品形态和研发范式的强大潜力。特别是在自动化优化、智能决策等领域的成功应用，为解决大数据平台面临的挑战提供了全新的思路和方法。通过引入先进的 AI 算法，可以实现对平台资源的智能感知、动态分配和精准调度，大幅提高资源利用率和系统性能；利用机器学习技术优化数据处理流程，能够显著提升数据开发效率，缩短业务响应时间；结合自然语言处理和知识图谱等技术，还可以构建智能化的交互界面和决策支持系统，降低技术门槛，让数据价值惠及更多业务人员。"", ""青云课题价值"": ""绿色计算与可持续发展\n通过资源优化减少硬件设备能耗与碳排放，践行绿色计算理念，助力 “双碳” 目标实现，推动技术发展与环境保护的协同共进。\n资源优化价值：通过智能算法动态优化资源分配，显著提升大数据与机器学习平台资源利用率，降低企业运营成本。\n效率提升价值：实现任务参数自动调优与智能调度，缩短数据处理与模型训练周期，加快业务创新迭代速度。\n技术创新价值：构建新一代 Data+AI 基础设施，推动分布式计算、智能决策等前沿技术落地，保持技术领先优势。"", ""青云课题挑战"": ""1. 通过智能算法调优CPU/GPU使用 资源、任务、参数，调度策略；\n2. 通过智能算法平衡CPU、GPU混合场景。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""基础平台中心""}"
腾讯混元-通用大模型压缩算法和并行解码研究,"{""青云课题背景"": ""设计和实现通用的大模型压缩和并行解码算法，涵盖文生文、多模态理解、多模态生成等场景，通过算法创新和算法优化保持模型效果无损，并配合推理框架团队完成加速部署落地，持续降低大模型落地部署的成本。"", ""青云课题价值"": ""通过大模型压缩和并行解码，可以在保证模型效果的同时，大幅度降低模型的推理成本，降低首字耗时，提升解码速率，最终在节省成本的同时也能提升用户体验。"", ""青云课题挑战"": ""1. 探索更低bit的压缩算法且保持精度，并推进推理框架适配落地；\n2. 在更大batch-size下的并行解码如果能获取到收益，需要更高的算法接受率并兼顾高效的并行解码kernel实现。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""基础平台中心""}"
腾讯混元-面向大模型的高性能算子开发和编译加速研究,"{""青云课题背景"": ""随着大模型参数规模和生成场景、多异构芯片的不断发展，高性能推理愈发成为PD分离架构、高性能编译和算子等系统性创新优化的趋势。构建高性能、自适应和快速影响的推理架构能力矩阵，是高效率模型开发、部署的必要条件。"", ""青云课题价值"": ""1. 从超大规模推理生产系统出发，定位系统瓶颈和机会，通过深入研究异构PD分离等架构创新，更充分利用不同规格、不同厂商的硬件资源，构建提供更稳定、更灵活、更有性价比的推理系统；\n2. 通过编译和高性能算子开发；充分挖掘各种GPU卡、异构硬件的性能；\n3.  形成编译优化、算子开发的高效方法，帮助团队快速满足来自业务、算法的性能优化、评估的需求；\n4. 通过分析、收集混元、主流开源大模型的典型计算片段；形成腾讯自己的异构硬件性能评估测试集，提升硬件引入、评估、设计时的效率和准确性。"", ""青云课题挑战"": ""1. 相对于PD混合，PD分离系统复杂度高，系统稳定性、容错、容灾要求高；\n2. PD分离和DP、EP、TP、PP、MTP等推理加速技术都有交互，开发难度大；\n3. 异构PD分离场景中，网络通讯更加复杂，各自推理框架差异较大；\n4. 高性能算子开发需要对算法、硬件结构都有深刻的理解并且需要系统的分析方法。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""基础平台中心""}"
大规模异构计算引擎融合,"{""青云课题背景"": ""设计和实现通用的超大规模计算引擎，支持流、批、交互式，增量计算，数科等大数据和AI分析场景，通过工程手段优化底层运行性能，提供高效的异构编程和运行时自适应框架，配合底层调度，算法创新持续提高异构计算的性能和易用性。"", ""青云课题价值"": ""通过设计实现通用的超大规模计算引擎、支持流、批、交互式、数科等大数据和AI分析场景，同时提供良好的自适应异构计算能力，并提供标准api提升整体性能的同时也提供用户更好的体验。"", ""青云课题挑战"": ""1. 以湖仓一体为中心的，通过Native，增量计算语义，存储/计算，软硬件协同等关键技术，构建满足数据分析与数据科学场景的通用计算引擎；\n\n2. 解决超大规模下异构计算数据交换，网络交互，以及高性能计算所涉及的指令，内存等的优化；\n\n3. 易用性​：需隐藏底层异构系统的交互细节，提供统一API支持用户使用，包括不限于SQL，dataframe 等。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""计算中心""}"
DATA+AI 统一数据湖存储引擎,"{""青云课题背景"": ""开放的湖仓架构已经成为大数据业界的标准，它能提供结构化，半结构化大规模数据的低延迟、低成本、高性能的访问能力，但是在面对 AI 数据链路中的图片、文本、音频、向量等多模态存储还未有成熟的解法，当前 AI 数据链路的存储当前较为混乱，预处理、训练、推理使用了多套存储访问，成本高企，访问低效。当前内部已在探索通过在数据湖上增强对多模态数据的存储和访问能力。"", ""青云课题价值"": ""通过探索 AI 数据链路的存储与计算模式，提供高效的多模态数据编码与压缩能力，匹配 AI 读写模式的高效索引，融合开放湖仓的读写接口，降低 AI 链路数据存储的成本，提升数据访问的效率。"", ""青云课题挑战"": ""1. 深入理解数科引擎不同阶段下访问数据的模式，例如，大模型链路预处理、训练、推理不同阶段访问数据的延迟、吞吐、性能需求；\n2. 需要掌握高效的编码，压缩技术，针对不同的多模态数据，使用适合的编码和压缩进行存储；\n3. 需要掌握和实现一些高效的索引，根据不同的引擎访问模式，设计合适的索引，增强访问性能。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""计算中心""}"
新一代多模态检索引擎,"{""青云课题背景"": ""在大模型技术演进过程中，向量检索扮演了重要的角色，为大模型提供了外置知识库作为长期记忆，能够有效缓解当前大模型普遍面临的幻觉问题，然而随着数据内容的多元化，向量数据也同时关联着大量的原始文本、数值、标签等额外的多模态信息，因此需要建立高效的多模态检索方式，保证外置知识库的查询效率和大模型的服务效率。"", ""青云课题价值"": ""探索新一代的多模态检索引擎，同时支持向量、倒排、BTree/GiST等多种索引类型，并支持不同索引类型的混合检索，形成一套完整的、高效的多模态检索系统，提升检索效率及准确度的同时，降低业务使用门槛。"", ""青云课题挑战"": ""1. 深入理解多模态检索在大模型中的应用场景，对LLM、技术栈、NLP、RAG等有深入的研究；\n2. 需要对向量、倒排等索引类型有比较深入的了解，设计自适应的混合检索执行引擎。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""计算中心""}"
异构混部技术的研究,"{""青云课题背景"": ""围绕多种类型的异构卡运行高性能计算过程中，通过研究多维策略组合的混部挖掘算力，并协助业务用好算力，从而提升更高的计算性能和能效比。"", ""青云课题价值"": ""通过深入研究异构算力卡混部技术，可以为多场景组合的高性能计算系统的优化提供重要的算力保障，优化异构计算资源之间的协同工作和任务分配问题，以实现更高效的计算资源利用和性能提升。"", ""青云课题挑战"": ""1. 异构多种卡型间混部技术的统一；\n2. 多维隔离能力、统一虚存的设计和实践；\n3. 深入内核层的修改以及反编译的能力。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""数据中心""}"
大规模资源调度技术研究,"{""青云课题背景"": ""基于联邦集群架构，研究大规模 AI 场景下资源调度能力。"", ""青云课题价值"": ""通过刻画不同任务在不同卡型的计算能力、不同任务其他依赖其他资源（包括异地存储等资源），研究一体化联邦调度方案。最终实现资源利用率和用户任务执行效率最优。"", ""青云课题挑战"": ""1. 研究跨集群场景下，计算任务跨集群、跨卡型、跨地域调度；\n2. 解决超大规模调度场景下的规模、性能问题；\n3. 多卡型算力归一化。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""数据中心""}"
训练/推理数据IO加速技术研究,"{""青云课题背景"": ""随着LLM应用数据规模急剧增长，在训练阶段显存容量与计算需求之间的差距日益扩大，传统基于CPU的存储方案依赖CPU协调数据访问，导致高同步开销和I/O流量放大，难以满足高并行吞吐需求。另外在推理的多轮对话场景，KVCache的远程卸载和重用还未大规模生产应用，存在IO性能下降的问题。"", ""青云课题价值"": ""通过研究面向GPU的数据加速方案，实现GPU对数据的管理和直接访问。通过研究文件系统抽象、跨进程共享、GPU友好的缓存、IO直通NVMe与远程存储等技术，降低数据的访问延迟，加速LLM的训练和推理。通过研究KVCache的多级缓存卸载、统一的显存内存编址、高性能硬件适配等技术，提升KVCache的复用效率。"", ""青云课题挑战"": ""​​1. 元数据同步​：需在主机与GPU文件系统间高效同步元数据，同时避免复杂控制逻辑损害GPU并行性；\n​2. 驱动限制​：现有设备驱动大多仅支持CPU独占控制，需要解决GPU内存到DMA映射问题以及与远程存储的访问问题；\n​3. 易用性​：需隐藏底层异构系统(CPU/GPU/xPU)的交互细节，为上层提供常用的接口，降低开发难度。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""数据中心""}"
腾讯混元-大规模强化学习框架性能优化研究,"{""青云课题背景"": ""强化学习已经成为提升模型效果最重要的一环，大模型强化学习框架如何构建、如何优化加速以及强化算法如何演进急需解决和深入探索。"", ""青云课题价值"": ""构建超大规模、高性能、高易用性的强化学习框架，支持同步和异步训练模式，以及支持算法强化策略自主探索，支持混元大模型的强化训练，同时基于硬件特性进行框架深层次优化，如通信和通信overlp以及通信和计算融合优化等提升强化训练效率。"", ""青云课题挑战"": ""1. 需要对强化学习解决的问题以及算法发展方向有深入的研究，更好的指导框架迭代和性能优化；\n2. 需要结合硬件特性、模型以及框架深入分析当前训练存在的性能问题，找到解决问题的方法和思路。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""太极平台中心""}"
腾讯混元-基于大模型技术的特征工程和数据工程,"{""青云课题背景"": ""传统特征工程与数据工程更多依赖领域专家经验和人工设计，挖掘和调优成本高且难以规模化，同时对于海量的非结构化数据缺乏高效的自动化挖掘和处理手段。随着大模型技术的突破，使得自动感知数据的潜在规律并提取深层次的语义特征成为可能，也是业界比较热门的探索方向。"", ""青云课题价值"": ""1. 用大模型技术提升特征全链路的效果，包括基于大模型的特征生产、处理、变换和选择；\n2. 特征自动生成和调优，显著降低特征工程的门槛和成本。"", ""青云课题挑战"": ""1. 深入理解大规模机器学习中的特征工程，同时对大模型应用有较深入理解和实践；\n2. 技术能较好的适应不同场景和任务，且相比传统方法有明显的收益。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""太极平台中心""}"
腾讯混元-大规模调度AutoScaling研究,"{""青云课题背景"": ""传统K8S的云源生调度体系，虽然支持了机器学习的基础调度能力，但对于大模型场景下的训练、推理任务的资源动态调整、工作负载降级、任务容错、任务驱逐保护及快速扩缩容等需求上，仍需技术支持。"", ""青云课题价值"": ""大规模调度AutoScaling研究的价值主要体现在资源优化、成本效益、性能保证、弹性伸缩、简化管理、支持多租户环境、适应未来需求和促进创新等方面。通过动态调整GPU资源，AutoScaling能够提高资源利用率，降低运营成本，保证任务性能，减少运维负担，公平分配资源，适应未来需求变化，并鼓励技术创新。这些优势使得AutoScaling成为大规模计算环境中不可或缺的技术。"", ""青云课题挑战"": ""1. 异构资源调度需平衡GPU型号、显存和算力差异，动态扩展时易引发资源碎片化；\n2. 弹性延迟敏感，GPU实例冷启动耗时（镜像加载、驱动初始化）可能破坏训练连续性；\n3. 成本与效率权衡，突发负载需快速响应，但过度配置会导致高昂费用；此外，分布式训练协同要求扩缩时保持通信拓扑稳定，避免梯度同步中断；\n4. 多租户隔离下，共享GPU资源需解决配额抢占和性能干扰问题。现有调度器（如K8S+Device Plugin）在细粒度弹性伸缩和拓扑感知方面仍有不足，需结合动态负载预测与低延迟编排技术优化。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""太极平台中心""}"
腾讯混元-大模型在线推理流量调度研究,"{""青云课题背景"": ""传统的推理流量调度体系虽然具备按业务优先级进行调度的能力，但在大模型推理场景下面临新的挑战。与传统服务不同，大模型推理请求通常分为 prefill 和 decode 两个阶段，尤其是 decode 阶段可能涉及上千次 token 的逐步生成，推理过程更为复杂且资源消耗模式差异显著。现有的流量调度机制难以充分适应大模型推理的阶段性特征和高并发需求，因此亟需结合大模型推理的实际特点，构建统一、高效的调度体系，以提升整体服务能力和资源利用效率。"", ""青云课题价值"": ""大模型推理流量调度系统通过对 prefill 和 decode 阶段流量的动态感知与精细化调度，能够根据推理过程中的 cache 利用、batch 组建、上下文长度等关键因素，结合业务优先级，实现对每个请求的智能分配和实例选择。该系统不仅显著提升了资源利用率，用更少的算力支撑更多的业务请求，还能保障高优先级业务的服务质量（SLA），为大模型推理服务的规模化、低成本和高可靠性提供坚实支撑。"", ""青云课题挑战"": ""大模型在线推理流量调度面临多重挑战：高吞吐与低延迟的平衡需优化动态批处理策略；异构请求处理需应对不同模型、输入长度和SLA要求；资源弹性与成本控制依赖精准负载预测以避免GPU扩缩延迟和闲置浪费；故障容错需保障分布式推理的稳定性；多租户隔离需解决资源抢占和公平性问题。现有调度策略在动态自适应性和细粒度资源分配上仍有不足，需结合实时监控与智能优化算法提升效率。"", ""BG"": ""TEG"", ""部门"": ""机器学习平台部"", ""中心"": ""太极平台中心""}"
营销、数字人领域大模型算法研究,"{""青云课题背景"": ""随着直播电商、游戏、虚拟互动等领域的快速发展，数字人技术逐渐成为提升内容生产效率、降低人力成本的重要工具；营销领域大模型在自动化生成营销方案、辅助产品经理决策中的作用日益关键。"", ""青云课题价值"": ""本研究致力于：\n1. 构建数字人资产标准化体系与全生命周期管理算法，旨在通过制定数据采集、模型结构、动作骨骼等环节的标准化规范，并扩展至服装、发型、妆容、脸型等视觉资产的标准化建模与存储，建立高效、可扩展的数字人资产管理平台，解决传统技术中资产互操作性差、迭代成本高等问题。研究融合多模态数据增强、迁移学习与自动化测试技术，形成多维度数字人特征库，实现资产快速适配与风格迁移，提升数字人多场景应用能力。研究成果可显著缩短数字人开发周期，降低运维成本，并推动直播电商、智能客服等领域的规模化、智能化升级；\n2. 研究LLM大模型与RAG框架，构建需求归纳与策略验证的双向推理闭环，使生成的营销方案（如促销活动设计、KOL合作策略）在逻辑推导路径上与产品经理的决策思维深度匹配，提升方案的可执行性。通过多目标优化机制与思维链提示技术，将市场数据（竞品案例、用户行为）与营销知识库（定价策略、渠道规则）动态融合，解决传统方案中“数据-逻辑-经验”三者的割裂问题，使创意新颖性与用户契合度突破。该框架为营销自动化提供可解释的AI决策支持，推动营销策划从“经验驱动”向“数据-逻辑协同驱动”转型，可扩展至营销活动页面生成、策略设计等多个场景。"", ""青云课题挑战"": ""1. 如何构建高效的万级数字人资产并行管理体系，以优化工程化效率与资源分配，同时支持服装、发型、妆容等视觉资产的快速检索与组合；如何突破数据碎片化问题，提升多模态数据语音-表情-动作-外观的协同标注与持续迭代能力；如何研发多模态协同生成算法，实现语音、视觉与情感计算的深度融合，提升交互体验；以及如何结合生成式AI技术优化营销素材生成，缩短开发周期并降低制作成本。此外，还需针对直播电商等特定场景，优化定制化效率，并推动商业化落地，实现技术与市场需求的深度结合；\n2. 策略建模的核心在于深度解析产品经理的分层决策逻辑，并结合不同品类的特性进行适配性设计；当前数据融合面临关键瓶颈，需借助对比学习方法融合市场知识库与领域规则库，以缓解数据稀疏带来的策略偏差问题；人机协同对齐需通过思维链提示与模型精调实现策略推导与产品经理思维模式的精准契合，确保策略能够动态适应市场变化；工程化落地需重点突破低延迟推理性能与多模态验证机制，同时提升创意生成的多样性水平，并着力解决数据碎片化对多模态标注流程造成的制约。"", ""BG"": ""TEG"", ""部门"": ""计费平台部"", ""中心"": ""支付平台中心""}"
基于AI大模型的数据库自学习优化器方向研发和探索,"{""青云课题背景"": ""通过AI大模型技术应用在数据库查询优化器中，实现数据库生成最优执行计划，从而达成混合负载下性能最优。"", ""青云课题价值"": ""查询优化器是数据库的核心组件，其产生的查询计划的质量直接决定了资源开销和查询时延。为提高计划准确性，优化器亟待解决基数估计不准确、计划错误不感知、搜索空间不完备等问题。本课题旨在探索AI大模型结合查询优化器产生最优计划，研究模型的持续演进、混合负载下的查询过滤机制和模型泛化性增强等问题。"", ""青云课题挑战"": ""1. 模型持续演进问题：探究在动态负载下维持优化效果的模型演进方法，应对长时间跨度场景下中可能出现的数据漂移等问题；\n2. 混合负载下的查询过滤机制：探究混合负载下的查询过滤机制，在降低推理开销的同时保证模型的充分训练和对负载变化的充分感知；\n3. 模型泛化性增强技术：探究模型的泛化性问题，探索不同负载类型之间实现模型迁移与预训练的有效途径，降低模型训练成本并提高模型在多样化负载下的适应性；\n4. LLM赋能小模型：利用LLM积累的查询优化经验补齐小模型短板，结合联邦学习、持续学习、RAG等技术，实现更精准的计划选择。"", ""BG"": ""TEG"", ""部门"": ""数据库研发部"", ""中心"": ""云原生数据库研发中心""}"
腾讯混元大模型-智能标注算法,"{""青云课题背景"": ""大模型标注是大模型应用精调的必要手段，当前大模型应用标注数据规模爆炸式增长，标注精度要求严苛，大模型对数据质量敏感，标注错误可能导致模型产生幻觉、偏见或逻辑错误；同时，标注的场景复杂多样涉及多模态数据（文本、图像、语音等）和复杂任务（实体关系抽取、事件中的幻觉检测等等），传统规则化标注工具难以覆盖。\n智能标注能力通过机器学习、深度学习技术（如 NLP、CV、语音识别等），实现数据标注的自动化、半自动化和智能化，达到效率提升（通过预先标注，以及主动学习优化，减少无效劳动、质量保证（通过规则和模型双重校验保证一致性和可靠性提升）以及优化成本的目标。"", ""青云课题价值"": ""智能标注能力是大模型时代数据生产的 “基础设施”，其核心价值在于平衡效率、质量与成本，解决传统人工标注难以应对的海量、复杂、高精度需求。随着大模型与标注技术的深度融合，未来的标注流程将更趋近于 “机器主导初标，人类聚焦决策” 的高效协作模式，持续推动 AI 训练进入 “快车道”。"", ""青云课题挑战"": ""1. 小样本 / 零样本标注的泛化能力不足：传统智能标注依赖大量标注数据训练模型，但在稀缺领域（如罕见病医疗数据、小语种方言）或新兴场景（如突发公共事件相关文本）中，标注样本极度匮乏，导致模型泛化能力差；\n2. 复杂语义理解的标注歧义性：自然语言中存在大量语境依赖、隐喻、反讽等复杂语义，智能标注模型难以精准捕捉；\n3. 多模态数据的时序等对齐：视频等多模态数据需同步标注视觉、听觉、时序等多维度信息，传统单模态标注模型难以处理跨模态关联；\n4. 标注模型的可解释性与可信度评估：深度神经网络的 “黑箱” 特性导致智能标注结果难以追溯逻辑，人工标注员难以信任模型输出。"", ""BG"": ""TEG"", ""部门"": ""数据平台部"", ""中心"": ""评测与标注中心""}"
腾讯混元大模型-大模型评测研究及应用,"{""青云课题背景"": ""大模型评测面临多维度挑战：百科全书式的知识覆盖面，包括数学、推理、专业领域、知识问答等等；模型能力的日益提升甚至超越普通本科生水平，例如在数学领域大模型已经可以解决IMO竞赛级别的难题；数据污染问题突出，训练集与测试集的重叠可能导致性能虚高。此外，多模态融合任务（如图文推理）需跨模态评估框架，传统基准难以覆盖。\n针对上述挑战，可采用以下技术路径：数据污染检测技术：通过字符串匹配、嵌入相似度分析及大模型辅助检测（如 LLMSanitize），识别训练集与测试集的重叠，确保评估公正性；多模态评估框架的构建，采用 MM-Vet 等标准，定义识别、OCR、空间感知等核心能力;AI 辅助评估与可解释性研究：引入大模型作为评估器（如 Agent-as-a-Judge），同时开发可解释性指标，衡量模型推理过程的透明度与可信度。腾讯依托全球领先的海量数据与全栈AI能力，从基础研究到产业落地，期待青云人才的加入，持续突破智能边界。"", ""青云课题价值"": ""全面的能力评测是推动人工智能技术发展的核心引擎，在技术演进、产业落地等方面发挥着重要作用。通过系统性评测，可量化模型的知识覆盖、推理逻辑、多模态处理等核心能力，同时发现模型的不足，牵引模型快速迭代。"", ""青云课题挑战"": ""1. 智能体能力评测：智能体具备自主性、复杂性和开放性，需在动态变化的环境中完成复杂任务，如跨设备协作（同时操作电脑和手机发送信息）或多模态交互（文本、图像、音频处理），并且需要考虑功能、效率、安全等多维度内容。随着技术发展，评测需从静态测试转向动态实战演练，结合红队测试、合成数据生成等创新方法，并建立持续迭代的测评机制，来全面评估智能体能力；\n2. 大模型AGI能力评测：AGI能力的全面评测非常困难，例如重要能力维度包括多领域知识融合、自主适应与学习、高级认知推理、情感理解及社会交互等等，同时每个能力维度的定义、体系设计、评测题生产、评测标准、评测质量都非常复杂。"", ""BG"": ""TEG"", ""部门"": ""数据平台部"", ""中心"": ""评测与标注中心""}"
腾讯混元大模型-大模型数据网页内容理解和质量算法研究,"{""青云课题背景"": ""随着多模态生成大模型的快速发展，其对训练数据的覆盖广度、量级规模和质量标准提出了更高要求：在数据覆盖方面，模型需要海量跨模态、跨领域的数据以实现通用能力；在数据量级上，训练需求呈指数级增长，亟需高效的数据供给方案；而在数据质量层面，则要求保证数据完整性、低重复、高优质等。腾讯依托亿级场景打磨的数据引擎与AI基础设施，持续推动技术革新，诚邀全球顶尖人才共同构建智能时代的下一代技术标杆。"", ""青云课题价值"": ""在大模型时代，算力和数据是大模型最重要的两个要素。其中数据的质量和数量覆盖能力直接大模型效果。企业拥有的是数据规模能力，实验室优势在于前沿技术的探索，通过规模和技术的合作，可以实现大模型在更多应用场景上的落地。"", ""青云课题挑战"": ""1. 规模大，中英文数据整体量级数千亿，且涉及的媒介和载体内容复杂包括不限于（文字、图片、音频、视频、3D等模态）;\n2. 格式复杂，面对数千亿网页规模和数千万的网站类型，且网页的布局和架构各异，需要从复杂多变的网页中区分出不同类型和质量的内容；\n3. 要求高，在整体的算法质量要求上要做到准召90%+。"", ""BG"": ""TEG"", ""部门"": ""数据平台部"", ""中心"": ""数据工程中心""}"
腾讯混元大模型-跨模态数据检索与分层聚类算法研究,"{""青云课题背景"": ""随着多模态生成大模型的快速发展，跨模态的需求日益增长，亟需构建跨模态（文本、图像、音频、视频、3D 等）的数据和算法能力。构建领域细分的细粒度多模态知识图谱，为跨模态大模型提供领域完备且可置信的知识储备。针对庞大的跨模态数据，探索跨模态数据层次化聚类方法，支持数据透视、数据分布调优、检索增强生成 RAG 等。基于混合模态大模型基座构建跨模态统一向量化模型，支持跨模态间语义互检索、超模态检索，为下一代大模型进行技术探索和储备。腾讯以亿级场景打磨的数据引擎与AI底座，始终引领技术革命浪潮，诚邀青云人才共筑智能时代的下一个里程碑。"", ""青云课题价值"": ""实现业界领先的跨模态能力探索和落地，通过构建跨模态知识图谱，完成高效的跨模态实体识别/链指归一化，实现基于视觉信息的、跨模态的实体关系/属性挖掘关键算法研究。同时实现以事实实体为主干、联合实体相关的描述文本、图像、音频、视频、3D 等跨模态对齐数据，形成普适的层次化聚类体系，构建跨模态（文本、图像、音频、视频、3D 等）统一向量化模型，建设完备的跨模态数据挖掘和透视能力。"", ""青云课题挑战"": ""1. 模态异质性：不同模态数据（如文本与音频）的底层特征分布差异显著，直接映射难以捕捉高阶语义关联；\n2. 语义鸿沟：跨模态数据的局部语义对齐不足，例如音频细节与文本描述的松散对应；\n3. 评估体系与可信验证：现有指标无法全面评估合成数据的任务适配性。需构建多维度评估框架（如主体一致性、事件联系性），并引入可解释性技术追踪数据问题源头；\n4. 计算效率与成本：跨模态推理和检索规模庞大，层次化复杂，需探索高效的增量优化策略。"", ""BG"": ""TEG"", ""部门"": ""数据平台部"", ""中心"": ""数据挖掘中心""}"
腾讯混元大模型-数据合成技术与数据价值衡量方法研究,"{""青云课题背景"": ""随着大模型的快速发展，模型对训练数据需求呈现指数级增长，对数据的结构与质量要求显著提升，这些数据往往难以从公开数据中直接获取，数据合成被认为是解决该问题的关键技术路径。同时，传统依赖海量堆叠的方式难以持续提升模型性能，如何合理配比不同类型的数据、构建高效的数据结构成为关键。数据的价值归因也是指导训练数据构建的关键，数据实验的设计是实现价值归因的重要手段，促进大模型高效的迭代优化。腾讯依托全球领先的海量数据与全栈AI能力，从基础研究到产业落地，期待青云人才的加入，持续突破智能边界。"", ""青云课题价值"": ""在大模型时代，数据的质量和数量直接决定大模型的能力上限。通过构建跨领域、多任务的数据配比策略，能够显著提升模型的均衡性和泛化能力；通过数据合成与数据实验，从源头和评估角度影响数据质量，供给多样性问题高匹配的训练样本、科学评估归因数据价值，形成构建-验证-优化的闭环机制，从而为大模型的持续高质量演进提供系统性支撑，实现大模型在更多应用场景上的落地。"", ""青云课题挑战"": ""1. 高质量语料获取难度大：稀缺领域数据、低噪声语料的采集和标注成本高昂，且依赖专家知识；\n\n2. 合成数据存在分布偏差：合成数据易继承生成模型的固有偏差，导致长尾特征丢失，连续迭代多轮合成数据迭代会使模型性能崩塌；\n\n3. 算力与效率瓶颈：高保真多模态数据合成依赖大规模计算资源，而模型压缩（如知识蒸馏）可能牺牲生成多样性；\n\n4. 多领域数据配比优化复杂：不同任务和领域的数据如何科学配比，以最大化模型泛化能力，仍需深入探索；\n\n5.伦理与法律风险：合成数据的版权归属、恶意信息植入亟待解决。"", ""BG"": ""TEG"", ""部门"": ""数据平台部"", ""中心"": ""数据挖掘中心""}"
腾讯混元大模型-AI硬件加速解决方案研究及应用,"{""青云课题背景"": ""随着AI的发展，对算力需求逐步增加、成本也逐日攀升。而目前市面上，也涌现出不少异构的加速硬件。如何通过软硬协同，将这些异构硬件有效整合起来，提升上层应用的训练效率、降低推理成本，是未来大规模应用的关键，需要做该方向的前沿探索。"", ""青云课题价值"": ""业务当前已在包括训练框架、编译优化方面开展了相关工作，未来将结合底层硬件设计、定制，进一步提升应用效果，在大模型、搜广推等场景发挥更大价值。"", ""青云课题挑战"": ""该课题的攻克，需要相关人员既要熟悉底层各种AI硬件，又要熟悉上层AI应用，对能力的全面性要求较高，行业资深人员较少，目前处在前沿探索阶段。"", ""BG"": ""TEG"", ""部门"": ""数据平台部"", ""中心"": ""运营技术中心""}"
面向AI集群业务的高性能网络,"{""青云课题背景"": ""随着千亿/万亿级参数规模的LLM大模型不断涌现，以及语料库的爆炸式增长，训练算力需要万卡级GPU集群来支撑，训练时间也持续数周或更长，这对集群网络的性能和稳定性提出了非常高的要求。一方面，大规模GPU集群训练的性能瓶颈在于如何优化网络集合通信来让万卡GPU之间保持高效地传输同步；另一方面，GPU集群通常按训练时间计算成本，价格昂贵，网络频繁故障/中断会导致整个训练任务耗时大增。因此如何优化网络集合通信性能，保证网络运营的可靠，是GPU集群网络研发的重要内容。"", ""青云课题价值"": ""通过突破GPU间通信性能瓶颈，提升大模型训练效率与降低模型推理成本。"", ""青云课题挑战"": ""1. 面向AI大模型的网络架构/协议设计、拓扑感知流量规划调度、集合通信性能优化、在网计算等GPU训练加速方案；\n2. 构建网络全栈监控体系、网络故障实时定位、故障快速自愈等可靠运营方案。"", ""BG"": ""TEG"", ""部门"": ""网络平台部"", ""中心"": ""基础网络中心""}"
存储引擎核心技术优化升级项目,"{""青云课题背景"": ""云架构平台部存储团队负责公司各类存储系统的研发运营。我们将持续夯实数据存储安全，并解决海量数据规模下存储系统高并发、低延迟、高可靠、低成本带来的技术挑战。"", ""青云课题价值"": ""该课题设计紧扣业务痛点与技术演进趋势，既解决当前海量数据存储的迫切需求，又布局面向未来的智能存储基础设施。"", ""青云课题挑战"": ""1. 高效数据存储：研究高效的数据冗余编码以及分布算法，在保证数据可靠性的情况下降低数据冗余度和数据恢复开销；\n2. 深度软硬结合：深入了解各类存储介质、网络传输特性，结合其硬件特性开发高性能的存储引擎和网络框架，及时跟进硬件技术的发展；\n3. 数据行为分析：研究用户数据访问行为，根据不同的冷热程度、生命周期特点来优化存储系统，降低用户使用成本。"", ""BG"": ""TEG"", ""部门"": ""云架构平台部"", ""中心"": ""对象存储中心""}"
边缘分布式推理、网络传输优化,"{""青云课题背景"": ""腾讯云EdgeOne作为下一代CDN与边缘安全加速平台，深度融合了传输协议优化、智能调度和分布式边缘计算技术，支撑了腾讯内外核心业务（如微信、QQ、腾讯会议）及全球化客户（如头部游戏公司、跨境电商平台）的高性能需求。\n当前，EdgeOne在MoE 大语言边缘推理系统、通用边缘推理系统方向，在全球分布的边缘节点增加 AI 推理能力，补充开发者生态能力。在传输协议领域的优化，研究超大规模分布式节点和流量的传输优化机制，推动传输协议和系统的发展，迎接业界复杂网络和多态业务的挑战。\n这些既是其技术壁垒的核心，也是推动行业发展的关键课题，亟需高阶人才加入以应对重大技术挑战与创新机遇。"", ""青云课题价值"": ""​1. 核心技术研究：参与边缘云基础设施演进、超大规模分布式系统的设计和实现。\n​2. 全面能力培养​：\n1）边缘分布式推理方向：设计调度系统、分布式并行推理架构，充分利用机房的通信、硬盘、加速卡等设施，实现行业领先的大语言推理系统；\n2）传输优化方向：从协议设计（如拥塞控制算法）到性能调优，数据分析再到内核编程，接受全方面的系统工程和算法研究的锻炼，覆盖传输层全链路；探索AI+网络（如智能QoE预测）、新型纠错编码（如FEC与网络编码融合）等交叉领域。\n​3. 业务价值驱动​：直接优化亿万级用户的产品体验。包括腾讯内外多个核心业务。"", ""青云课题挑战"": ""1. 边缘分布式推理：\n需要结合边缘算力的特点，设计调度系统、分布式并行推理架构，充分利用机房的通信、硬盘、加速卡等设施，实现行业领先的大语言推理系统；将缓存、传输等优化方案应用至 CDN 领域，形成新的边缘云基础设施；应对大规模长尾模型对显存资源的使用，实现超高速的模型加载和卸载，并实现对模型推理的通用性能优化。\n2、传输优化：\n1）大规模流量的拥塞控制与全局优化​：开发基于边缘计算的分布式拥塞控制模型，结合网络拓扑感知的流量调度，探索QUIC与SDN/NFV技术的协同优化；\n2）多业务场景的传输适配与智能调度​：设计跨场景统一传输框架，结合AI预测网络状态，动态切换BBR/Cubic等算法；探索流优先级调度模型；\n​3）弱网跨网环境下的多路径传输可靠性增强​：快速连接迁移机制、动态FEC冗余度算法、端到端网络测量与多路径择优；\n4） 协议栈创新与内核深度优化​：用户态TCP/IP协议栈设计（如DPDK+QUIC）、基于eBPF的内核协议热升级，或混合架构下的零损耗协议转换。"", ""BG"": ""TEG"", ""部门"": ""云架构平台部"", ""中心"": ""接入平台中心""}"
高性能操作系统研发和自维护,"{""青云课题背景"": ""TencentOS正式进入全面自研路线，为支撑腾讯海量业务场景，自主研发能够从根本上保证腾讯操作系统的安全供应。自研操作系统是长期且具有挑战性的项目，希望有更多秉持着长期主义价值，对操作系统有充足技术热情的人才加入我们。"", ""青云课题价值"": ""操作系统属于计算机工程范畴里复杂性最大的软件程序之一；加入我们，结合腾讯内部充满挑战性的复杂应用场景，不断通过技术以提升资源利用率，节省海量算力成本，持续加速全社会云上创新；打造下一代智能云原生操作系统，提升行业界影响力。"", ""青云课题挑战"": ""1. 基础组件自维护：自研操作系统要求团队维护大规模的基础组件包，在维护效率、成本、先进性等方面都带来巨大挑战；\n2. 自研特性：自研特性是保证腾讯操作系统差异化竞争力的核心要素，包括自研组件、内核特性、工具链、运管平台等，这要求团队坚持创新开放，紧密拥抱上游；\n3. 场景化能力：通用操作系统无法满足腾讯自研业务和腾讯云上客户差异化的需求，TencentOS需要具备场景化感知、分析和调优能力；\n4. 开源生态：TencentOS的发展不仅依赖开源生态的输入更依赖我们。"", ""BG"": ""TEG"", ""部门"": ""云架构平台部"", ""中心"": ""系统研发中心""}"
端到端AI视频图像编解码,"{""青云课题背景"": ""为了满足腾讯公司日益增长的视频业务需求，提升公司视频业务的行业竞争力，香农实验室视频编码团队一直长期坚持做业界领先的编码器，专业服务好公司各类视频业务，连续多年在MSU编码比赛中保持多项第一。目前正在探索端到端压缩的研发落地，为直播、云游戏、图片、会议等业务提供主观体验更好、码率更低的编解码服务。"", ""青云课题价值"": ""当前基于深度学习的端到端AI视频图像编码正在迅速发展，编码压缩性能已经超过传统编码，并且在低码率下具有更好的主观质量，但复杂度也更高。如何设计实现编码压缩性能更高、场景泛化性更好、更鲁棒的AI编码器，同时降低复杂度、满足业务应用要求，是当前研发的重要方向。"", ""青云课题挑战"": ""设计实现行业领先的端到端AI视频图像编码器，需要具有深厚的视频图像编码、深度学习知识和应用能力，并且有较好的工程实践能力。"", ""BG"": ""TEG"", ""部门"": ""云架构平台部"", ""中心"": ""香农实验室""}"
自适应自演进Agent,"{""青云课题背景"": ""当前 Agent 方向的重要性在业界已经有足够的共识，也是业界重点投入的方向，极具创新价值。Agent 算法与能力突破成为前沿研究的焦点。"", ""青云课题价值"": ""当前开源的Agent在能以及算法上依旧有局限性，我们主要聚焦于解决下面两个问题：\n1. 使用用有限标注数据的方式进行被动学习，导致比较难以Scale、难以适应不断变化的新环境，还会有overfitting的问题；\n2. 缺少个性化，对不同的用户无法根据用户历史偏好进行千人千面的回复。"", ""青云课题挑战"": ""1. 从被动学习到自主探索与持续自演进\n传统的模型训练通常依赖于有限的标注数据进行被动学习，难以适应不断变化的环境和任务。为突破这一限制，智能体需逐步转向自主探索与自我演进的学习范式。具体包括：\n-智能体通过在新环境中的自主交互行为，主动收集环境反馈与指导信号，利用自监督学习、强化学习等方式，逐步建立对环境的理解和策略优化能力；\n-随着经验积累，智能体能够不断扩展至更复杂或未知的新任务与新环境，实现“自我学习-自我优化-自我泛化”的持续进化过程；\n-实现从“以人为中心标注”向“以智能体自身为学习驱动”的范式转变，有效提升可扩展性与泛化能力。\n2. 从缺少个性化到自动适配不同用户\n当前智能系统往往缺乏对用户个体差异的深入理解，难以提供个性化响应。为提升用户体验和系统实用性，智能体需逐步实现面向个体的动态适配与持续优化。具体包括：\n-智能体通过与用户的多轮交互，主动学习和积累用户的偏好、习惯与表达风格；\n-利用Memory或其他机制对用户信息进行提取、整合和更新；\n-在长期交互中不断巩固和优化偏好模型，实现个性化回复生成和策略调整。"", ""BG"": ""TEG"", ""部门"": ""AI Lab"", ""中心"": ""AGI研究中心""}"
原生多模态大模型,"{""青云课题背景"": ""现阶段主流的多模态大模型架构，更多是以语言大模型（LLM）为重心，桥接挂载图片&视频、语音&音频等其它模态。这种方式在具备一定多模态能力的同时，但仍存在关键局限。本课题旨在探索原生多模态大模型方向，从训练初期即同步发展语言、音频与视觉能力，重点打造模型在以下方面的原生能力：真实物理世界理解、多模态复杂推理与自适应持续学习。"", ""青云课题价值"": ""当前多模态大模型存在的局限性包括：\n1. 模型的多模态能力是在LLM后叠加的，难以实现对真实物理世界的细粒度建模、推理与预测；\n2. 模型是静态架构，一旦训练完成便不能从与环境的交互之中学习新的知识和能力；\n3. 推理和记忆等能力都封装在同一套参数里，导致记忆难以动态更新，会造成严重的遗忘问题。"", ""青云课题挑战"": ""1. 原生多模态模型架构：研究新的模型架构和训练方法，从初始阶段即实现多模态融合（Fusing），在多模态任务上超越当前以LLM为核心的建模方式；\n2. 时空建模能力：与当前以LLM为重心、更偏Chatbot的模型不同，原生多模态模型具备更强的对时间-空间进行建模的能力，从而能够处理、重构、理解和预测多模态数据（包括2D+时间和3D+时间的音视频），探索具有真实世界理解、多模态推理和自适应持续学习的大模型；\n3. 多模态复杂推理能力：复杂推理能力是大模型最基本的能力，须具备多模态理解、多模态形式的中间复杂推理以及答案生成能力；\n4. 多模态编码与表征学习：设计适用于音频、图像、视频等多模态数据的连续或离散表示方式；\n5. 长期记忆能力：探索多模态模型如何建立、更新与调用长期记忆，以支持持续学习与历史知识积累。"", ""BG"": ""TEG"", ""部门"": ""AI Lab"", ""中心"": ""AGI研究中心""}"
AIGC FOR GAME -自然语言处理方向,"{""青云课题背景"": ""1. 近期UGC生态得到了游戏行业的高度重视，如何更快的激发高质量UGC的创建已经成为了产品是否可以成功和长青的必要条件；\n同时，基于LLM的AI Agent也是当下AI最具前沿的研发方向，各大公司都投入巨大资源进行研发，为此团队也发起了以AI Agent为基础的游戏UGC场景搭建的研发课题；\n2. 目前游戏的AI Bot 和 LLM对话生成是分开实现，AI Bot 负责决策、LLM负责语言生成，这种实现方式容易导致言行不一问题，无法像真人玩家一样理解和响应人类玩家的语言，交互体验不足。希望能够通过LLM和AI Bot的联合建模，提升AI Bot的交互能力，做到打造“能听、会说、会玩”的AI玩家，从而提升玩家的游戏体验。"", ""青云课题价值"": ""1. UGC：AI Agent驱动的场景（半）自动搭建系统，可以极大的激发创作者更多、更高质量的UGC地图生产。同时为中腰部及以下创作者，提供帮助，使其创作地图更容易、创作的地图可以保证一定水准；\n得益于AI场景搭建，游戏的UGC生态可以得到更快、更高质量的构建，从而促使游戏得到成功；\n2. 对话：该课题需要探索研究LLM如何理解游戏盘面，然后进行决策和对话，其中涉及多模态大模型，盘面状态压缩，LLM与AI BOT结合等方向的探索。打造业界SoTA的角色扮演能力，效果领先、成本行业最低。"", ""青云课题挑战"": ""1. 目前业界的多模态大模型更多是在图像、视频、声音、文本等领域，没有考虑游戏盘面状态，LLM对游戏盘面状态的理解较弱，无法直接利用游戏盘面状态进行决策和对话；另外游戏盘面状态较多，作为Prompt输入较长，对效果、性能、成本都会有影响，如何在海量用户高并发下做到效果最优且成本最低，是一个很大的挑战；\n2. 目前LLM与AI BOT的结合更多是以pipeline的方式，天花板上限较低，没有考虑端到端联合建模；\n3. 个性化记忆：NPC需长期保持人设一致性（如性格、背景设定），同时根据玩家行为和游戏进程动态调整对话策略。实时结合玩家画像（如性格、游戏风格）生成个性化对话，需构建细粒度玩家行为分析模型；\n4. 跨场景打通：在游戏中，NPC需在不同场景（战斗、社交、剧情）中保持对话逻辑连贯。例如对局中和对局外的对话需做到无缝衔接。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""基础技术中心""}"
AIGC FOR GAME -多模态方向,"{""青云课题背景"": ""AIGC技术的发展给内容生产行业带来了新的历史性机遇，而游戏作为内容行业的主要方向之一更有重大影响，一方面希望通过AI技术提升游戏生产效率、降本增效，另一方面也希望创新游戏玩法，创造新的品类。\n团队正在开展可控的游戏视频生成项目，该项目希望通过生成高可玩、高质量的游戏视频，替代现有游戏引擎，同时为创造更多内容、风格的游戏赋能。"", ""青云课题价值"": ""本课题通过研发实时的视频生成，从而替代传统的人工3D资产生产+游戏引擎渲染的方式，该技术可为创造更多的游戏内容、更多风格的游戏关卡提供可能，从而使得玩家可以在更精彩多样的游戏内容中畅游。"", ""青云课题挑战"": ""目前视频生成领域是一个具备前沿性、有挑战的方向。本课题在此基础上，增加了实时生成、可控、可交互、可玩等更高的要求，具有业界较高的挑战难度。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""基础技术中心""}"
AI Agent for Game-强化学习方向,"{""青云课题背景"": ""以绝艺、绝悟为代表的的游戏AI，构建多智能体和策略博弈能力，持续建设游戏AI的拟人性、合理性、交互性，更进一步地挖掘AI Agents的商业价值对AI Agents的能力提出了更高的要求。"", ""青云课题价值"": ""结合强化学习、模仿学习和LLM生成式模型等方法，探索多模态决策推理能力； 支持腾讯重点游戏项目，包括MOBA、FPS、棋牌、UGC等各种品类，通过PVP和PVE场景AI Bot，持续提升AI Bot的商业价值。"", ""青云课题挑战"": ""1. 多模态决策智能：构建决策智能场景下的AI多模态信息处理能力，结合LLM、模仿学习、强化学习等相关技术，打造游戏场景下能听会说&言行匹配的下一代AI Bot；\n2. 决策智能场景下的多样性策略生成：探索生成式AI技术如何构建行为内容拟人&多样的智能体，从而构建游戏内容丰富的PVE玩家对局体验，进一步丰富和改善PVP玩家对局体验；\n3. 自适应智能体构建：结合生成式智能体和强化学习，探索多种品类UGC场景下Agent的生成能力，探索场景泛化和任务泛化的能力，以及复杂3D地图的场景泛化能力；\n4. 打法分层及还原能力：业界传统的游戏AI能力分层方案主要基于策划经验配置规则或参数，缺少创新且能落地的自适应方案。本课题方向之一是基于真实玩家数据的模仿学习实现AI能力对齐，并基于玩家画像个性化投放不同能力的AI调节玩家心流体验，从而提升游戏玩家的活跃留存。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""游戏接入中心""}"
AIGC FOR GAME -自然语言处理方向,"{""青云课题背景"": ""1. 近期UGC生态得到了游戏行业的高度重视，如何更快的激发高质量UGC的创建已经成为了产品是否可以成功和长青的必要条件；\n同时，基于LLM的AI Agent也是当下AI最具前沿的研发方向，各大公司都投入巨大资源进行研发，为此团队也发起了以AI Agent为基础的游戏UGC场景搭建的研发课题；\n2. 目前游戏的AI Bot 和 LLM对话生成是分开实现，AI Bot 负责决策、LLM负责语言生成，这种实现方式容易导致言行不一问题，无法像真人玩家一样理解和响应人类玩家的语言，交互体验不足。希望能够通过LLM和AI Bot的联合建模，提升AI Bot的交互能力，做到打造“能听、会说、会玩”的AI玩家，从而提升玩家的游戏体验。"", ""青云课题价值"": ""1. UGC：AI Agent驱动的场景（半）自动搭建系统，可以极大的激发创作者更多、更高质量的UGC地图生产。同时为中腰部及以下创作者，提供帮助，使其创作地图更容易、创作的地图可以保证一定水准；\n得益于AI场景搭建，游戏的UGC生态可以得到更快、更高质量的构建，从而促使游戏得到成功；\n2. 对话：该课题需要探索研究LLM如何理解游戏盘面，然后进行决策和对话，其中涉及多模态大模型，盘面状态压缩，LLM与AI BOT结合等方向的探索。打造业界SoTA的角色扮演能力，效果领先、成本行业最低。"", ""青云课题挑战"": ""1. 目前业界的多模态大模型更多是在图像、视频、声音、文本等领域，没有考虑游戏盘面状态，LLM对游戏盘面状态的理解较弱，无法直接利用游戏盘面状态进行决策和对话；另外游戏盘面状态较多，作为Prompt输入较长，对效果、性能、成本都会有影响，如何在海量用户高并发下做到效果最优且成本最低，是一个很大的挑战；\n2. 目前LLM与AI BOT的结合更多是以pipeline的方式，天花板上限较低，没有考虑端到端联合建模；\n3. 个性化记忆：NPC需长期保持人设一致性（如性格、背景设定），同时根据玩家行为和游戏进程动态调整对话策略。实时结合玩家画像（如性格、游戏风格）生成个性化对话，需构建细粒度玩家行为分析模型；\n4. 跨场景打通：在游戏中，NPC需在不同场景（战斗、社交、剧情）中保持对话逻辑连贯。例如对局中和对局外的对话需做到无缝衔接。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""游戏AI生成中心""}"
AIGC FOR GAME -自然语言处理方向,"{""青云课题背景"": ""1. 近期UGC生态得到了游戏行业的高度重视，如何更快的激发高质量UGC的创建已经成为了产品是否可以成功和长青的必要条件；\n同时，基于LLM的AI Agent也是当下AI最具前沿的研发方向，各大公司都投入巨大资源进行研发，为此团队也发起了以AI Agent为基础的游戏UGC场景搭建的研发课题；\n2. 目前游戏的AI Bot 和 LLM对话生成是分开实现，AI Bot 负责决策、LLM负责语言生成，这种实现方式容易导致言行不一问题，无法像真人玩家一样理解和响应人类玩家的语言，交互体验不足。希望能够通过LLM和AI Bot的联合建模，提升AI Bot的交互能力，做到打造“能听、会说、会玩”的AI玩家，从而提升玩家的游戏体验。"", ""青云课题价值"": ""1. UGC：AI Agent驱动的场景（半）自动搭建系统，可以极大的激发创作者更多、更高质量的UGC地图生产。同时为中腰部及以下创作者，提供帮助，使其创作地图更容易、创作的地图可以保证一定水准；\n得益于AI场景搭建，游戏的UGC生态可以得到更快、更高质量的构建，从而促使游戏得到成功；\n2. 对话：该课题需要探索研究LLM如何理解游戏盘面，然后进行决策和对话，其中涉及多模态大模型，盘面状态压缩，LLM与AI BOT结合等方向的探索。打造业界SoTA的角色扮演能力，效果领先、成本行业最低。"", ""青云课题挑战"": ""1. 目前业界的多模态大模型更多是在图像、视频、声音、文本等领域，没有考虑游戏盘面状态，LLM对游戏盘面状态的理解较弱，无法直接利用游戏盘面状态进行决策和对话；另外游戏盘面状态较多，作为Prompt输入较长，对效果、性能、成本都会有影响，如何在海量用户高并发下做到效果最优且成本最低，是一个很大的挑战；\n2. 目前LLM与AI BOT的结合更多是以pipeline的方式，天花板上限较低，没有考虑端到端联合建模；\n3. 个性化记忆：NPC需长期保持人设一致性（如性格、背景设定），同时根据玩家行为和游戏进程动态调整对话策略。实时结合玩家画像（如性格、游戏风格）生成个性化对话，需构建细粒度玩家行为分析模型；\n4. 跨场景打通：在游戏中，NPC需在不同场景（战斗、社交、剧情）中保持对话逻辑连贯。例如对局中和对局外的对话需做到无缝衔接。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""游戏AI生成中心""}"
AIGC FOR GAME -游戏语音对话,"{""青云课题背景"": ""在游戏场景下，探索玩家与NPC的端到端语音对话能力，打造高情商、高智商、高拟人、全双工、低延时的语音聊天体验。"", ""青云课题价值"": ""1. 技术价值： 端到端全双工语音对话，低延时，整合实时游戏数据和玩家画像，具备更高的智能化和拟人化；\n2. 应用价值：提升游戏沉浸感，支持虚拟陪伴等场景。"", ""青云课题挑战"": ""1. 多模态融合与上下文理解：如何有效地将语音信号（包含文本内容、说话人特征、丰富的情感韵律信息）与文本语义深度融合，使模型能真正理解语音中的动态情绪和隐含信息，从而生成高情商的回应；\n2. 复杂指令与声学特性的精准控制：如何让模型精确理解用户关于音色、语调、节奏、情感强度等声学层面的复杂指令，并稳定可靠地在语音输出中生成这些特性；\n3. 多轮对话中的长期记忆与准确性：如何在处理连续的语音对话流时，高效、准确地存储、检索和利用历史信息（尤其是语音中包含的丰富副语言信息），避免关键细节丢失或误解；\n4. 实现低延时的全双工交互：探索新的端到端模型架构，支持用户打断，具备实时处理流式输入和输出的能力，并且能够保持生成语音的高质量（自然度、表现力）。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""游戏AI生成中心""}"
AI Agent for Game-强化学习方向,"{""青云课题背景"": ""以绝艺、绝悟为代表的的游戏AI，构建多智能体和策略博弈能力，持续建设游戏AI的拟人性、合理性、交互性，更进一步地挖掘AI Agents的商业价值对AI Agents的能力提出了更高的要求。"", ""青云课题价值"": ""结合强化学习、模仿学习和LLM生成式模型等方法，探索多模态决策推理能力； 支持腾讯重点游戏项目，包括MOBA、FPS、棋牌、UGC等各种品类，通过PVP和PVE场景AI Bot，持续提升AI Bot的商业价值。"", ""青云课题挑战"": ""1. 多模态决策智能：构建决策智能场景下的AI多模态信息处理能力，结合LLM、模仿学习、强化学习等相关技术，打造游戏场景下能听会说&言行匹配的下一代AI Bot；\n2. 决策智能场景下的多样性策略生成：探索生成式AI技术如何构建行为内容拟人&多样的智能体，从而构建游戏内容丰富的PVE玩家对局体验，进一步丰富和改善PVP玩家对局体验；\n3. 自适应智能体构建：结合生成式智能体和强化学习，探索多种品类UGC场景下Agent的生成能力，探索场景泛化和任务泛化的能力，以及复杂3D地图的场景泛化能力；\n4. 打法分层及还原能力：业界传统的游戏AI能力分层方案主要基于策划经验配置规则或参数，缺少创新且能落地的自适应方案。本课题方向之一是基于真实玩家数据的模仿学习实现AI能力对齐，并基于玩家画像个性化投放不同能力的AI调节玩家心流体验，从而提升游戏玩家的活跃留存。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""游戏AI研发中心""}"
Agentic AI-智能交互、决策LLM、世界生成,"{""青云课题背景"": ""本课题关注Agentic AI，基础技术层面，拟研究AI在复杂虚拟世界的决策能力以及智能交互能力背后相关的技术，涉及了决策LLM的端到端建模方法、RL efficiency、RL与文本语音等其他模态融合的能力、复杂任务的自动化拆解（例如完成长链条任务）、AI在大世界的规划&推理能力（例如推理不同导航路径）、AI理解和生成可交互内容的能力（例如游戏世界生成，世界模型）。"", ""青云课题价值"": ""1. 研究价值：打造垂类AI Agent，提炼并深耕有特色和差异性的技术点，例如交互式&可控式世界生成、强化学习与LLM的结合等前沿课题，打造业界影响力和技术领先性；\n2. 应用价值：拟利用上述细分领域的技术，去提升AI智能体在虚拟世界的智能性、交互性，提升AI基座的能力，结合公司的游戏业务场景，提升用户体验。"", ""青云课题挑战"": ""虽然业界相关研究和研发较多，但在上述技术点上，仍然没有完全成熟的技术方案。同时，应用价值与热点技术的结合还待探索。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""游戏AI应用中心""}"
AI Infrastructure For GameAI,"{""青云课题背景"": ""腾讯开悟是腾讯牵头构建的AI多智能体与复杂决策开放研究平台，聚焦在游戏+AI的应用落地。当前的课题是高性能计算优化，偏向AI工程优化方向。欢迎查看我们的团队的官网了解更多：\nhttps://aiarena.tencent.com/"", ""青云课题价值"": ""该课题聚焦在游戏+AI的应用场景下的高性能计算问题，包括大规模的分布式训练优化、云端AI部署优化、移动端AI部署优化等。"", ""青云课题挑战"": ""AI工程优化是AI在产品落地的关键环节，降低AI在训练阶段和部署阶段的成本至关重要。当前有许多技术挑战，包括大规模的分布式训练优化、云端AI部署优化、移动端AI部署优化等等。欢迎感兴趣的同学加入。"", ""BG"": ""TEG"", ""部门"": ""AI平台部"", ""中心"": ""智能计算中心""}"
具身智能大模型开放平台,"{""青云课题背景"": ""构建具身智能开放平台，面向整个机器人行业和落地场景，从长程复杂规划决策、多模态感知理解物理世界、通用操作和运动策略三个方向服务行业需求，实现通用、跨本体、稳定的算法平台。"", ""青云课题价值"": ""期待吸引具身智能方向的大模型、多模态、操作运动控制领域的专业人才加入一起共建具身智能开放平台。"", ""青云课题挑战"": ""1. 构建针对解决长程、复杂的具身智能决策问题的Agent，通过语言、视觉大模型的复杂推理能力，结合CoT、Search、Prompting、RAG等技术，在真实物理世界下进行推理和规划；\n2. 构建多模态感知大模型，对真实物理世界进行快速、精准的理解和感知，能够支持语义、空间、物理逻辑等信息的整理和输出；\n3. 构建通用、稳定的VLA基础操作模型，能够跨本体实现生活、工业场景下的任务泛化，实现稳定的locomotion运动控制策略的训练和部署。"", ""BG"": ""TEG"", ""部门"": ""Robotics X"", ""中心"": ""2025\n具身智能技术中心""}"
WXG-基础大模型研究与应用,"{""青云课题背景"": ""在基础微信场景中，有大量微信特色的大模型应用场景，例如研发适合微信生态的 AI Agent。场景需求和数据极为丰富，应用前景广阔，因此受到重点关注。然而，为实现较好的用户体验，我们需要解决众多业界尚未探索或未有数据支持的前沿问题。"", ""青云课题价值"": ""探索微信生态内的大模型智能体应用实践"", ""青云课题挑战"": ""我们需要有极强研究能力、了解工程落地、善于业务思考、充满改变世界热情的同学加入我们共同面对这些极具特色又充满挑战的问题。"", ""BG"": ""WXG"", ""部门"": ""基础产品部"", ""中心"": ""推荐技术中心""}"
WXG-视频高效编码、增强处理与传输技术研究,"{""青云课题背景"": ""在面向海量用户的微信视频通话、直播、朋友圈/消息/视频号等图片和短视频业务中，视频图像内容场景复杂多样，且视频源的质量参差不齐，同时，移动端的视频图像应用对功耗、发热提出了非常严格的要求，而日益增长的图片视频业务给带宽流量成本、存储空间等都带来巨大的压力。"", ""青云课题价值"": ""我们需要在视频编解码、图像处理、流媒体传输等技术上持续突破，不断探索各类前沿视频图像技术，希望以合理的成本为用户提供最佳的多媒体业务体验。"", ""青云课题挑战"": ""本项目希望在以下方向中进行探索研究（包括但不限于）：\n1. 高效视频编解码技术\n2. 视频图像前后处理技术\n3. 视频传输QoS/QoE技术\n4. 多视点视频、自由视点视频、VR视频的编码、传输、播放技术"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""多媒体技术中心""}"
WXG-多语言大模型研发与应用,"{""青云课题背景"": ""微信的绝大部分场景（包括聊天、朋友圈、扫一扫、视频号、公众号、搜一搜、小程序、微信支付等）及相关生态产品（如微信读书、微信输入法、企业微信、秒剪等）存在大量的多语言、跨语言需求。面向这些丰富而具体的产品应用，我们希望研发高质量的多语言大模型。"", ""青云课题价值"": ""提升微信生态多语言场景的表达与信息获取体验"", ""青云课题挑战"": ""本项目希望面向业务场景进行深度探索，包括但不限于：\n1. 多语言混杂与格式复杂Query(如网页、复杂格式邮件等)的理解和翻译质量提升\n2. 提升跨模态场景的翻译质量，如实现更丝滑的图-图翻译、视频字幕翻译等\n3. 解决长内容翻译（如书籍翻译）中的忠实性、风格一致性和文采性问题\n4. 解决需要融入（时效性、文化、背景）知识和深度推理的 Query 翻译问题\n5. 熟练运用最前沿的深度学习算法、模型、强化学习技术等优化效果（如礼貌、幽默、消除偏见等），提升效率"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""模式识别中心""}"
WXG-深度推理大模型优化技术研究与应用,"{""青云课题背景"": ""深度推理大模型作为 AI 领域的一项重要技术突破，大幅度提升了复杂任务（如逻辑推理、多步规划、上下文理解等）的解决能力，微信中存在大量的复杂任务场景，可以借助深度推理达到效率提升。"", ""青云课题价值"": ""探索深度推理在微信场景内的应用"", ""青云课题挑战"": ""本项目希望在以下方向中进行探索研究（包括但不限于）：\n1. 参与深度推理大模型的高效模型结构、推理增强的数据构建方法和训练优化探索\n2. 面向具体应用场景优化效果和推理效率\n3. 熟练运用最前沿的深度学习算法、模型、强化学习技术等，探索更优路线"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""模式识别中心""}"
WXG-预训练与对齐-大规模语言模型研究与应用,"{""青云课题背景"": ""大规模语言模型在国民级应用中有巨大潜力，但同时落地过程也面临诸多挑战。"", ""青云课题价值"": ""团队在大规模语言模型研发领域积累超过4年的经验，欢迎对AI有热情的你加入我们，共同革新人们的信息获取方式。"", ""青云课题挑战"": ""1. 参与高效模型结构、预训练数据策略、对齐策略的研发，提升大规模语言模型的通用能力。2. 研究压缩、Scaling Law 等方法论与模型智能的关系。3. 研发高效的训练系统，提升大规模语言模型在具体应用场景的表现及迭代效果。"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""模式识别中心""}"
WXG-多模态强推理模型技术研究,"{""青云课题背景"": ""随着信息技术的飞速发展，我们获取的数据类型日益丰富，不再局限于单一的文本、图像或音频，而是多种模态数据并存。多模态强推理模型是当下人工智能领域的前沿研究方向，其技术背景基于多模态数据融合与推理能力提升的迫切需求而产生。"", ""青云课题价值"": ""整合不同模态的数据，使模型能够从多个维度理解和分析信息。"", ""青云课题挑战"": ""1. 深入研究多模态数据（文本、图像、音频等）的融合方法，构建高效的多模态强推理模型架构。\n2. 运用前沿深度学习算法，如 Transformer 及其变体、强化学习方法等，优化模型性能，提升推理的准确性和效率。\n3. 参与海量多模态数据集的整理与标注，为模型训练提供高质量数据支持。"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""模式识别中心""}"
WXG-视频生成模型蒸馏压缩,"{""青云课题背景"": ""基于扩散模型的视频生成模型已经在内容创作领域得到了广泛应用。现在视频生成模型推理成本较高，无法在C端场景中大规模部署，因此需要综合应用模型蒸馏、步数蒸馏、量化推理等优化技术大幅度降低推理的延迟和成本。"", ""青云课题价值"": ""探索生成式视频模型在C端场景的高效部署，推动视频号等内容创作场景的技术升级。"", ""青云课题挑战"": ""本项目希望基于行业开源的视频生成模型，结合微信视频号业务需求和业务私域数据，实现推理性能超百倍提升。"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""视觉技术中心""}"
WXG-多模态文档理解大模型,"{""青云课题背景"": ""在视频号、公众号、微信读书等场景存在大量的文档内容理解需求，过往寄托于OCR+分类的研发范式不够通用，而且算法上限低，业务需要借助VLM模型在通用世界理解的优势，探索OCR-free和OCR+VLM的多模态文档理解大模型在业务中的应用。"", ""青云课题价值"": ""突破传统文档解析的技术限制，提供通用化文档智能处理能力。"", ""青云课题挑战"": ""1. 围绕现有的VLM模型框架，设计高效的visual tokenizer、projector等模块;\n2. 在7B以下的参数量，文档理解能力打平甚至超过72B通用模型；\n3. 探索额外感知信息注入对VLM模型的影响；"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""视觉技术中心""}"
WXG-原生多模态大语言模型技术研究,"{""青云课题背景"": ""多模态大语言模型在学术界和工业界都受到了广泛关注。现有的多模态大语言模型基于预训练好的语言模型和大量的多模态数据，如图文对，去优化一个视觉、语音等模态到文本模态的对齐模块。虽然获得了令人惊喜的效果，但大多数现有方案并没有从原理上对多模态数据进行联合建模，仅仅是利用了多模态数据对的弱监督信号，学习到了一个表征对齐网络，而无法充分利用多模态数据和模型的建模能力。"", ""青云课题价值"": ""获得更好的多模态模型，涌现多模态大模型的 scaling law"", ""青云课题挑战"": ""探索原生多模态大语言模型的算法流程"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""视觉技术中心""}"
WXG-AI计算基础设施,"{""青云课题背景"": ""AI 在微信生态中有很多重要的实际应用，基于 AI 的推荐、视觉、语音、NLP 等上百种大中小模型已经被部署在移动/桌面/服务器端 CPU/GPU/NPU，以及多种操作系统平台上，时时刻刻影响着上亿人的日常生活。"", ""青云课题价值"": ""极致的多平台 AI 训练推理、图形图像处理等并行计算能力和配套设施，是微信持续跟进、打磨与夯实的重要基础能力模块之一，对于丰富产品能力、提高产品用户体验、降低功耗、节省成本至关重要。"", ""青云课题挑战"": ""1、硬件微架构和体系结构层面：不少部署平台（高通骁龙、苹果、ARM 等）微架构与体系结构细节并不公开，需要极其扎实的计算机体系结构功底、丰富的经验和极强的动手能力，方可解密硬件工作细节，设计极致的效率优化方案；2、AI、图像并行算法层面：精通与跟进所有 AI、图像算法的计算过程细节，是做系统性优化的基本前提。不限于视觉、NLP、大模型、多模态等任何具体领域，都需要时刻跟进学术界进展，嗅出潜在的落地机会并提前进行工程与系统设计和优化；3、软件系统框架与架构层面：基础计算设施需要时刻跟进新的算法结构和应用流程需求，随着大语言模型、多模态模型的兴起，计算密集型问题已不再是应用的唯一瓶颈，异构处理器结合异构存储器的联合系统设计与优化，甚至结合微信具体的负载环境和关联业务情况，提供充分兼容现有甚至未来软件需求的灵活设计，对新应用的开发/运行效率至关重要，这对软件架构能力也提出了巨大的挑战；此外，不同场景和平台面向的目标也千差万别，有的注重功耗，有的注重吞吐，有的注重延迟，如何让系统有足够合理的折衷的同时，不失设计的灵活性，也是个巨大的挑战；\n4、学科交叉层面：除了要拥有绝对扎实和深入的计算机硬件、CPU/GPU/NPU 体系结构、高性能计算、并行/分布式计算基础理论和极强的动手能力，系统的构建、优化与应用的集成，还需要对计算机视觉/语音/NLP、图像与视频处理、软件工程与测试、现代编译器、操作系统原理、客户端开发甚至数据库也有足够的了解；5、非技术层面：设计与优化系统达到 state-of-the-art 甚至还只是万里长征的第一步，如何在微信生态中与产品、算法团队密切合作，推广自己的工作，使得数亿用户能够实实在在的受益，丰富产品能力、提高产品用户体验、降低功耗、节省成本，其中不乏大量非技术问题，既对情商和逆商提出了前所未有的挑战，也能带来足够的历炼；"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""视觉技术中心""}"
WXG-大模型强化学习及蒸馏技术应用,"{""青云课题背景"": ""在大模型落地场景中，知识蒸馏是降低成本、提升效果的重要手段。为支持微信内多场景的大模型应用，我们希望研发面向大模型应用开发生命周期的完整系统，包括大模型的蒸馏、剪枝、强化学习等过程的成熟技术方法及应用平台。"", ""青云课题价值"": ""建设面向大模型应用开发生命周期的完整系统，包括大模型的蒸馏、剪枝、强化学习等过程的成熟技术方法及应用平台。"", ""青云课题挑战"": ""本项目希望面向业务场景进行深度探索，包括但不限于：\n1、大语言模型面向特定业务场景的系统化蒸馏方法\n2、多模态大模型的蒸馏方法\n3、大模型面向业务场景的强化学习方法\n4、多智能体决策系统的开发平台建设\n5、基于海量图数据挖掘的实时社交推荐新范式开发"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""数据中心""}"
WXG-智聆超自然语音合成技术研究,"{""青云课题背景"": ""语音合成技术（Text-to-Speech, TTS）在人工智能和深度学习的推动下取得了显著进步，特别是大模型技术的应用，使合成语音在自然度、流畅性和个性化方面达到了前所未有的水平。传统的语音合成方法依赖于拼接合成或统计参数合成，往往存在生硬、不够逼真的问题。而近年来，基于深度学习的端到端TTS模型（如Tacotron、FastSpeech等）结合神经声码器（如WaveNet、HiFi-GAN），极大提升了语音的自然度和表达力。随着大模型技术的发展，语音合成正向更智能、更个性化的方向演进。基于大规模预训练的语音合成模型，如VALL-E、Bark等，能够通过少量语音样本快速克隆说话人声音，实现高度逼真的个性化语音合成。此外，多模态大模型的兴起，使语音合成与语义理解、情感分析等技术深度融合，使得合成语音不仅具备高保真度，还能精准表达情感和语气。"", ""青云课题价值"": ""未来，语音合成技术将在多个领域发挥更大作用，如智能助手、虚拟主播、教育、医疗等。随着模型规模的扩大和训练数据的丰富，语音合成将更加自然，并具备更强的上下文理解能力。同时，实时语音合成、低资源语言支持以及隐私保护等方向也将成为研究重点。可以预见，大模型驱动下的语音合成技术将持续突破，为人机交互带来更加智能和沉浸式的体验。"", ""青云课题挑战"": ""1. 计算机、电子信息、人工智能、应用数学等相关专业，硕士及以上学历，具备语音合成或语音处理相关研究或开发经验；  \n2. 熟悉深度学习框架（如TensorFlow、PyTorch），具备语音合成模型的训练和优化经验；  \n3. 掌握语音信号处理、语音合成（TTS）相关技术，熟悉Tacotron、FastSpeech、VALL-E、HiFi-GAN等模型；  \n4. 具备大规模预训练模型的研究经验，了解多模态学习、语音克隆、情感合成等技术；  \n5. 具备良好的编程能力，熟练使用Python，掌握NumPy、SciPy等数据处理工具；  \n7. 具备良好的团队协作能力、创新意识和技术钻研精神，有语音合成相关论文发表或竞赛经验者优先。  \n8、优先条件 有实际语音合成系统落地经验，优化过TTS模型在生产环境中的性能者优先。"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""语音技术中心""}"
WXG-智聆语音识别技术研究,"{""青云课题背景"": ""随着大规模预训练模型的兴起，语音识别技术正在向更智能、更通用的方向发展。基于Transformer架构的模型（如Whisper、Wav2Vec 2.0、HuBERT）能够利用海量无监督数据进行预训练，从而在低资源语言、口音适应、嘈杂环境等方面展现出更强的鲁棒性。此外，多模态大模型的发展，使得语音识别与自然语言理解（NLU）、语音合成（TTS）等技术深度融合，实现更精准的语音交互体验GPT4o。"", ""青云课题价值"": ""未来，语音识别技术将在智能助手、智能客服、自动字幕、医疗记录、车载语音交互等领域发挥更大作用。随着模型规模的进一步扩大和计算能力的提升，语音识别将更加精准，并具备更强的上下文理解能力。同时，实时语音识别、低资源语言支持、隐私保护等方向也将成为研究重点。可以预见，大模型驱动下的语音识别技术将持续突破，使人机交互更加自然智能，为各行业带来深远影响。"", ""青云课题挑战"": ""1. 计算机、电子信息、人工智能、应用数学等相关专业，硕士及以上学历，具备语音识别或语音处理相关研究或开发经验；  \n2. 熟悉深度学习框架（如TensorFlow、PyTorch），具备语音识别模型的训练和优化经验；  \n3. 掌握语音信号处理、语音识别（ASR）相关技术，熟悉Whisper、Wav2Vec 2.0、DeepSpeech、Conformer等模型；  \n4. 具备大规模预训练模型的研究经验，了解自监督学习、端到端语音识别、多模态融合等技术；  \n5. 熟悉音频处理工具（如Librosa、Kaldi、FFmpeg）及常见语音特征（如Mel谱、MFCC、音高等）；  \n6. 具备良好的编程能力，熟练使用Python，掌握NumPy、SciPy等数据处理工具；  \n7. 具备良好的团队协作能力、创新意识和技术钻研精神，有语音识别相关论文发表或竞赛经验者优先。  \n8、有实际语音识别系统落地经验，优化过ASR模型在生产环境中的性能者优先。"", ""BG"": ""WXG"", ""部门"": ""技术架构部"", ""中心"": ""语音技术中心""}"
WXG-直播架构优化,"{""青云课题背景"": ""随着大模型的普及和发展，视频号在产品上需要与大模型能力相结合，在实际业务中提升对视频内容的处理和感知能力，这里也带来了工程上和算法上的双重挑战。"", ""青云课题价值"": ""支持视频号场景内的大模型应用探索，保证需求快速高质量的迭代。"", ""青云课题挑战"": ""1. 完成大模型在海量后台系统的落地，提供 7x24 无间断的服务能力，提升用户体验；2. 在应用场景对大模型进行针对性优化。"", ""BG"": ""WXG"", ""部门"": ""视频产品部"", ""中心"": ""后台开发中心""}"
WXG-直播架构优化,"{""青云课题背景"": ""随着视频号业务发展以及大模型 AI 能力的突破，视频号业务与大模型结合努力尝试，希望在视频创作等场景进行结合落地。"", ""青云课题价值"": ""支持视频号场景内的大模型应用探索，保证需求快速高质量的迭代。"", ""青云课题挑战"": ""1、为推荐场景提供高性能的可靠特征计算能力\n2、对模型服务性能做极致优化，保证需求的快速迭代和高质量稳定运行\n3、结合业务场景对模型做针对性优化"", ""BG"": ""WXG"", ""部门"": ""视频产品部"", ""中心"": ""后台开发中心""}"
WXG-视频号/直播推荐系统优化,"{""青云课题背景"": ""在短视频平台上，内容的冷启动问题是指新上传的视频在缺乏用户交互数据的情况下，如何被有效地推荐给合适的用户。"", ""青云课题价值"": ""探索短视频内容冷启动推荐场景的大模型应用，提升推荐系统的性能和用户满意度。"", ""青云课题挑战"": ""1. 多模态数据处理：短视频内容包含文本、图像、音频等多种模态信息。如何有效地融合这些多模态数据，提取出有用的特征用于推荐，是大模型应用中的一个难点。尤其是不同模态之间的信息如何协同工作，提升推荐的准确性。\n2. 历史消费序列与多模态特征的异构性：用户的历史消费序列通常是由一系列离散的视频ID、交互行为（如点击、点赞、分享等）组成，而当前视频的多模态特征则是连续的、高维的向量表示（如文本嵌入、图像特征、音频特征等）。如何将这两种异构的数据进行有效对齐，是一个关键挑战。\n3. 实时性要求：短视频平台的推荐系统需要具备极高的实时性，尤其是在冷启动阶段，新上传的视频需要尽快获得推荐曝光。大模型通常计算复杂度较高，如何在保证推荐质量的同时，满足实时性要求，是一个技术挑战。\n4. 计算资源与成本：大模型的训练和推理需要大量的计算资源，尤其是在短视频平台这种海量数据场景下，如何平衡推荐效果与计算成本，是一个实际的挑战。"", ""BG"": ""WXG"", ""部门"": ""视频产品部"", ""中心"": ""基础推荐中心""}"
WXG-视频号推荐系统优化,"{""青云课题背景"": ""随着视频号和直播的迭代与发展，各业务持续涌现出新的技术挑战。"", ""青云课题价值"": ""探索超大规模稀疏模型的训练与推理、生成式AI大模型，提升推荐性能。"", ""青云课题挑战"": ""1.推荐平台：负责开发模型训练平台，支持超大规模稀疏模型的秒级实时训练与推理；负责开发模型推理平台，支撑数百个模型的复杂环境下的高并发、低延迟、低成本运行和海量资源的实时调度；\n2.生成式AI大模型： 负责在大模型基础上做Post-training，研发视频号场景的生成式AI大模型，解决在Post-training、大规模推理上线、强化学习等过程中的技术问题。"", ""BG"": ""WXG"", ""部门"": ""视频产品部"", ""中心"": ""基础推荐中心""}"
WXG-视频号/直播推荐系统优化,"{""青云课题背景"": ""随着视频号和直播的迭代与发展，各业务持续涌现出新的产品与技术挑战。我们在用户的消费与发表体验、内容生态的供需关系、商业化生态与变现效率等方面面临着具有挑战性的任务。我们希望邀请热爱技术的你，一起攻克这些难题。"", ""青云课题价值"": ""提升视频号场景内的用户消费与发表体验、内容生态供需关系、商业化生态与变现效率等。"", ""青云课题挑战"": ""1、推荐技术：超大模型与超长序列设计、超大模型流式训练方法设计、模型与工程的协同优化、高并发亿级候选环境下召回系统设计、微信视频号各场景的跨域推荐；复杂多目标融合机制、多内容题材混排机制、直播推荐中的实时表征等；\n2、内容生态：供需关系刻画与生态机制设计、发表激励与内容冷启动模型与机制设计、社交关注与评论区等业务向优化；\n3、商业化：交易方向，视频号全场景用户交易兴趣建模，用户拉新与复购优化，交易与内容统一价值衡量机制， 用户支付/售后行为延迟反馈优化， 作者/主播/商家收入与供给生态优化等。投放方向，大规模稀疏场景下的变现效率与用户体验，高效的自然与投放内容的混排策略；全链路投放精度与一致性优化；稀疏场景下的内容冷启动优化；出价、ROI、成本、稳定性等机制优化。"", ""BG"": ""WXG"", ""部门"": ""视频产品部"", ""中心"": ""基础推荐中心""}"
WXG-视频号/直播推荐系统优化,"{""青云课题背景"": ""随着视频号和直播的迭代与发展，各业务持续涌现出新的产品与技术挑战。我们在用户的消费与发表体验、内容生态的供需关系、商业化生态与变现效率等方面面临着具有挑战性的任务。我们希望邀请热爱技术的你，一起攻克这些难题。"", ""青云课题价值"": ""提升视频号场景内的用户消费与发表体验、内容生态供需关系、商业化生态与变现效率等"", ""青云课题挑战"": ""1、推荐技术：超大模型与超长序列设计、超大模型流式训练方法设计、模型与工程的协同优化、高并发亿级候选环境下召回系统设计、微信视频号各场景的跨域推荐；复杂多目标融合机制、多内容题材混排机制、直播推荐中的实时表征等；\n2、内容生态：供需关系刻画与生态机制设计、发表激励与内容冷启动模型与机制设计、社交关注与评论区等业务向优化；\n3、商业化：交易方向，视频号全场景用户交易兴趣建模，用户拉新与复购优化，交易与内容统一价值衡量机制， 用户支付/售后行为延迟反馈优化， 作者/主播/商家收入与供给生态优化等。投放方向，大规模稀疏场景下的变现效率与用户体验，高效的自然与投放内容的混排策略；全链路投放精度与一致性优化；稀疏场景下的内容冷启动优化；出价、ROI、成本、稳定性等机制优化。"", ""BG"": ""WXG"", ""部门"": ""视频产品部"", ""中心"": ""基础推荐中心""}"
WXG-视频号/直播推荐系统优化,"{""青云课题背景"": ""随着视频号和直播的迭代与发展，各业务持续涌现出新的产品与技术挑战。"", ""青云课题价值"": ""提升视频号场景内的用户消费与发表体验、内容生态供需关系、商业化生态与变现效率等"", ""青云课题挑战"": ""1、推荐技术：超大模型与超长序列设计、超大模型流式训练方法设计、模型与工程的协同优化、高并发亿级候选环境下召回系统设计、微信视频号各场景的跨域推荐；复杂多目标融合机制、多内容题材混排机制、直播推荐中的实时表征等；\n2、内容生态：供需关系刻画与生态机制设计、发表激励与内容冷启动模型与机制设计、社交关注与评论区等业务向优化；\n3、商业化：交易方向，视频号全场景用户交易兴趣建模，用户拉新与复购优化，交易与内容统一价值衡量机制， 用户支付/售后行为延迟反馈优化， 作者/主播/商家收入与供给生态优化等。投放方向，大规模稀疏场景下的变现效率与用户体验，高效的自然与投放内容的混排策略；全链路投放精度与一致性优化；稀疏场景下的内容冷启动优化；出价、ROI、成本、稳定性等机制优化。"", ""BG"": ""WXG"", ""部门"": ""视频产品部"", ""中心"": ""直播推荐中心""}"
WXG-大模型在搜索系统中的应用落地研究,"{""青云课题背景"": ""利用大模型全面升级现有搜索技术、解决搜索系统中的核心难题、提升搜索效果并且兼顾搜索成本是目前的一个重要方向，具体研发内容包括但不限于：\n（1） 基于大模型的内容理解、复杂语义和多模态匹配技术。\n（2） 基于大模型的无Ground Truth场景多目标强化学习技术。\n（3） 基于大模型的AI搜索问答应用落地。\n（4） 基于大模型的搜索推荐场景下用户行为反馈RL应用落地。"", ""青云课题价值"": ""从更高层次探索在大模型的框架下检索范式的变化方向，辅助大模型在搜索业务的近似online级别的落地应用。"", ""青云课题挑战"": ""两大方面挑战：\n1. 效果方面，需要以搜索业务效果为目标，从训练数据、模型设计、训练工艺等角度调研大模型在搜索阶段的应用，切实解决业务问题；研究如何在搜索（无Ground Truth）场景下结合多目标学习提升产品体验等\n2. 效率方面，需要针对搜索业务数据量大、任务多、实时性强的特点，针对性进行训练效率提升，并对于复杂的大规模弱监督信号进行探索和消偏。"", ""BG"": ""WXG"", ""部门"": ""搜索应用部"", ""中心"": ""搜索技术中心""}"
WXG-从设计到代码（可信程序自动生成）,"{""青云课题背景"": ""微信支付为金融关键基础设施，对系统稳定性有着严苛的要求。因此研发团队一直遵循MDD模型驱动开发的模式，积累了丰富的结构化软件工程领域知识库(需求文档，领域模型，用例，序列图等)。研究LLM4SE技术从设计到自动敏捷生成高可用软件系统代码，能把软件工程师从繁杂的业务逻辑实现工作中解放出来，减少人因带来的系统bug，对研发效能及系统稳定性有着极大的价值。"", ""青云课题价值"": ""研究LLM4SE技术从设计到自动敏捷生成高可用软件系统代码，能把软件工程师从繁杂的业务逻辑实现工作中解放出来，减少人因带来的系统bug，对研发效能及系统稳定性有着极大的价值。"", ""青云课题挑战"": ""1. 该项目需训练领域大模型，在有界上下文的普遍语言上对通用LLM基座做领域适配(Domain-Adaptation)，模型需对微信支付领域知识(错综复杂的领域模型及业务逻辑)具有精准的掌握及推理能力。为LLM辅助领域建模提供支持。\n2. 该项目需研发CodeAgent，基于第一阶段领域建模后的模型，自动精准生成高可用的系统代码。且能根据需求调整，敏捷的响应系统代码更新迭代。"", ""BG"": ""WXG"", ""部门"": ""微信支付线/研发部"", ""中心"": ""瓦特实验室""}"
WXG-大模型安全防控,"{""青云课题背景"": ""大模型生成内容存在一定随机性，通过一些诱导，大模型可能输出带有偏见、有害的不合规回答，给业务带来负面影响。"", ""青云课题价值"": ""确保大模型在应用场景内的安全可靠、准确性以及与主流价值观的一致性。"", ""青云课题挑战"": ""1.高覆盖的prompt恶意识别能力\n对于用户输入内容提供多维度内容审核能力，识别明显存在如色情、涉政、涉恐等违法违规的引导问题，并对高对抗高变异的诱导性、欺诈性问题进行召回，防止诱导大模型输出恶意内容。对于恶意诱导大模型生成违规内容问题，进行自动Prompt改写并输出毒性提示。\n2.高性能的大模型内容理解能力\n大模型生成内容存在一定不可控情况，通过诱导，可能出现触犯违法违规、偏见歧视、涉政、违反社会价值观、侵犯隐私、恐怖/极端主义等安全合规的回答，需要构建高性能的大模型内容过滤能力，防止恶意内容输出到用户。\n3.安全稳定的大模型标准化生成\n对于涉敏高危问题，训练合规、具有正向引导的可信模型，通过训练数据过滤、强化学习、检索增强生成、数据持续预训练等方式，确保模型对高敏问题输出正向且正确的答案。\n4.高准的大模型内容合规评估\n构建大模型安全评测和攻击指令评测链路，对模型进行定期评估，主动发现大模型安全漏洞。"", ""BG"": ""WXG"", ""部门"": ""运营平台部"", ""中心"": ""信息中心""}"
WXG-大模型安全防控,"{""青云课题背景"": ""大模型生成内容存在一定随机性，通过一些诱导，大模型可能输出带有偏见、有害的不合规回答，给业务带来负面影响。"", ""青云课题价值"": ""确保大模型在应用场景内的安全可靠、准确性以及与主流价值观的一致性。"", ""青云课题挑战"": ""1.高覆盖的prompt恶意识别能力\n对于用户输入内容提供多维度内容审核能力，识别明显存在如色情、涉政、涉恐等违法违规的引导问题，并对高对抗高变异的诱导性、欺诈性问题进行召回，防止诱导大模型输出恶意内容。对于恶意诱导大模型生成违规内容问题，进行自动Prompt改写并输出毒性提示。\n2.高性能的大模型内容理解能力\n大模型生成内容存在一定不可控情况，通过诱导，可能出现触犯违法违规、偏见歧视、涉政、违反社会价值观、侵犯隐私、恐怖/极端主义等安全合规的回答，需要构建高性能的大模型内容过滤能力，防止恶意内容输出到用户。\n3.安全稳定的大模型标准化生成\n对于涉敏高危问题，训练合规、具有正向引导的可信模型，通过训练数据过滤、强化学习、检索增强生成、数据持续预训练等方式，确保模型对高敏问题输出正向且正确的答案。\n5.高准的大模型内容合规评估\n构建大模型安全评测和攻击指令评测链路，对模型进行定期评估，主动发现大模型安全漏洞。"", ""BG"": ""WXG"", ""部门"": ""运营平台部"", ""中心"": ""信息中心""}"
WXG-全模态特征风控大模型,"{""青云课题背景"": ""安全业务中，经常会使用多种多样的数据来解决问题，多样化的特征有助于提示各类风控识别的准度。"", ""青云课题价值"": ""在微信生态内完成一个多种模态特征的风控模型搭建（同时考虑行为序列，社交关系，设备环境，内容，画像等）。"", ""青云课题挑战"": ""1. 海量异构数据：基于微信海量用户海量数据进行各种模态预训练和模态对齐，包括数据清洗，并行模型训练，效果验证。\n2. 多种模特特征融合：微信产品的多样性决定了业务产生多种异构数据，包括行为序列，社交关系，设备环境，内容，基础用户画像，模型需要能基于全模态数据进行学习并且对各类数据有一定理解和输出解释能力。\n3. 零样本/少样本能力：解决大模型的概念，风控大模型的目标是借助大量数据建立垂直模型的能力，让模型在样本稀疏的风控场景有比较好的先验效果。"", ""BG"": ""WXG"", ""部门"": ""运营平台部"", ""中心"": ""业务中心""}"
