#!/usr/bin/env python3
"""
测试调度器序列化功能
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from scheduler_day_allapp_optimized import TaskScheduler, TaskInfo, TaskStatus

def test_task_serialization():
    """测试任务信息序列化"""
    print("测试任务信息序列化...")
    
    # 创建一个测试任务
    task_info = TaskInfo(
        task_id="test_task_001",
        session_cgi_label="cgi7",
        start_time=datetime.now(),
        end_time=datetime.now(),
        status=TaskStatus.COMPLETED,
        error_message=None,
        retry_count=0,
        max_retries=3,
        log_file="logs/test_task_001.log"
    )
    
    try:
        # 测试转换为字典
        task_dict = task_info.to_dict()
        print(f"✅ 任务转换为字典成功: {task_dict}")
        
        # 测试 JSON 序列化
        json_str = json.dumps(task_dict, indent=2)
        print(f"✅ JSON 序列化成功")
        
        # 测试 JSON 反序列化
        loaded_dict = json.loads(json_str)
        print(f"✅ JSON 反序列化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 序列化测试失败: {e}")
        return False

def test_scheduler_state_save():
    """测试调度器状态保存"""
    print("\n测试调度器状态保存...")
    
    try:
        # 创建调度器
        scheduler = TaskScheduler()
        
        # 添加一些已完成的任务
        for i in range(3):
            task_info = TaskInfo(
                task_id=f"test_task_{i:03d}",
                session_cgi_label="cgi7",
                start_time=datetime.now(),
                end_time=datetime.now(),
                status=TaskStatus.COMPLETED,
                log_file=f"logs/test_task_{i:03d}.log"
            )
            scheduler.completed_tasks.append(task_info)
        
        # 测试保存状态
        scheduler._save_task_state()
        print("✅ 调度器状态保存成功")
        
        # 检查文件是否存在
        if os.path.exists('scheduler_state.json'):
            print("✅ 状态文件创建成功")
            
            # 读取并验证内容
            with open('scheduler_state.json', 'r') as f:
                state = json.load(f)
                print(f"✅ 状态文件内容有效，包含 {len(state.get('completed_tasks', []))} 个任务")
        else:
            print("❌ 状态文件未创建")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 调度器状态保存测试失败: {e}")
        return False

def test_scheduler_state_load():
    """测试调度器状态加载"""
    print("\n测试调度器状态加载...")
    
    try:
        # 创建新的调度器实例
        scheduler = TaskScheduler()
        
        # 加载状态
        scheduler._load_task_state()
        print(f"✅ 调度器状态加载成功，恢复了 {len(scheduler.completed_tasks)} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 调度器状态加载测试失败: {e}")
        return False

def cleanup():
    """清理测试文件"""
    try:
        if os.path.exists('scheduler_state.json'):
            os.remove('scheduler_state.json')
            print("✅ 清理测试文件成功")
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")

if __name__ == "__main__":
    print("开始序列化测试...")
    
    success = True
    
    # 运行测试
    success &= test_task_serialization()
    success &= test_scheduler_state_save()
    success &= test_scheduler_state_load()
    
    # 清理
    cleanup()
    
    if success:
        print("\n🎉 所有测试通过！序列化问题已修复。")
    else:
        print("\n❌ 部分测试失败，请检查代码。")
        sys.exit(1)
