#!/usr/bin/env python3
"""
将 CSV 文件转换为 Excel 兼容的格式
"""

import csv
import pandas as pd
import os

def convert_csv_to_excel_formats():
    """将 CSV 转换为多种 Excel 兼容格式"""
    
    input_file = "anno_task.csv"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    try:
        # 方法1：生成 UTF-8 BOM 的 CSV（已经在主脚本中实现）
        print("当前的 anno_task.csv 已经是 UTF-8 BOM 格式，Excel 应该能正确打开")
        
        # 方法2：使用 pandas 生成 Excel 文件
        df = pd.read_csv(input_file, encoding='utf-8-sig')
        excel_file = "anno_task.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')
        print(f"已生成 Excel 文件: {excel_file}")
        
        # 方法3：生成 GBK 编码的 CSV（适用于中文 Windows Excel）
        gbk_file = "anno_task_gbk.csv"
        df.to_csv(gbk_file, index=False, encoding='gbk')
        print(f"已生成 GBK 编码 CSV 文件: {gbk_file}")
        
        print("\n使用建议:")
        print("1. 优先使用 anno_task.xlsx (Excel 文件)")
        print("2. 如果 Excel 打开 anno_task.csv 仍有乱码，使用 anno_task_gbk.csv")
        print("3. 在 Excel 中打开 CSV 时，可以使用'数据' -> '从文本/CSV' 导入，手动指定编码")
        
    except ImportError:
        print("错误：需要安装 pandas 和 openpyxl")
        print("请运行: pip install pandas openpyxl")
    except Exception as e:
        print(f"错误：转换过程中出现异常 {e}")

if __name__ == "__main__":
    convert_csv_to_excel_formats()
