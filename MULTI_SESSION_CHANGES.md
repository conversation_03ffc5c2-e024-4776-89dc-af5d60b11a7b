# 多Session CGI Label支持 - 修改总结

## 修改概述

为了支持同时处理多个`session_cgi_label`，对`scheduler_day_allapp_optimized.py`进行了以下关键修改：

## 主要修改内容

### 1. 参数解析增强
**文件位置**: 第675行
**修改内容**: 
- 更新`--session_cgi_label`参数的帮助文本
- 支持逗号分隔的多个标签输入

```python
# 修改前
parser.add_argument("--session_cgi_label", type=str, default="cgi7", help="会话 CGI 标签")

# 修改后  
parser.add_argument("--session_cgi_label", type=str, default="cgi7", 
                   help="会话 CGI 标签，支持多个标签用逗号分隔，如：cgi7,cgi8,cgi9")
```

### 2. 多标签解析逻辑
**文件位置**: 第696行
**修改内容**: 
- 添加标签解析逻辑，将逗号分隔的字符串转换为列表

```python
# 解析session_cgi_label参数，支持多个标签
session_cgi_labels = [label.strip() for label in args.session_cgi_label.split(',')]
```

### 3. 单次运行模式增强
**文件位置**: 第713-743行
**修改内容**: 
- 支持异步模式并发执行多个标签
- 支持同步模式依次执行多个标签
- 添加任务启动统计和反馈

```python
# 异步模式：并发启动所有session_cgi_label的任务
started_tasks = []
for label in session_cgi_labels:
    print(f"正在启动标签: {label}")
    task_id = run_task_optimized(label, args.use_multiprocess, args.max_workers, args.chunk_size)
    if task_id:
        started_tasks.append(task_id)
        print(f"任务 {task_id} 已启动，日志文件: logs/{task_id}.log")
```

### 4. 定时任务模式增强
**文件位置**: 第744-790行
**修改内容**: 
- 定时任务循环中支持多标签处理
- 添加定期状态显示功能（每5分钟）
- 区分同步和异步模式的处理逻辑

### 5. 任务管理功能扩展
**文件位置**: 第529-568行
**修改内容**: 
- 添加`get_running_tasks_by_labels()`方法
- 添加`get_task_summary()`方法
- 支持按标签分组的任务状态查询

```python
def get_running_tasks_by_labels(self, session_cgi_labels: List[str]) -> Dict[str, List[str]]:
    """获取指定标签的正在运行的任务"""
    
def get_task_summary(self, session_cgi_labels: List[str]) -> Dict[str, Dict[str, int]]:
    """获取指定标签的任务统计摘要"""
```

### 6. 状态显示功能
**文件位置**: 第698-716行
**修改内容**: 
- 添加`print_task_status()`函数
- 支持多标签的状态统计和显示
- 添加`--status`命令行参数

### 7. 类型注解修复
**修改内容**: 
- 修复所有涉及`max_workers`参数的类型注解
- 将`int`改为`Optional[int]`以支持None值

## 新增功能

### 1. 多标签并发执行
- 可以同时启动多个`session_cgi_label`的任务
- 智能并发控制，避免系统过载
- 每个标签独立的任务ID和日志文件

### 2. 异步执行模式
- **异步模式**：所有标签的任务并发执行，充分利用系统资源

### 3. 增强的状态监控
- 按标签分组的任务状态显示
- 实时统计运行中、已完成、失败的任务数量
- 定期自动状态更新（定时任务模式）

### 4. 命令行状态查询
- 新增`--status`参数，快速查看当前状态
- 支持多标签的状态汇总显示

## 使用示例

### 基本用法
```bash
# 单个标签（兼容原有用法）
python scheduler_day_allapp_optimized.py --session_cgi_label=cgi7 --run_once

# 多个标签（新功能）
python scheduler_day_allapp_optimized.py --session_cgi_label=cgi7,cgi8,cgi9 --run_once
```

### 高级用法
```bash
# 异步并发执行
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9 \
    --run_once \
    --use_multiprocess \
    --max_workers=4 \
    --max_concurrent=5

# 查看状态
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9 \
    --status
```

## 兼容性

### 向后兼容
- 所有原有的单标签用法完全兼容
- 原有的命令行参数和功能保持不变
- 现有脚本无需修改即可继续使用

### 新功能可选
- 多标签功能是可选的，默认行为与原版相同
- 可以逐步迁移到多标签模式

## 性能优势

### 1. 并发处理
- 多个标签可以同时处理，充分利用系统资源
- 减少总体处理时间

### 2. 资源优化
- 智能并发控制，避免系统过载
- 可配置的并发限制和进程数

### 3. 监控改进
- 实时状态监控，便于运维管理
- 详细的任务统计信息

## 文件结构

### 新增文件
1. `multi_session_example.py` - 使用示例和演示脚本
2. `MULTI_SESSION_README.md` - 详细使用指南
3. `MULTI_SESSION_CHANGES.md` - 本修改总结文档

### 修改文件
1. `scheduler_day_allapp_optimized.py` - 主调度器文件

### 日志文件
- 每个标签生成独立的日志文件：`logs/{session_cgi_label}_{YYYYMMDD}.log`
- 调度器主日志：`scheduler.log`
- 状态持久化：`scheduler_state.json`

## 测试验证

### 功能测试
- ✅ 单标签兼容性测试
- ✅ 多标签解析测试
- ✅ 状态显示功能测试
- ✅ 命令行参数测试

### 性能测试
- 建议在实际环境中测试不同并发设置的性能表现
- 监控系统资源使用情况
- 验证任务完成的正确性

## 注意事项

1. **资源管理**: 合理设置`max_concurrent`参数，避免系统过载
2. **磁盘空间**: 多标签会生成更多文件，确保有足够磁盘空间
3. **网络连接**: 确保到目标存储路径的网络连接稳定
4. **监控告警**: 建议设置监控，及时发现异常情况

## 后续优化建议

1. **负载均衡**: 可以考虑根据系统负载动态调整并发数
2. **优先级管理**: 为不同标签设置优先级
3. **故障恢复**: 增强任务失败后的自动重试机制
4. **性能分析**: 添加详细的性能统计和分析功能
