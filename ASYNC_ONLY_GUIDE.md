# 多Session CGI Label异步调度器使用指南

## 概述

优化版调度器现在专注于异步并发执行，支持同时处理多个`session_cgi_label`，通过智能并发控制最大化系统资源利用率。

## 核心特性

### ✨ 多标签并发执行
- 支持同时处理多个`session_cgi_label`
- 用逗号分隔多个标签：`cgi7,cgi8,cgi9`
- 每个标签独立生成任务ID和日志文件

### ⚡ 异步执行模式
- 所有标签的任务并发执行
- 智能并发控制，避免系统过载
- 充分利用多核CPU和系统资源

### 📊 实时状态监控
- 按标签分组的任务状态显示
- 实时统计运行中、已完成、失败的任务数量
- 定期自动状态更新

## 快速开始

### 基本用法
```bash
# 单个标签（兼容原有用法）
python scheduler_day_allapp_optimized.py --session_cgi_label=cgi7 --run_once

# 多个标签（新功能）
python scheduler_day_allapp_optimized.py --session_cgi_label=cgi7,cgi8,cgi9 --run_once
```

### 高性能配置
```bash
# 启用多进程和高并发
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9,cgi10 \
    --run_once \
    --use_multiprocess \
    --max_workers=6 \
    --chunk_size=2000 \
    --max_concurrent=8
```

### 定时任务
```bash
# 每天凌晨自动执行
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9 \
    --start_time=00:00 \
    --end_time=00:30 \
    --use_multiprocess \
    --max_workers=4 \
    --max_concurrent=6
```

### 状态查看
```bash
# 查看当前任务状态
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9 \
    --status
```

## 参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--session_cgi_label` | 会话标签，支持多个（逗号分隔） | cgi7 | cgi7,cgi8,cgi9 |
| `--run_once` | 只运行一次，不进入定时循环 | False | - |
| `--max_concurrent` | 最大并发任务数 | 3 | 5 |
| `--use_multiprocess` | 启用多进程加速 | False | - |
| `--max_workers` | 最大进程数 | 自动 | 6 |
| `--chunk_size` | 每批处理的行数 | 1000 | 2000 |
| `--status` | 显示当前任务状态并退出 | False | - |
| `--start_time` | 定时任务开始时间 | 00:00 | 01:00 |
| `--end_time` | 定时任务结束时间 | 00:10 | 01:30 |
| `--interval` | 定时任务间隔时间（秒） | 60 | 300 |

## 输出示例

### 任务启动
```
优化版调度器启动
配置参数:
  会话标签: ['cgi7', 'cgi8', 'cgi9']
  多进程模式: True
  最大进程数: 4
  批次大小: 1000
  最大并发任务数: 5
  系统CPU核心数: 8

单次运行模式
正在启动标签: cgi7
[2024-07-30 10:00:01] 任务 cgi7_20240729 已启动（异步执行）
[2024-07-30 10:00:01] 日志文件: logs/cgi7_20240729.log
正在启动标签: cgi8
[2024-07-30 10:00:02] 任务 cgi8_20240729 已启动（异步执行）
[2024-07-30 10:00:02] 日志文件: logs/cgi8_20240729.log
正在启动标签: cgi9
[2024-07-30 10:00:03] 任务 cgi9_20240729 已启动（异步执行）
[2024-07-30 10:00:03] 日志文件: logs/cgi9_20240729.log
总共启动了 3 个任务: cgi7_20240729, cgi8_20240729, cgi9_20240729
```

### 状态查看
```
=== 任务状态摘要 ===

标签: cgi7
  正在运行: 1 个任务
    任务ID: cgi7_20240729
  已完成: 5 个
  失败: 0 个
  已取消: 0 个

标签: cgi8
  正在运行: 1 个任务
    任务ID: cgi8_20240729
  已完成: 3 个
  失败: 1 个
  已取消: 0 个

标签: cgi9
  正在运行: 0 个任务
  已完成: 2 个
  失败: 0 个
  已取消: 0 个

总并发限制: 5
当前总运行任务数: 2
==============================
```

## 性能优化建议

### 🚀 并发设置
- **max_concurrent**: 建议设置为CPU核心数的1-2倍
- **max_workers**: 每个任务的进程数，建议2-8个
- **chunk_size**: 根据内存大小调整，建议500-2000

### 📈 系统监控
```bash
# 监控CPU和内存使用
htop

# 监控磁盘I/O
iotop

# 查看任务进程
ps aux | grep scheduler
```

### ⚙️ 推荐配置

#### 开发环境
```bash
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8 \
    --run_once \
    --max_concurrent=2 \
    --max_workers=2
```

#### 生产环境（高性能服务器）
```bash
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9,cgi10,cgi11 \
    --start_time=00:00 \
    --end_time=01:00 \
    --use_multiprocess \
    --max_workers=8 \
    --chunk_size=2000 \
    --max_concurrent=10
```

## 日志管理

### 日志文件结构
```
logs/
├── cgi7_20240729.log    # 标签cgi7的任务日志
├── cgi8_20240729.log    # 标签cgi8的任务日志
├── cgi9_20240729.log    # 标签cgi9的任务日志
└── ...
scheduler.log            # 调度器主日志
scheduler_state.json     # 任务状态持久化
```

### 日志查看
```bash
# 查看特定标签的日志
tail -f logs/cgi7_20240729.log

# 查看调度器日志
tail -f scheduler.log

# 查看所有日志
tail -f logs/*.log
```

## 故障排除

### ❌ 常见问题

#### 1. 任务无法启动
- 检查是否达到最大并发限制
- 确认没有同名任务正在运行
- 查看调度器日志：`scheduler.log`

#### 2. 任务执行失败
- 查看具体任务的日志文件
- 检查磁盘空间是否充足
- 确认目标目录权限正确

#### 3. 性能问题
- 适当调整并发参数
- 监控系统资源使用情况
- 考虑分批执行标签

### 🔧 调试技巧
```bash
# 查看系统资源使用
top -p $(pgrep -f scheduler_day_allapp_optimized)

# 查看任务进程树
pstree -p $(pgrep -f scheduler_day_allapp_optimized)

# 监控文件系统使用
df -h
```

## 最佳实践

### 1. 🎯 合理规划并发
- 根据系统资源设置合理的并发数
- 避免设置过高的并发导致系统过载
- 监控系统负载，动态调整参数

### 2. 📝 日志管理
- 定期清理旧日志文件
- 设置日志轮转策略
- 监控日志文件大小

### 3. 🔄 任务调度
- 合理安排任务执行时间
- 避免在系统繁忙时段执行大量任务
- 设置合适的重试机制

### 4. 📊 监控告警
- 设置任务失败告警
- 监控系统资源使用
- 定期检查任务执行状态

## 示例脚本

运行提供的示例脚本了解更多用法：
```bash
# 查看功能演示
python multi_session_example.py features

# 查看使用示例
python multi_session_example.py examples

# 运行演示（仅显示命令，不实际执行）
python multi_session_example.py run
```

## 总结

异步调度器通过并发执行多个`session_cgi_label`的任务，显著提高了数据处理效率。合理配置并发参数，配合系统监控，可以充分发挥系统性能，实现高效的数据处理流水线。
