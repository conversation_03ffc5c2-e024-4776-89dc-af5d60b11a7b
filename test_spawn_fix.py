#!/usr/bin/env python3
"""
测试spawn模式修复的脚本
"""

import os
import subprocess
import time
import multiprocessing as mp
import psutil
from datetime import datetime

def test_multiprocess_spawn():
    """测试多进程spawn模式"""
    print("=" * 50)
    print("测试多进程spawn模式")
    print("=" * 50)
    
    # 设置spawn模式
    if hasattr(mp, 'set_start_method'):
        try:
            mp.set_start_method('spawn', force=True)
            print("✅ 成功设置多进程启动方法为spawn")
        except RuntimeError as e:
            print(f"⚠️  设置spawn模式时出现警告: {e}")
    else:
        print("❌ 当前Python版本不支持set_start_method")
        return False
    
    # 创建测试脚本
    test_script = '''
import multiprocessing as mp
import time
import os
import sys

def worker_function(worker_id):
    """工作进程函数"""
    print(f"Worker {worker_id} started, PID: {os.getpid()}")
    time.sleep(2)
    print(f"Worker {worker_id} finished")
    return f"Result from worker {worker_id}"

def main():
    print(f"Main process PID: {os.getpid()}")
    print(f"Multiprocessing start method: {mp.get_start_method()}")
    
    # 使用ProcessPoolExecutor测试
    from concurrent.futures import ProcessPoolExecutor
    
    with ProcessPoolExecutor(max_workers=2, mp_context=mp.get_context('spawn')) as executor:
        futures = [executor.submit(worker_function, i) for i in range(3)]
        
        for future in futures:
            result = future.result()
            print(f"Got result: {result}")
    
    print("All workers completed successfully")

if __name__ == "__main__":
    main()
'''
    
    script_path = "test_spawn_multiprocess.py"
    with open(script_path, 'w') as f:
        f.write(test_script)
    
    try:
        print("启动spawn模式测试子进程...")
        
        # 启动子进程
        process = subprocess.Popen(
            ["python", script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            env=os.environ.copy(),
            preexec_fn=os.setsid if hasattr(os, 'setsid') else None,
            close_fds=True
        )
        
        print(f"测试进程PID: {process.pid}")
        
        # 监控进程
        start_time = time.time()
        output_lines = []
        
        while process.poll() is None:
            line = process.stdout.readline()
            if line:
                output_lines.append(line.strip())
                print(f"子进程输出: {line.strip()}")
            
            # 超时保护
            if time.time() - start_time > 30:
                print("测试超时，终止进程")
                process.terminate()
                break
        
        # 获取剩余输出
        remaining_output, _ = process.communicate()
        if remaining_output:
            for line in remaining_output.strip().split('\n'):
                if line:
                    output_lines.append(line)
                    print(f"子进程输出: {line}")
        
        print(f"测试进程退出码: {process.returncode}")
        print(f"测试运行时间: {time.time() - start_time:.1f} 秒")
        
        # 检查是否成功
        success_indicators = [
            "All workers completed successfully",
            "spawn" in ' '.join(output_lines)
        ]
        
        if process.returncode == 0 and any(success_indicators):
            print("✅ spawn模式测试成功")
            return True
        else:
            print("❌ spawn模式测试失败")
            return False
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(script_path):
            os.remove(script_path)

def test_scheduler_with_spawn():
    """测试调度器的spawn模式修复"""
    print("\n" + "=" * 50)
    print("测试调度器spawn模式修复")
    print("=" * 50)
    
    try:
        # 检查调度器脚本是否存在
        if not os.path.exists("scheduler_day_allapp_optimized.py"):
            print("❌ 找不到调度器脚本文件")
            return False
        
        # 生成一个临时任务脚本来检查内容
        from scheduler_day_allapp_optimized import TaskScheduler
        
        scheduler = TaskScheduler()
        script_content = scheduler._generate_complete_task_script(
            session_cgi_label="test_cgi",
            use_multiprocess=True,
            max_workers=2,
            chunk_size=500
        )
        
        # 检查脚本内容是否包含spawn设置
        spawn_checks = [
            "import multiprocessing as mp" in script_content,
            "mp.set_start_method('spawn'" in script_content,
            "force=True" in script_content
        ]
        
        print("检查生成的任务脚本:")
        print(f"  包含multiprocessing导入: {'✅' if spawn_checks[0] else '❌'}")
        print(f"  包含spawn设置: {'✅' if spawn_checks[1] else '❌'}")
        print(f"  包含force参数: {'✅' if spawn_checks[2] else '❌'}")
        
        if all(spawn_checks):
            print("✅ 调度器已正确配置spawn模式")
            return True
        else:
            print("❌ 调度器spawn模式配置不完整")
            return False
            
    except Exception as e:
        print(f"测试调度器时出错: {e}")
        return False

def check_zombie_processes_before_after():
    """检查修复前后的僵尸进程情况"""
    print("\n" + "=" * 50)
    print("检查僵尸进程情况")
    print("=" * 50)
    
    def count_zombies():
        zombie_count = 0
        for proc in psutil.process_iter(['pid', 'status', 'name']):
            try:
                if proc.info['status'] == psutil.STATUS_ZOMBIE:
                    zombie_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return zombie_count
    
    initial_zombies = count_zombies()
    print(f"初始僵尸进程数量: {initial_zombies}")
    
    # 模拟一些子进程活动
    print("模拟子进程活动...")
    
    test_script = '''
import time
import os
print(f"Test subprocess PID: {os.getpid()}")
time.sleep(1)
print("Test subprocess completed")
'''
    
    script_path = "test_zombie_check.py"
    with open(script_path, 'w') as f:
        f.write(test_script)
    
    try:
        processes = []
        for i in range(3):
            proc = subprocess.Popen(
                ["python", script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                preexec_fn=os.setsid if hasattr(os, 'setsid') else None,
                close_fds=True
            )
            processes.append(proc)
        
        # 等待所有进程完成
        for proc in processes:
            proc.wait()
        
        time.sleep(2)  # 给系统时间清理
        
        final_zombies = count_zombies()
        print(f"最终僵尸进程数量: {final_zombies}")
        
        if final_zombies <= initial_zombies:
            print("✅ 没有产生新的僵尸进程")
            return True
        else:
            print(f"❌ 产生了 {final_zombies - initial_zombies} 个新的僵尸进程")
            return False
            
    except Exception as e:
        print(f"检查僵尸进程时出错: {e}")
        return False
    finally:
        if os.path.exists(script_path):
            os.remove(script_path)

def main():
    """主测试函数"""
    print(f"spawn模式修复测试 - {datetime.now()}")
    print(f"Python版本: {mp.get_start_method()}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 测试结果
    results = []
    
    # 1. 测试多进程spawn模式
    results.append(("多进程spawn模式", test_multiprocess_spawn()))
    
    # 2. 测试调度器spawn配置
    results.append(("调度器spawn配置", test_scheduler_with_spawn()))
    
    # 3. 检查僵尸进程
    results.append(("僵尸进程检查", check_zombie_processes_before_after()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！spawn模式修复成功。")
        print("\n建议:")
        print("1. 子进程现在应该使用spawn模式启动，避免fork相关问题")
        print("2. 僵尸进程问题应该得到解决")
        print("3. 多进程任务应该更加稳定")
        return 0
    else:
        print("\n⚠️  部分测试失败，可能仍存在问题。")
        return 1

if __name__ == "__main__":
    exit(main())
