#!/usr/bin/env python3
"""
脚本用于将 anno_task.csv 中的 app_id 与 app_ids.json 中的数据匹配，
并填入对应的 app_name。
"""

import json
import csv
import os
from typing import Dict, List


def load_app_ids_mapping(json_file_path: str) -> Dict[str, str]:
    """
    从 JSON 文件加载 app_name 到 app_id 的映射，
    并创建反向映射（app_id 到 app_name）。
    
    Args:
        json_file_path: JSON 文件路径
        
    Returns:
        app_id 到 app_name 的映射字典
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            app_name_to_id = json.load(f)
        
        # 创建反向映射：app_id -> app_name
        id_to_name = {app_id: app_name for app_name, app_id in app_name_to_id.items()}
        
        print(f"成功加载 {len(id_to_name)} 个应用映射")
        return id_to_name
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {json_file_path}")
        return {}
    except json.JSONDecodeError:
        print(f"错误：JSON 文件格式错误 {json_file_path}")
        return {}
    except Exception as e:
        print(f"错误：加载 JSON 文件时出现异常 {e}")
        return {}


def process_csv_file(csv_file_path: str, id_to_name_mapping: Dict[str, str], output_file_path: str = None) -> None:
    """
    处理 CSV 文件，根据 app_id 填入对应的 app_name。
    
    Args:
        csv_file_path: 输入 CSV 文件路径
        id_to_name_mapping: app_id 到 app_name 的映射字典
        output_file_path: 输出文件路径，如果为 None 则覆盖原文件
    """
    if not os.path.exists(csv_file_path):
        print(f"错误：找不到 CSV 文件 {csv_file_path}")
        return
    
    if output_file_path is None:
        output_file_path = csv_file_path
    
    try:
        # 读取 CSV 文件，处理可能的 BOM
        rows = []
        with open(csv_file_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames

            # 检查列名（处理可能的 BOM 问题）
            app_id_col = None
            app_name_col = None

            for col in fieldnames:
                if col.strip().endswith('app_id'):
                    app_id_col = col
                elif col.strip().endswith('app_name'):
                    app_name_col = col

            if app_id_col is None:
                print(f"错误：CSV 文件中没有找到 'app_id' 列。现有列: {fieldnames}")
                return

            if app_name_col is None:
                print(f"错误：CSV 文件中没有找到 'app_name' 列。现有列: {fieldnames}")
                return
            
            for row in reader:
                rows.append(row)
        
        # 处理数据
        matched_count = 0
        unmatched_count = 0
        
        for row in rows:
            app_id = row[app_id_col].strip()
            if app_id and app_id in id_to_name_mapping:
                row[app_name_col] = id_to_name_mapping[app_id]
                matched_count += 1
            elif app_id:
                # app_id 存在但找不到对应的 app_name
                unmatched_count += 1
                print(f"警告：找不到 app_id '{app_id}' 对应的 app_name")
        
        # 写入结果（使用 UTF-8 BOM，Excel 可以正确识别）
        with open(output_file_path, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
        
        print(f"处理完成！")
        print(f"- 成功匹配: {matched_count} 条记录")
        print(f"- 未匹配: {unmatched_count} 条记录")
        print(f"- 输出文件: {output_file_path}")
        
    except Exception as e:
        print(f"错误：处理 CSV 文件时出现异常 {e}")


def main():
    """主函数"""
    # 文件路径
    json_file = "app_ids.json"
    csv_file = "anno_task.csv"
    
    # 检查文件是否存在
    if not os.path.exists(json_file):
        print(f"错误：找不到 {json_file} 文件")
        return
    
    if not os.path.exists(csv_file):
        print(f"错误：找不到 {csv_file} 文件")
        return
    
    print("开始处理文件...")
    
    # 加载映射关系
    id_to_name_mapping = load_app_ids_mapping(json_file)
    if not id_to_name_mapping:
        print("无法加载应用映射关系，程序退出")
        return
    
    # 处理 CSV 文件
    # 创建备份文件名
    backup_file = csv_file.replace('.csv', '_backup.csv')
    
    # 先创建备份
    try:
        import shutil
        shutil.copy2(csv_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
    except Exception as e:
        print(f"警告：无法创建备份文件 {e}")
    
    # 处理文件
    process_csv_file(csv_file, id_to_name_mapping)


if __name__ == "__main__":
    main()
