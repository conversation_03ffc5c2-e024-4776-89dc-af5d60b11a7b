#!/usr/bin/env python3
"""
检查处理结果的脚本
"""

import csv
import json

def check_results():
    """检查处理结果"""
    
    # 读取处理后的 CSV 文件
    with open('anno_task.csv', 'r', encoding='utf-8-sig') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
    
    # 统计信息
    total_rows = len(rows)
    filled_rows = sum(1 for row in rows if row['app_name'].strip())
    empty_rows = total_rows - filled_rows
    
    print(f"处理结果统计:")
    print(f"- 总记录数: {total_rows}")
    print(f"- 已填充 app_name: {filled_rows}")
    print(f"- 未填充 app_name: {empty_rows}")
    
    if empty_rows > 0:
        print(f"\n未填充的记录:")
        for i, row in enumerate(rows, 1):
            if not row['app_name'].strip():
                print(f"  行 {i+1}: app_id = {row['app_id']}")
    
    # 显示一些成功匹配的示例
    print(f"\n成功匹配的示例 (前5条):")
    count = 0
    for i, row in enumerate(rows, 1):
        if row['app_name'].strip() and count < 5:
            print(f"  行 {i+1}: {row['app_id']} -> {row['app_name']}")
            count += 1

if __name__ == "__main__":
    check_results()
