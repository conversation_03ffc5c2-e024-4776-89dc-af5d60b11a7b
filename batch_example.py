#!/usr/bin/env python3
"""
批量注释请求的使用示例
"""

from batch_annotation_requests import AnnotationBatchProcessor

def example_usage():
    """使用示例"""
    
    # 你的查询列表
    queries = [
        "帮我点一杯拿铁",
        "我想要一杯美式咖啡", 
        "给我来一份三明治",
        "有什么推荐的饮品吗",
        "我要一杯热巧克力",
        "请给我一个芝士蛋糕",
        "我想要一杯冰咖啡",
        "有素食选项吗",
        "帮我推荐一款茶饮",
        "我要一份沙拉"
    ]
    
    # 创建批量处理器
    processor = AnnotationBatchProcessor()
    
    print("开始批量处理...")
    print(f"将处理 {len(queries)} 个查询")
    print(f"目标URL: {processor.base_url}")
    print(f"App ID: {processor.app_id}")
    print(f"App Name: {processor.app_name}")
    print("-" * 50)
    
    # 执行批量处理
    # delay参数控制请求间隔，避免服务器压力过大
    results = processor.batch_process(queries, delay=1.0)
    
    # 保存结果
    processor.save_results(results, "my_batch_results.json")
    
    # 分析结果
    print("\n" + "="*50)
    print("处理结果分析:")
    print("="*50)
    
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    print(f"总查询数: {len(queries)}")
    print(f"成功数: {len(successful_results)}")
    print(f"失败数: {len(failed_results)}")
    
    if successful_results:
        print("\n成功的查询:")
        for result in successful_results:
            print(f"  ✓ {result['query']} (状态码: {result['status_code']})")
    
    if failed_results:
        print("\n失败的查询:")
        for result in failed_results:
            print(f"  ✗ {result['query']} - 错误: {result.get('error', '未知错误')}")
    
    print(f"\n详细结果已保存到: my_batch_results.json")
    print("日志文件: batch_requests.log")


def custom_queries_example():
    """自定义查询列表示例"""
    
    # 你可以在这里放入你的查询列表
    my_queries = [
        # 在这里添加你的查询
        "你的查询1",
        "你的查询2", 
        "你的查询3",
        # ... 更多查询
    ]
    
    processor = AnnotationBatchProcessor()
    
    # 如果需要修改app信息，可以这样做：
    # processor.app_id = "your_app_id"
    # processor.app_name = "your_app_name"
    
    results = processor.batch_process(my_queries, delay=0.5)
    processor.save_results(results, "custom_results.json")
    
    return results


if __name__ == "__main__":
    # 运行示例
    example_usage()
    
    # 如果你想使用自定义查询，取消下面的注释
    # custom_queries_example()
