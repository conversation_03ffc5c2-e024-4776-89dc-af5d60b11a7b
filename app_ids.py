import json

# 用户已知的小程序id
known_app_ids = {
    '学信网报告在线验证': 'wxa7f1a3cd7919a788',
    'i龙华': 'wx76e5f638841936e1',
    '京东购物丨点外卖领国补': 'wx91d27dbf599dff74',
    '赛伦100蛇伤防治': 'wxf6e8e8b098003e72',
    '金钩集': 'wxbc3ff72cb5039467',
    '途虎养车I轮胎保养蓄电池洗车': 'wx27d20205249c56a3',
    '腾讯手机充值服务': 'wxad3150031786d672',
    '海底捞特色服务排号': 'wxef944201e579d156',
    '一证通查': 'wx9ccaf5ef968ef0cd',
}

# 读取apps.txt，获取所有app_name
with open('apps.txt', 'r', encoding='utf-8') as f:
    app_names = set(line.strip() for line in f if line.strip())

# 用于存储结果
name_to_appid = known_app_ids.copy()

# 逐行读取app_info_dict.json，查找对应的appid
with open('app_info_dict.json', 'r', encoding='utf-8') as f:
    for line in f:
        try:
            info = json.loads(line)
            name = info.get('name')
            appid = info.get('appid')
            if name in app_names and name not in name_to_appid:
                name_to_appid[name] = appid
        except Exception as e:
            continue  # 跳过格式不对的行

# 输出结果
for name in app_names:
    appid = name_to_appid.get(name, 'NOT_FOUND')
    print(f'{name}\t{appid}')

# 写入json文件（只包含找到的）
with open('app_ids.json', 'w', encoding='utf-8') as f:
    json.dump(name_to_appid, f, ensure_ascii=False, indent=2)

# 单独打印没有找到的
not_found = [name for name in app_names if name not in name_to_appid]
if not_found:
    print('\n未找到的app_name:')
    for name in not_found:
        print(name)