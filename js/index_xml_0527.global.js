
const __WX_MER_DECODE_VERSION__ = '0.3.28';
const __WX_MER_DECODE_BUILD_TIME__ = '2025/7/28 20:06:00';
"use strict";
var __WX_MER_DECODE__ = (() => {
  var __create = Object.create;
  var __defProp = Object.defineProperty;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getProtoOf = Object.getPrototypeOf;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
    get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
  }) : x)(function(x) {
    if (typeof require !== "undefined") return require.apply(this, arguments);
    throw Error('Dynamic require of "' + x + '" is not supported');
  });
  var __commonJS = (cb, mod) => function __require2() {
    return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
    // If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
    mod
  ));
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

  // ../../node_modules/.pnpm/papaparse@5.5.3/node_modules/papaparse/papaparse.js
  var require_papaparse = __commonJS({
    "../../node_modules/.pnpm/papaparse@5.5.3/node_modules/papaparse/papaparse.js"(exports, module) {
      "use strict";
      (function(root, factory) {
        if (typeof define === "function" && define.amd) {
          define([], factory);
        } else if (typeof module === "object" && typeof exports !== "undefined") {
          module.exports = factory();
        } else {
          root.Papa = factory();
        }
      })(exports, function moduleFactory() {
        "use strict";
        var global = function() {
          if (typeof self !== "undefined") {
            return self;
          }
          if (typeof window !== "undefined") {
            return window;
          }
          if (typeof global !== "undefined") {
            return global;
          }
          return {};
        }();
        function getWorkerBlob() {
          var URL = global.URL || global.webkitURL || null;
          var code = moduleFactory.toString();
          return Papa2.BLOB_URL || (Papa2.BLOB_URL = URL.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ", "(", code, ")();"], { type: "text/javascript" })));
        }
        var IS_WORKER = !global.document && !!global.postMessage, IS_PAPA_WORKER = global.IS_PAPA_WORKER || false;
        var workers = {}, workerIdCounter = 0;
        var Papa2 = {};
        Papa2.parse = CsvToJson;
        Papa2.unparse = JsonToCsv;
        Papa2.RECORD_SEP = String.fromCharCode(30);
        Papa2.UNIT_SEP = String.fromCharCode(31);
        Papa2.BYTE_ORDER_MARK = "\uFEFF";
        Papa2.BAD_DELIMITERS = ["\r", "\n", '"', Papa2.BYTE_ORDER_MARK];
        Papa2.WORKERS_SUPPORTED = !IS_WORKER && !!global.Worker;
        Papa2.NODE_STREAM_INPUT = 1;
        Papa2.LocalChunkSize = 1024 * 1024 * 10;
        Papa2.RemoteChunkSize = 1024 * 1024 * 5;
        Papa2.DefaultDelimiter = ",";
        Papa2.Parser = Parser;
        Papa2.ParserHandle = ParserHandle;
        Papa2.NetworkStreamer = NetworkStreamer;
        Papa2.FileStreamer = FileStreamer;
        Papa2.StringStreamer = StringStreamer;
        Papa2.ReadableStreamStreamer = ReadableStreamStreamer;
        if (typeof PAPA_BROWSER_CONTEXT === "undefined") {
          Papa2.DuplexStreamStreamer = DuplexStreamStreamer;
        }
        if (global.jQuery) {
          var $ = global.jQuery;
          $.fn.parse = function(options) {
            var config = options.config || {};
            var queue = [];
            this.each(function(idx) {
              var supported = $(this).prop("tagName").toUpperCase() === "INPUT" && $(this).attr("type").toLowerCase() === "file" && global.FileReader;
              if (!supported || !this.files || this.files.length === 0)
                return true;
              for (var i = 0; i < this.files.length; i++) {
                queue.push({
                  file: this.files[i],
                  inputElem: this,
                  instanceConfig: $.extend({}, config)
                });
              }
            });
            parseNextFile();
            return this;
            function parseNextFile() {
              if (queue.length === 0) {
                if (isFunction(options.complete))
                  options.complete();
                return;
              }
              var f = queue[0];
              if (isFunction(options.before)) {
                var returned = options.before(f.file, f.inputElem);
                if (typeof returned === "object") {
                  if (returned.action === "abort") {
                    error("AbortError", f.file, f.inputElem, returned.reason);
                    return;
                  } else if (returned.action === "skip") {
                    fileComplete();
                    return;
                  } else if (typeof returned.config === "object")
                    f.instanceConfig = $.extend(f.instanceConfig, returned.config);
                } else if (returned === "skip") {
                  fileComplete();
                  return;
                }
              }
              var userCompleteFunc = f.instanceConfig.complete;
              f.instanceConfig.complete = function(results) {
                if (isFunction(userCompleteFunc))
                  userCompleteFunc(results, f.file, f.inputElem);
                fileComplete();
              };
              Papa2.parse(f.file, f.instanceConfig);
            }
            function error(name, file, elem, reason) {
              if (isFunction(options.error))
                options.error({ name }, file, elem, reason);
            }
            function fileComplete() {
              queue.splice(0, 1);
              parseNextFile();
            }
          };
        }
        if (IS_PAPA_WORKER) {
          global.onmessage = workerThreadReceivedMessage;
        }
        function CsvToJson(_input, _config) {
          _config = _config || {};
          var dynamicTyping = _config.dynamicTyping || false;
          if (isFunction(dynamicTyping)) {
            _config.dynamicTypingFunction = dynamicTyping;
            dynamicTyping = {};
          }
          _config.dynamicTyping = dynamicTyping;
          _config.transform = isFunction(_config.transform) ? _config.transform : false;
          if (_config.worker && Papa2.WORKERS_SUPPORTED) {
            var w = newWorker();
            w.userStep = _config.step;
            w.userChunk = _config.chunk;
            w.userComplete = _config.complete;
            w.userError = _config.error;
            _config.step = isFunction(_config.step);
            _config.chunk = isFunction(_config.chunk);
            _config.complete = isFunction(_config.complete);
            _config.error = isFunction(_config.error);
            delete _config.worker;
            w.postMessage({
              input: _input,
              config: _config,
              workerId: w.id
            });
            return;
          }
          var streamer = null;
          if (_input === Papa2.NODE_STREAM_INPUT && typeof PAPA_BROWSER_CONTEXT === "undefined") {
            streamer = new DuplexStreamStreamer(_config);
            return streamer.getStream();
          } else if (typeof _input === "string") {
            _input = stripBom(_input);
            if (_config.download)
              streamer = new NetworkStreamer(_config);
            else
              streamer = new StringStreamer(_config);
          } else if (_input.readable === true && isFunction(_input.read) && isFunction(_input.on)) {
            streamer = new ReadableStreamStreamer(_config);
          } else if (global.File && _input instanceof File || _input instanceof Object)
            streamer = new FileStreamer(_config);
          return streamer.stream(_input);
          function stripBom(string) {
            if (string.charCodeAt(0) === 65279) {
              return string.slice(1);
            }
            return string;
          }
        }
        function JsonToCsv(_input, _config) {
          var _quotes = false;
          var _writeHeader = true;
          var _delimiter = ",";
          var _newline = "\r\n";
          var _quoteChar = '"';
          var _escapedQuote = _quoteChar + _quoteChar;
          var _skipEmptyLines = false;
          var _columns = null;
          var _escapeFormulae = false;
          unpackConfig();
          var quoteCharRegex = new RegExp(escapeRegExp(_quoteChar), "g");
          if (typeof _input === "string")
            _input = JSON.parse(_input);
          if (Array.isArray(_input)) {
            if (!_input.length || Array.isArray(_input[0]))
              return serialize(null, _input, _skipEmptyLines);
            else if (typeof _input[0] === "object")
              return serialize(_columns || Object.keys(_input[0]), _input, _skipEmptyLines);
          } else if (typeof _input === "object") {
            if (typeof _input.data === "string")
              _input.data = JSON.parse(_input.data);
            if (Array.isArray(_input.data)) {
              if (!_input.fields)
                _input.fields = _input.meta && _input.meta.fields || _columns;
              if (!_input.fields)
                _input.fields = Array.isArray(_input.data[0]) ? _input.fields : typeof _input.data[0] === "object" ? Object.keys(_input.data[0]) : [];
              if (!Array.isArray(_input.data[0]) && typeof _input.data[0] !== "object")
                _input.data = [_input.data];
            }
            return serialize(_input.fields || [], _input.data || [], _skipEmptyLines);
          }
          throw new Error("Unable to serialize unrecognized input");
          function unpackConfig() {
            if (typeof _config !== "object")
              return;
            if (typeof _config.delimiter === "string" && !Papa2.BAD_DELIMITERS.filter(function(value) {
              return _config.delimiter.indexOf(value) !== -1;
            }).length) {
              _delimiter = _config.delimiter;
            }
            if (typeof _config.quotes === "boolean" || typeof _config.quotes === "function" || Array.isArray(_config.quotes))
              _quotes = _config.quotes;
            if (typeof _config.skipEmptyLines === "boolean" || typeof _config.skipEmptyLines === "string")
              _skipEmptyLines = _config.skipEmptyLines;
            if (typeof _config.newline === "string")
              _newline = _config.newline;
            if (typeof _config.quoteChar === "string")
              _quoteChar = _config.quoteChar;
            if (typeof _config.header === "boolean")
              _writeHeader = _config.header;
            if (Array.isArray(_config.columns)) {
              if (_config.columns.length === 0) throw new Error("Option columns is empty");
              _columns = _config.columns;
            }
            if (_config.escapeChar !== void 0) {
              _escapedQuote = _config.escapeChar + _quoteChar;
            }
            if (_config.escapeFormulae instanceof RegExp) {
              _escapeFormulae = _config.escapeFormulae;
            } else if (typeof _config.escapeFormulae === "boolean" && _config.escapeFormulae) {
              _escapeFormulae = /^[=+\-@\t\r].*$/;
            }
          }
          function serialize(fields, data, skipEmptyLines) {
            var csv = "";
            if (typeof fields === "string")
              fields = JSON.parse(fields);
            if (typeof data === "string")
              data = JSON.parse(data);
            var hasHeader = Array.isArray(fields) && fields.length > 0;
            var dataKeyedByField = !Array.isArray(data[0]);
            if (hasHeader && _writeHeader) {
              for (var i = 0; i < fields.length; i++) {
                if (i > 0)
                  csv += _delimiter;
                csv += safe(fields[i], i);
              }
              if (data.length > 0)
                csv += _newline;
            }
            for (var row = 0; row < data.length; row++) {
              var maxCol = hasHeader ? fields.length : data[row].length;
              var emptyLine = false;
              var nullLine = hasHeader ? Object.keys(data[row]).length === 0 : data[row].length === 0;
              if (skipEmptyLines && !hasHeader) {
                emptyLine = skipEmptyLines === "greedy" ? data[row].join("").trim() === "" : data[row].length === 1 && data[row][0].length === 0;
              }
              if (skipEmptyLines === "greedy" && hasHeader) {
                var line = [];
                for (var c = 0; c < maxCol; c++) {
                  var cx = dataKeyedByField ? fields[c] : c;
                  line.push(data[row][cx]);
                }
                emptyLine = line.join("").trim() === "";
              }
              if (!emptyLine) {
                for (var col = 0; col < maxCol; col++) {
                  if (col > 0 && !nullLine)
                    csv += _delimiter;
                  var colIdx = hasHeader && dataKeyedByField ? fields[col] : col;
                  csv += safe(data[row][colIdx], col);
                }
                if (row < data.length - 1 && (!skipEmptyLines || maxCol > 0 && !nullLine)) {
                  csv += _newline;
                }
              }
            }
            return csv;
          }
          function safe(str, col) {
            if (typeof str === "undefined" || str === null)
              return "";
            if (str.constructor === Date)
              return JSON.stringify(str).slice(1, 25);
            var needsQuotes = false;
            if (_escapeFormulae && typeof str === "string" && _escapeFormulae.test(str)) {
              str = "'" + str;
              needsQuotes = true;
            }
            var escapedQuoteStr = str.toString().replace(quoteCharRegex, _escapedQuote);
            needsQuotes = needsQuotes || _quotes === true || typeof _quotes === "function" && _quotes(str, col) || Array.isArray(_quotes) && _quotes[col] || hasAny(escapedQuoteStr, Papa2.BAD_DELIMITERS) || escapedQuoteStr.indexOf(_delimiter) > -1 || escapedQuoteStr.charAt(0) === " " || escapedQuoteStr.charAt(escapedQuoteStr.length - 1) === " ";
            return needsQuotes ? _quoteChar + escapedQuoteStr + _quoteChar : escapedQuoteStr;
          }
          function hasAny(str, substrings) {
            for (var i = 0; i < substrings.length; i++)
              if (str.indexOf(substrings[i]) > -1)
                return true;
            return false;
          }
        }
        function ChunkStreamer(config) {
          this._handle = null;
          this._finished = false;
          this._completed = false;
          this._halted = false;
          this._input = null;
          this._baseIndex = 0;
          this._partialLine = "";
          this._rowCount = 0;
          this._start = 0;
          this._nextChunk = null;
          this.isFirstChunk = true;
          this._completeResults = {
            data: [],
            errors: [],
            meta: {}
          };
          replaceConfig.call(this, config);
          this.parseChunk = function(chunk, isFakeChunk) {
            const skipFirstNLines = parseInt(this._config.skipFirstNLines) || 0;
            if (this.isFirstChunk && skipFirstNLines > 0) {
              let _newline = this._config.newline;
              if (!_newline) {
                const quoteChar = this._config.quoteChar || '"';
                _newline = this._handle.guessLineEndings(chunk, quoteChar);
              }
              const splitChunk = chunk.split(_newline);
              chunk = [...splitChunk.slice(skipFirstNLines)].join(_newline);
            }
            if (this.isFirstChunk && isFunction(this._config.beforeFirstChunk)) {
              var modifiedChunk = this._config.beforeFirstChunk(chunk);
              if (modifiedChunk !== void 0)
                chunk = modifiedChunk;
            }
            this.isFirstChunk = false;
            this._halted = false;
            var aggregate = this._partialLine + chunk;
            this._partialLine = "";
            var results = this._handle.parse(aggregate, this._baseIndex, !this._finished);
            if (this._handle.paused() || this._handle.aborted()) {
              this._halted = true;
              return;
            }
            var lastIndex = results.meta.cursor;
            if (!this._finished) {
              this._partialLine = aggregate.substring(lastIndex - this._baseIndex);
              this._baseIndex = lastIndex;
            }
            if (results && results.data)
              this._rowCount += results.data.length;
            var finishedIncludingPreview = this._finished || this._config.preview && this._rowCount >= this._config.preview;
            if (IS_PAPA_WORKER) {
              global.postMessage({
                results,
                workerId: Papa2.WORKER_ID,
                finished: finishedIncludingPreview
              });
            } else if (isFunction(this._config.chunk) && !isFakeChunk) {
              this._config.chunk(results, this._handle);
              if (this._handle.paused() || this._handle.aborted()) {
                this._halted = true;
                return;
              }
              results = void 0;
              this._completeResults = void 0;
            }
            if (!this._config.step && !this._config.chunk) {
              this._completeResults.data = this._completeResults.data.concat(results.data);
              this._completeResults.errors = this._completeResults.errors.concat(results.errors);
              this._completeResults.meta = results.meta;
            }
            if (!this._completed && finishedIncludingPreview && isFunction(this._config.complete) && (!results || !results.meta.aborted)) {
              this._config.complete(this._completeResults, this._input);
              this._completed = true;
            }
            if (!finishedIncludingPreview && (!results || !results.meta.paused))
              this._nextChunk();
            return results;
          };
          this._sendError = function(error) {
            if (isFunction(this._config.error))
              this._config.error(error);
            else if (IS_PAPA_WORKER && this._config.error) {
              global.postMessage({
                workerId: Papa2.WORKER_ID,
                error,
                finished: false
              });
            }
          };
          function replaceConfig(config2) {
            var configCopy = copy(config2);
            configCopy.chunkSize = parseInt(configCopy.chunkSize);
            if (!config2.step && !config2.chunk)
              configCopy.chunkSize = null;
            this._handle = new ParserHandle(configCopy);
            this._handle.streamer = this;
            this._config = configCopy;
          }
        }
        function NetworkStreamer(config) {
          config = config || {};
          if (!config.chunkSize)
            config.chunkSize = Papa2.RemoteChunkSize;
          ChunkStreamer.call(this, config);
          var xhr;
          if (IS_WORKER) {
            this._nextChunk = function() {
              this._readChunk();
              this._chunkLoaded();
            };
          } else {
            this._nextChunk = function() {
              this._readChunk();
            };
          }
          this.stream = function(url) {
            this._input = url;
            this._nextChunk();
          };
          this._readChunk = function() {
            if (this._finished) {
              this._chunkLoaded();
              return;
            }
            xhr = new XMLHttpRequest();
            if (this._config.withCredentials) {
              xhr.withCredentials = this._config.withCredentials;
            }
            if (!IS_WORKER) {
              xhr.onload = bindFunction(this._chunkLoaded, this);
              xhr.onerror = bindFunction(this._chunkError, this);
            }
            xhr.open(this._config.downloadRequestBody ? "POST" : "GET", this._input, !IS_WORKER);
            if (this._config.downloadRequestHeaders) {
              var headers = this._config.downloadRequestHeaders;
              for (var headerName in headers) {
                xhr.setRequestHeader(headerName, headers[headerName]);
              }
            }
            if (this._config.chunkSize) {
              var end = this._start + this._config.chunkSize - 1;
              xhr.setRequestHeader("Range", "bytes=" + this._start + "-" + end);
            }
            try {
              xhr.send(this._config.downloadRequestBody);
            } catch (err) {
              this._chunkError(err.message);
            }
            if (IS_WORKER && xhr.status === 0)
              this._chunkError();
          };
          this._chunkLoaded = function() {
            if (xhr.readyState !== 4)
              return;
            if (xhr.status < 200 || xhr.status >= 400) {
              this._chunkError();
              return;
            }
            this._start += this._config.chunkSize ? this._config.chunkSize : xhr.responseText.length;
            this._finished = !this._config.chunkSize || this._start >= getFileSize(xhr);
            this.parseChunk(xhr.responseText);
          };
          this._chunkError = function(errorMessage) {
            var errorText = xhr.statusText || errorMessage;
            this._sendError(new Error(errorText));
          };
          function getFileSize(xhr2) {
            var contentRange = xhr2.getResponseHeader("Content-Range");
            if (contentRange === null) {
              return -1;
            }
            return parseInt(contentRange.substring(contentRange.lastIndexOf("/") + 1));
          }
        }
        NetworkStreamer.prototype = Object.create(ChunkStreamer.prototype);
        NetworkStreamer.prototype.constructor = NetworkStreamer;
        function FileStreamer(config) {
          config = config || {};
          if (!config.chunkSize)
            config.chunkSize = Papa2.LocalChunkSize;
          ChunkStreamer.call(this, config);
          var reader, slice;
          var usingAsyncReader = typeof FileReader !== "undefined";
          this.stream = function(file) {
            this._input = file;
            slice = file.slice || file.webkitSlice || file.mozSlice;
            if (usingAsyncReader) {
              reader = new FileReader();
              reader.onload = bindFunction(this._chunkLoaded, this);
              reader.onerror = bindFunction(this._chunkError, this);
            } else
              reader = new FileReaderSync();
            this._nextChunk();
          };
          this._nextChunk = function() {
            if (!this._finished && (!this._config.preview || this._rowCount < this._config.preview))
              this._readChunk();
          };
          this._readChunk = function() {
            var input = this._input;
            if (this._config.chunkSize) {
              var end = Math.min(this._start + this._config.chunkSize, this._input.size);
              input = slice.call(input, this._start, end);
            }
            var txt = reader.readAsText(input, this._config.encoding);
            if (!usingAsyncReader)
              this._chunkLoaded({ target: { result: txt } });
          };
          this._chunkLoaded = function(event) {
            this._start += this._config.chunkSize;
            this._finished = !this._config.chunkSize || this._start >= this._input.size;
            this.parseChunk(event.target.result);
          };
          this._chunkError = function() {
            this._sendError(reader.error);
          };
        }
        FileStreamer.prototype = Object.create(ChunkStreamer.prototype);
        FileStreamer.prototype.constructor = FileStreamer;
        function StringStreamer(config) {
          config = config || {};
          ChunkStreamer.call(this, config);
          var remaining;
          this.stream = function(s) {
            remaining = s;
            return this._nextChunk();
          };
          this._nextChunk = function() {
            if (this._finished) return;
            var size = this._config.chunkSize;
            var chunk;
            if (size) {
              chunk = remaining.substring(0, size);
              remaining = remaining.substring(size);
            } else {
              chunk = remaining;
              remaining = "";
            }
            this._finished = !remaining;
            return this.parseChunk(chunk);
          };
        }
        StringStreamer.prototype = Object.create(StringStreamer.prototype);
        StringStreamer.prototype.constructor = StringStreamer;
        function ReadableStreamStreamer(config) {
          config = config || {};
          ChunkStreamer.call(this, config);
          var queue = [];
          var parseOnData = true;
          var streamHasEnded = false;
          this.pause = function() {
            ChunkStreamer.prototype.pause.apply(this, arguments);
            this._input.pause();
          };
          this.resume = function() {
            ChunkStreamer.prototype.resume.apply(this, arguments);
            this._input.resume();
          };
          this.stream = function(stream) {
            this._input = stream;
            this._input.on("data", this._streamData);
            this._input.on("end", this._streamEnd);
            this._input.on("error", this._streamError);
          };
          this._checkIsFinished = function() {
            if (streamHasEnded && queue.length === 1) {
              this._finished = true;
            }
          };
          this._nextChunk = function() {
            this._checkIsFinished();
            if (queue.length) {
              this.parseChunk(queue.shift());
            } else {
              parseOnData = true;
            }
          };
          this._streamData = bindFunction(function(chunk) {
            try {
              queue.push(typeof chunk === "string" ? chunk : chunk.toString(this._config.encoding));
              if (parseOnData) {
                parseOnData = false;
                this._checkIsFinished();
                this.parseChunk(queue.shift());
              }
            } catch (error) {
              this._streamError(error);
            }
          }, this);
          this._streamError = bindFunction(function(error) {
            this._streamCleanUp();
            this._sendError(error);
          }, this);
          this._streamEnd = bindFunction(function() {
            this._streamCleanUp();
            streamHasEnded = true;
            this._streamData("");
          }, this);
          this._streamCleanUp = bindFunction(function() {
            this._input.removeListener("data", this._streamData);
            this._input.removeListener("end", this._streamEnd);
            this._input.removeListener("error", this._streamError);
          }, this);
        }
        ReadableStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);
        ReadableStreamStreamer.prototype.constructor = ReadableStreamStreamer;
        function DuplexStreamStreamer(_config) {
          var Duplex = __require("stream").Duplex;
          var config = copy(_config);
          var parseOnWrite = true;
          var writeStreamHasFinished = false;
          var parseCallbackQueue = [];
          var stream = null;
          this._onCsvData = function(results) {
            var data = results.data;
            if (!stream.push(data) && !this._handle.paused()) {
              this._handle.pause();
            }
          };
          this._onCsvComplete = function() {
            stream.push(null);
          };
          config.step = bindFunction(this._onCsvData, this);
          config.complete = bindFunction(this._onCsvComplete, this);
          ChunkStreamer.call(this, config);
          this._nextChunk = function() {
            if (writeStreamHasFinished && parseCallbackQueue.length === 1) {
              this._finished = true;
            }
            if (parseCallbackQueue.length) {
              parseCallbackQueue.shift()();
            } else {
              parseOnWrite = true;
            }
          };
          this._addToParseQueue = function(chunk, callback) {
            parseCallbackQueue.push(bindFunction(function() {
              this.parseChunk(typeof chunk === "string" ? chunk : chunk.toString(config.encoding));
              if (isFunction(callback)) {
                return callback();
              }
            }, this));
            if (parseOnWrite) {
              parseOnWrite = false;
              this._nextChunk();
            }
          };
          this._onRead = function() {
            if (this._handle.paused()) {
              this._handle.resume();
            }
          };
          this._onWrite = function(chunk, encoding, callback) {
            this._addToParseQueue(chunk, callback);
          };
          this._onWriteComplete = function() {
            writeStreamHasFinished = true;
            this._addToParseQueue("");
          };
          this.getStream = function() {
            return stream;
          };
          stream = new Duplex({
            readableObjectMode: true,
            decodeStrings: false,
            read: bindFunction(this._onRead, this),
            write: bindFunction(this._onWrite, this)
          });
          stream.once("finish", bindFunction(this._onWriteComplete, this));
        }
        if (typeof PAPA_BROWSER_CONTEXT === "undefined") {
          DuplexStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);
          DuplexStreamStreamer.prototype.constructor = DuplexStreamStreamer;
        }
        function ParserHandle(_config) {
          var MAX_FLOAT = Math.pow(2, 53);
          var MIN_FLOAT = -MAX_FLOAT;
          var FLOAT = /^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/;
          var ISO_DATE = /^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/;
          var self2 = this;
          var _stepCounter = 0;
          var _rowCounter = 0;
          var _input;
          var _parser;
          var _paused = false;
          var _aborted = false;
          var _delimiterError;
          var _fields = [];
          var _results = {
            // The last results returned from the parser
            data: [],
            errors: [],
            meta: {}
          };
          if (isFunction(_config.step)) {
            var userStep = _config.step;
            _config.step = function(results) {
              _results = results;
              if (needsHeaderRow())
                processResults();
              else {
                processResults();
                if (_results.data.length === 0)
                  return;
                _stepCounter += results.data.length;
                if (_config.preview && _stepCounter > _config.preview)
                  _parser.abort();
                else {
                  _results.data = _results.data[0];
                  userStep(_results, self2);
                }
              }
            };
          }
          this.parse = function(input, baseIndex, ignoreLastRow) {
            var quoteChar = _config.quoteChar || '"';
            if (!_config.newline)
              _config.newline = this.guessLineEndings(input, quoteChar);
            _delimiterError = false;
            if (!_config.delimiter) {
              var delimGuess = guessDelimiter(input, _config.newline, _config.skipEmptyLines, _config.comments, _config.delimitersToGuess);
              if (delimGuess.successful)
                _config.delimiter = delimGuess.bestDelimiter;
              else {
                _delimiterError = true;
                _config.delimiter = Papa2.DefaultDelimiter;
              }
              _results.meta.delimiter = _config.delimiter;
            } else if (isFunction(_config.delimiter)) {
              _config.delimiter = _config.delimiter(input);
              _results.meta.delimiter = _config.delimiter;
            }
            var parserConfig = copy(_config);
            if (_config.preview && _config.header)
              parserConfig.preview++;
            _input = input;
            _parser = new Parser(parserConfig);
            _results = _parser.parse(_input, baseIndex, ignoreLastRow);
            processResults();
            return _paused ? { meta: { paused: true } } : _results || { meta: { paused: false } };
          };
          this.paused = function() {
            return _paused;
          };
          this.pause = function() {
            _paused = true;
            _parser.abort();
            _input = isFunction(_config.chunk) ? "" : _input.substring(_parser.getCharIndex());
          };
          this.resume = function() {
            if (self2.streamer._halted) {
              _paused = false;
              self2.streamer.parseChunk(_input, true);
            } else {
              setTimeout(self2.resume, 3);
            }
          };
          this.aborted = function() {
            return _aborted;
          };
          this.abort = function() {
            _aborted = true;
            _parser.abort();
            _results.meta.aborted = true;
            if (isFunction(_config.complete))
              _config.complete(_results);
            _input = "";
          };
          this.guessLineEndings = function(input, quoteChar) {
            input = input.substring(0, 1024 * 1024);
            var re = new RegExp(escapeRegExp(quoteChar) + "([^]*?)" + escapeRegExp(quoteChar), "gm");
            input = input.replace(re, "");
            var r = input.split("\r");
            var n = input.split("\n");
            var nAppearsFirst = n.length > 1 && n[0].length < r[0].length;
            if (r.length === 1 || nAppearsFirst)
              return "\n";
            var numWithN = 0;
            for (var i = 0; i < r.length; i++) {
              if (r[i][0] === "\n")
                numWithN++;
            }
            return numWithN >= r.length / 2 ? "\r\n" : "\r";
          };
          function testEmptyLine(s) {
            return _config.skipEmptyLines === "greedy" ? s.join("").trim() === "" : s.length === 1 && s[0].length === 0;
          }
          function testFloat(s) {
            if (FLOAT.test(s)) {
              var floatValue = parseFloat(s);
              if (floatValue > MIN_FLOAT && floatValue < MAX_FLOAT) {
                return true;
              }
            }
            return false;
          }
          function processResults() {
            if (_results && _delimiterError) {
              addError("Delimiter", "UndetectableDelimiter", "Unable to auto-detect delimiting character; defaulted to '" + Papa2.DefaultDelimiter + "'");
              _delimiterError = false;
            }
            if (_config.skipEmptyLines) {
              _results.data = _results.data.filter(function(d) {
                return !testEmptyLine(d);
              });
            }
            if (needsHeaderRow())
              fillHeaderFields();
            return applyHeaderAndDynamicTypingAndTransformation();
          }
          function needsHeaderRow() {
            return _config.header && _fields.length === 0;
          }
          function fillHeaderFields() {
            if (!_results)
              return;
            function addHeader(header, i2) {
              if (isFunction(_config.transformHeader))
                header = _config.transformHeader(header, i2);
              _fields.push(header);
            }
            if (Array.isArray(_results.data[0])) {
              for (var i = 0; needsHeaderRow() && i < _results.data.length; i++)
                _results.data[i].forEach(addHeader);
              _results.data.splice(0, 1);
            } else
              _results.data.forEach(addHeader);
          }
          function shouldApplyDynamicTyping(field) {
            if (_config.dynamicTypingFunction && _config.dynamicTyping[field] === void 0) {
              _config.dynamicTyping[field] = _config.dynamicTypingFunction(field);
            }
            return (_config.dynamicTyping[field] || _config.dynamicTyping) === true;
          }
          function parseDynamic(field, value) {
            if (shouldApplyDynamicTyping(field)) {
              if (value === "true" || value === "TRUE")
                return true;
              else if (value === "false" || value === "FALSE")
                return false;
              else if (testFloat(value))
                return parseFloat(value);
              else if (ISO_DATE.test(value))
                return new Date(value);
              else
                return value === "" ? null : value;
            }
            return value;
          }
          function applyHeaderAndDynamicTypingAndTransformation() {
            if (!_results || !_config.header && !_config.dynamicTyping && !_config.transform)
              return _results;
            function processRow(rowSource, i) {
              var row = _config.header ? {} : [];
              var j;
              for (j = 0; j < rowSource.length; j++) {
                var field = j;
                var value = rowSource[j];
                if (_config.header)
                  field = j >= _fields.length ? "__parsed_extra" : _fields[j];
                if (_config.transform)
                  value = _config.transform(value, field);
                value = parseDynamic(field, value);
                if (field === "__parsed_extra") {
                  row[field] = row[field] || [];
                  row[field].push(value);
                } else
                  row[field] = value;
              }
              if (_config.header) {
                if (j > _fields.length)
                  addError("FieldMismatch", "TooManyFields", "Too many fields: expected " + _fields.length + " fields but parsed " + j, _rowCounter + i);
                else if (j < _fields.length)
                  addError("FieldMismatch", "TooFewFields", "Too few fields: expected " + _fields.length + " fields but parsed " + j, _rowCounter + i);
              }
              return row;
            }
            var incrementBy = 1;
            if (!_results.data.length || Array.isArray(_results.data[0])) {
              _results.data = _results.data.map(processRow);
              incrementBy = _results.data.length;
            } else
              _results.data = processRow(_results.data, 0);
            if (_config.header && _results.meta)
              _results.meta.fields = _fields;
            _rowCounter += incrementBy;
            return _results;
          }
          function guessDelimiter(input, newline, skipEmptyLines, comments, delimitersToGuess) {
            var bestDelim, bestDelta, fieldCountPrevRow, maxFieldCount;
            delimitersToGuess = delimitersToGuess || [",", "	", "|", ";", Papa2.RECORD_SEP, Papa2.UNIT_SEP];
            for (var i = 0; i < delimitersToGuess.length; i++) {
              var delim = delimitersToGuess[i];
              var delta = 0, avgFieldCount = 0, emptyLinesCount = 0;
              fieldCountPrevRow = void 0;
              var preview = new Parser({
                comments,
                delimiter: delim,
                newline,
                preview: 10
              }).parse(input);
              for (var j = 0; j < preview.data.length; j++) {
                if (skipEmptyLines && testEmptyLine(preview.data[j])) {
                  emptyLinesCount++;
                  continue;
                }
                var fieldCount = preview.data[j].length;
                avgFieldCount += fieldCount;
                if (typeof fieldCountPrevRow === "undefined") {
                  fieldCountPrevRow = fieldCount;
                  continue;
                } else if (fieldCount > 0) {
                  delta += Math.abs(fieldCount - fieldCountPrevRow);
                  fieldCountPrevRow = fieldCount;
                }
              }
              if (preview.data.length > 0)
                avgFieldCount /= preview.data.length - emptyLinesCount;
              if ((typeof bestDelta === "undefined" || delta <= bestDelta) && (typeof maxFieldCount === "undefined" || avgFieldCount > maxFieldCount) && avgFieldCount > 1.99) {
                bestDelta = delta;
                bestDelim = delim;
                maxFieldCount = avgFieldCount;
              }
            }
            _config.delimiter = bestDelim;
            return {
              successful: !!bestDelim,
              bestDelimiter: bestDelim
            };
          }
          function addError(type, code, msg, row) {
            var error = {
              type,
              code,
              message: msg
            };
            if (row !== void 0) {
              error.row = row;
            }
            _results.errors.push(error);
          }
        }
        function escapeRegExp(string) {
          return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
        }
        function Parser(config) {
          config = config || {};
          var delim = config.delimiter;
          var newline = config.newline;
          var comments = config.comments;
          var step = config.step;
          var preview = config.preview;
          var fastMode = config.fastMode;
          var quoteChar;
          var renamedHeaders = null;
          var headerParsed = false;
          if (config.quoteChar === void 0 || config.quoteChar === null) {
            quoteChar = '"';
          } else {
            quoteChar = config.quoteChar;
          }
          var escapeChar = quoteChar;
          if (config.escapeChar !== void 0) {
            escapeChar = config.escapeChar;
          }
          if (typeof delim !== "string" || Papa2.BAD_DELIMITERS.indexOf(delim) > -1)
            delim = ",";
          if (comments === delim)
            throw new Error("Comment character same as delimiter");
          else if (comments === true)
            comments = "#";
          else if (typeof comments !== "string" || Papa2.BAD_DELIMITERS.indexOf(comments) > -1)
            comments = false;
          if (newline !== "\n" && newline !== "\r" && newline !== "\r\n")
            newline = "\n";
          var cursor = 0;
          var aborted = false;
          this.parse = function(input, baseIndex, ignoreLastRow) {
            if (typeof input !== "string")
              throw new Error("Input must be a string");
            var inputLen = input.length, delimLen = delim.length, newlineLen = newline.length, commentsLen = comments.length;
            var stepIsFunction = isFunction(step);
            cursor = 0;
            var data = [], errors = [], row = [], lastCursor = 0;
            if (!input)
              return returnable();
            if (fastMode || fastMode !== false && input.indexOf(quoteChar) === -1) {
              var rows = input.split(newline);
              for (var i = 0; i < rows.length; i++) {
                row = rows[i];
                cursor += row.length;
                if (i !== rows.length - 1)
                  cursor += newline.length;
                else if (ignoreLastRow)
                  return returnable();
                if (comments && row.substring(0, commentsLen) === comments)
                  continue;
                if (stepIsFunction) {
                  data = [];
                  pushRow(row.split(delim));
                  doStep();
                  if (aborted)
                    return returnable();
                } else
                  pushRow(row.split(delim));
                if (preview && i >= preview) {
                  data = data.slice(0, preview);
                  return returnable(true);
                }
              }
              return returnable();
            }
            var nextDelim = input.indexOf(delim, cursor);
            var nextNewline = input.indexOf(newline, cursor);
            var quoteCharRegex = new RegExp(escapeRegExp(escapeChar) + escapeRegExp(quoteChar), "g");
            var quoteSearch = input.indexOf(quoteChar, cursor);
            for (; ; ) {
              if (input[cursor] === quoteChar) {
                quoteSearch = cursor;
                cursor++;
                for (; ; ) {
                  quoteSearch = input.indexOf(quoteChar, quoteSearch + 1);
                  if (quoteSearch === -1) {
                    if (!ignoreLastRow) {
                      errors.push({
                        type: "Quotes",
                        code: "MissingQuotes",
                        message: "Quoted field unterminated",
                        row: data.length,
                        // row has yet to be inserted
                        index: cursor
                      });
                    }
                    return finish();
                  }
                  if (quoteSearch === inputLen - 1) {
                    var value = input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar);
                    return finish(value);
                  }
                  if (quoteChar === escapeChar && input[quoteSearch + 1] === escapeChar) {
                    quoteSearch++;
                    continue;
                  }
                  if (quoteChar !== escapeChar && quoteSearch !== 0 && input[quoteSearch - 1] === escapeChar) {
                    continue;
                  }
                  if (nextDelim !== -1 && nextDelim < quoteSearch + 1) {
                    nextDelim = input.indexOf(delim, quoteSearch + 1);
                  }
                  if (nextNewline !== -1 && nextNewline < quoteSearch + 1) {
                    nextNewline = input.indexOf(newline, quoteSearch + 1);
                  }
                  var checkUpTo = nextNewline === -1 ? nextDelim : Math.min(nextDelim, nextNewline);
                  var spacesBetweenQuoteAndDelimiter = extraSpaces(checkUpTo);
                  if (input.substr(quoteSearch + 1 + spacesBetweenQuoteAndDelimiter, delimLen) === delim) {
                    row.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));
                    cursor = quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen;
                    if (input[quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen] !== quoteChar) {
                      quoteSearch = input.indexOf(quoteChar, cursor);
                    }
                    nextDelim = input.indexOf(delim, cursor);
                    nextNewline = input.indexOf(newline, cursor);
                    break;
                  }
                  var spacesBetweenQuoteAndNewLine = extraSpaces(nextNewline);
                  if (input.substring(quoteSearch + 1 + spacesBetweenQuoteAndNewLine, quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen) === newline) {
                    row.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));
                    saveRow(quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen);
                    nextDelim = input.indexOf(delim, cursor);
                    quoteSearch = input.indexOf(quoteChar, cursor);
                    if (stepIsFunction) {
                      doStep();
                      if (aborted)
                        return returnable();
                    }
                    if (preview && data.length >= preview)
                      return returnable(true);
                    break;
                  }
                  errors.push({
                    type: "Quotes",
                    code: "InvalidQuotes",
                    message: "Trailing quote on quoted field is malformed",
                    row: data.length,
                    // row has yet to be inserted
                    index: cursor
                  });
                  quoteSearch++;
                  continue;
                }
                continue;
              }
              if (comments && row.length === 0 && input.substring(cursor, cursor + commentsLen) === comments) {
                if (nextNewline === -1)
                  return returnable();
                cursor = nextNewline + newlineLen;
                nextNewline = input.indexOf(newline, cursor);
                nextDelim = input.indexOf(delim, cursor);
                continue;
              }
              if (nextDelim !== -1 && (nextDelim < nextNewline || nextNewline === -1)) {
                row.push(input.substring(cursor, nextDelim));
                cursor = nextDelim + delimLen;
                nextDelim = input.indexOf(delim, cursor);
                continue;
              }
              if (nextNewline !== -1) {
                row.push(input.substring(cursor, nextNewline));
                saveRow(nextNewline + newlineLen);
                if (stepIsFunction) {
                  doStep();
                  if (aborted)
                    return returnable();
                }
                if (preview && data.length >= preview)
                  return returnable(true);
                continue;
              }
              break;
            }
            return finish();
            function pushRow(row2) {
              data.push(row2);
              lastCursor = cursor;
            }
            function extraSpaces(index) {
              var spaceLength = 0;
              if (index !== -1) {
                var textBetweenClosingQuoteAndIndex = input.substring(quoteSearch + 1, index);
                if (textBetweenClosingQuoteAndIndex && textBetweenClosingQuoteAndIndex.trim() === "") {
                  spaceLength = textBetweenClosingQuoteAndIndex.length;
                }
              }
              return spaceLength;
            }
            function finish(value2) {
              if (ignoreLastRow)
                return returnable();
              if (typeof value2 === "undefined")
                value2 = input.substring(cursor);
              row.push(value2);
              cursor = inputLen;
              pushRow(row);
              if (stepIsFunction)
                doStep();
              return returnable();
            }
            function saveRow(newCursor) {
              cursor = newCursor;
              pushRow(row);
              row = [];
              nextNewline = input.indexOf(newline, cursor);
            }
            function returnable(stopped) {
              if (config.header && !baseIndex && data.length && !headerParsed) {
                const result = data[0];
                const headerCount = /* @__PURE__ */ Object.create(null);
                const usedHeaders = new Set(result);
                let duplicateHeaders = false;
                for (let i2 = 0; i2 < result.length; i2++) {
                  let header = result[i2];
                  if (isFunction(config.transformHeader))
                    header = config.transformHeader(header, i2);
                  if (!headerCount[header]) {
                    headerCount[header] = 1;
                    result[i2] = header;
                  } else {
                    let newHeader;
                    let suffixCount = headerCount[header];
                    do {
                      newHeader = `${header}_${suffixCount}`;
                      suffixCount++;
                    } while (usedHeaders.has(newHeader));
                    usedHeaders.add(newHeader);
                    result[i2] = newHeader;
                    headerCount[header]++;
                    duplicateHeaders = true;
                    if (renamedHeaders === null) {
                      renamedHeaders = {};
                    }
                    renamedHeaders[newHeader] = header;
                  }
                  usedHeaders.add(header);
                }
                if (duplicateHeaders) {
                  console.warn("Duplicate headers found and renamed.");
                }
                headerParsed = true;
              }
              return {
                data,
                errors,
                meta: {
                  delimiter: delim,
                  linebreak: newline,
                  aborted,
                  truncated: !!stopped,
                  cursor: lastCursor + (baseIndex || 0),
                  renamedHeaders
                }
              };
            }
            function doStep() {
              step(returnable());
              data = [];
              errors = [];
            }
          };
          this.abort = function() {
            aborted = true;
          };
          this.getCharIndex = function() {
            return cursor;
          };
        }
        function newWorker() {
          if (!Papa2.WORKERS_SUPPORTED)
            return false;
          var workerUrl = getWorkerBlob();
          var w = new global.Worker(workerUrl);
          w.onmessage = mainThreadReceivedMessage;
          w.id = workerIdCounter++;
          workers[w.id] = w;
          return w;
        }
        function mainThreadReceivedMessage(e) {
          var msg = e.data;
          var worker = workers[msg.workerId];
          var aborted = false;
          if (msg.error)
            worker.userError(msg.error, msg.file);
          else if (msg.results && msg.results.data) {
            var abort = function() {
              aborted = true;
              completeWorker(msg.workerId, { data: [], errors: [], meta: { aborted: true } });
            };
            var handle = {
              abort,
              pause: notImplemented,
              resume: notImplemented
            };
            if (isFunction(worker.userStep)) {
              for (var i = 0; i < msg.results.data.length; i++) {
                worker.userStep({
                  data: msg.results.data[i],
                  errors: msg.results.errors,
                  meta: msg.results.meta
                }, handle);
                if (aborted)
                  break;
              }
              delete msg.results;
            } else if (isFunction(worker.userChunk)) {
              worker.userChunk(msg.results, handle, msg.file);
              delete msg.results;
            }
          }
          if (msg.finished && !aborted)
            completeWorker(msg.workerId, msg.results);
        }
        function completeWorker(workerId, results) {
          var worker = workers[workerId];
          if (isFunction(worker.userComplete))
            worker.userComplete(results);
          worker.terminate();
          delete workers[workerId];
        }
        function notImplemented() {
          throw new Error("Not implemented.");
        }
        function workerThreadReceivedMessage(e) {
          var msg = e.data;
          if (typeof Papa2.WORKER_ID === "undefined" && msg)
            Papa2.WORKER_ID = msg.workerId;
          if (typeof msg.input === "string") {
            global.postMessage({
              workerId: Papa2.WORKER_ID,
              results: Papa2.parse(msg.input, msg.config),
              finished: true
            });
          } else if (global.File && msg.input instanceof File || msg.input instanceof Object) {
            var results = Papa2.parse(msg.input, msg.config);
            if (results)
              global.postMessage({
                workerId: Papa2.WORKER_ID,
                results,
                finished: true
              });
          }
        }
        function copy(obj) {
          if (typeof obj !== "object" || obj === null)
            return obj;
          var cpy = Array.isArray(obj) ? [] : {};
          for (var key in obj)
            cpy[key] = copy(obj[key]);
          return cpy;
        }
        function bindFunction(f, self2) {
          return function() {
            f.apply(self2, arguments);
          };
        }
        function isFunction(func) {
          return typeof func === "function";
        }
        return Papa2;
      });
    }
  });

  // src/index.ts
  var index_exports = {};
  __export(index_exports, {
    decodeReportCSV: () => decodeReportCSV,
    decodeReportData: () => decodeReportData,
    decodeReportDataToJSON: () => decodeReportDataToJSON,
    decodeReportDataToXML: () => decodeReportDataToXML,
    jsonStrToXML: () => jsonStrToXML,
    jsonToXML: () => jsonToXML,
    jsonToXMLTree: () => jsonToXMLTree,
    miniNodeToXml: () => miniNodeToXml
  });

  // ../xweb/src/miniprogram/ast.ts
  function miniNodeToXml(node, indentLevel = 0) {
    var _a3, _b;
    const indent = "  ".repeat(indentLevel);
    const attributes = serializeAttributes(node);
    const hasChildren = node.children && node.children.length > 0;
    const nodeValue = node.textContent || node.innerText || node.nodeValue || "";
    const hasContent = nodeValue !== "";
    let tagName = ((_a3 = node.tagName) == null ? void 0 : _a3.toLowerCase()) || ((_b = node.nodeName) == null ? void 0 : _b.toLowerCase());
    if (!tagName) {
      return "";
    }
    if (tagName.startsWith("#")) {
      tagName = tagName.slice(1);
    }
    let xml = `${indent}<${tagName}${attributes}`;
    if (!hasChildren && !hasContent) {
      return `${xml} />
`;
    }
    xml += ">";
    if (hasContent && !hasChildren) {
      let value = nodeValue;
      if (nodeValue.includes("\n")) {
        value = nodeValue.split("\n").join(`  `);
      }
      return `${xml}${escapeXml(value)}</${tagName}>
`;
    }
    xml += "\n";
    if (node.children) {
      node.children.forEach((child) => {
        xml += miniNodeToXml(child, indentLevel + 1);
      });
      if (hasContent) {
        xml += `${indent}  ${escapeXml(nodeValue)}
`;
      }
    }
    xml += `${indent}</${tagName}>
`;
    return xml;
  }
  function serializeAttributes(node) {
    if (!node)
      return "";
    const attrs = [];
    const nodeId = node.id || node.nodeId;
    if (nodeId) {
      attrs.push(`id="${nodeId}"`);
    }
    if (typeof node.attributes === "object" && node.attributes) {
      if (Array.isArray(node.attributes)) {
        if (node.attributes && node.attributes.length > 0) {
          attrs.push(`attr="${serializeArray(node.attributes)}"`);
        }
      } else {
        for (const key in node.attributes) {
          if (node.attributes[key] !== void 0) {
            attrs.push(`${key}="${escapeXml(node.attributes[key])}"`);
          }
        }
      }
    }
    const events = node.events || node.wxEvents;
    if (events && events.length > 0) {
      attrs.push(`event="${serializeArray(events)}"`);
    }
    const listeners = node.listeners;
    if (listeners && listeners.length > 0) {
      attrs.push(`listener="${serializeArray(listeners)}"`);
    }
    if (node.classList && node.classList.length > 0) {
      attrs.push(`class="${node.classList.join(" ")}"`);
    }
    if (node.zIndex) {
      attrs.push(`z="${node.zIndex}"`);
    }
    return attrs.length > 0 ? ` ${attrs.join(" ")}` : "";
  }
  function serializeArray(arr) {
    return `[${arr.map((item) => `'${escapeXml(item)}'`).join(", ")}]`;
  }
  function escapeXml(str) {
    if (!str)
      return "";
    return str.replace(/[&<>'"]/g, (match) => {
      switch (match) {
        case "&":
          return "&amp;";
        case "<":
          return "&lt;";
        case ">":
          return "&gt;";
        case '"':
          return "&quot;";
        case "'":
          return "&apos;";
        default:
          return match;
      }
    });
  }

  // src/csv.ts
  var import_papaparse = __toESM(require_papaparse(), 1);
  function parseCSV(csvText) {
    return import_papaparse.default.parse(csvText, {
      header: true,
      skipEmptyLines: true
    }).data;
  }

  // src/decode/csv.ts
  function decodeReportCSV(data) {
    const lines = data.split("\n");
    const header = lines[0].split(",");
    const nodes = [];
    const csvData = parseCSV(data);
    for (let i = 0; i < csvData.length; i++) {
      const row = csvData[i];
      const obj = {};
      for (let j = 0; j < header.length; j++) {
        const key = header[j];
        const value = row[key];
        switch (key) {
          case "id":
            obj[key] = Number(value);
            break;
          case "attributes":
            try {
              if (value)
                obj[key] = JSON.parse(value);
            } catch (e) {
              console.error("JSON parse error", value);
              throw e;
            }
            break;
          case "isVisible":
          case "isInteractive":
            obj[key] = value === "1";
            break;
          default:
            obj[key] = value;
            break;
        }
      }
      nodes.push(obj);
    }
    const nodesMap = {};
    nodes.forEach((node) => {
      nodesMap[node.id] = node;
    });
    nodes.forEach((item) => {
      var _a3, _b, _c;
      if (item.wxEvents) {
        item.wxEvents = (_a3 = item.wxEvents) == null ? void 0 : _a3.split("|");
      }
      if (item.listeners) {
        item.listeners = (_b = item.listeners) == null ? void 0 : _b.split("|");
      }
      while ((_c = item.xpath) == null ? void 0 : _c.includes("#")) {
        const xpaths = item.xpath.split("#");
        const commonPathID = xpaths[0];
        const parentNode = nodesMap[commonPathID];
        if (!parentNode) {
          break;
        }
        xpaths[0] = parentNode.xpath || "";
        item.xpath = xpaths.join("");
      }
    });
    return nodes;
  }

  // ../miniprogram-elements-report/src/browser/init.ts
  function initGetEventListeners() {
    var _a3;
    if (typeof window === "undefined") {
      return;
    }
    window.isPdfViewer = !!((_a3 = document == null ? void 0 : document.body) == null ? void 0 : _a3.querySelector('body > embed[type="application/pdf"][width="100%"]'));
    if (!window.isPdfViewer) {
      (() => {
        if (window._eventListenerTrackerInitialized)
          return;
        window._eventListenerTrackerInitialized = true;
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        const eventListenersMap = /* @__PURE__ */ new WeakMap();
        EventTarget.prototype.addEventListener = function(type, listener, options) {
          if (typeof listener === "function") {
            let listeners = eventListenersMap.get(this);
            if (!listeners) {
              listeners = [];
              eventListenersMap.set(this, listeners);
            }
            listeners.push({
              type,
              listener,
              listenerPreview: listener.toString().slice(0, 100),
              options
            });
          }
          options = typeof options === "object" ? options : { capture: !!options };
          return originalAddEventListener.call(this, type, listener, options);
        };
        window.getEventListenersForNode = (node) => {
          const listeners = eventListenersMap.get(node) || [];
          return listeners.map(({ type, listenerPreview, options }) => ({
            type,
            listenerPreview,
            options
          }));
        };
      })();
    }
  }

  // ../miniprogram-elements-report/src/env.ts
  var _a;
  var rawConsoleLog = (typeof window !== "undefined" ? (_a = window.__WX_MER__) == null ? void 0 : _a.consoleLog : void 0) || console.log;
  var _a2;
  var debug = (_a2 = typeof window !== "undefined" ? window.__WX_MER_DEBUG__ : void 0) != null ? _a2 : false;
  if (debug) {
    consoleLog("\u{1F41B} [DEBUG MODE]", window.__WX_MER_DEBUG__);
  }
  function consoleLog(...info) {
    rawConsoleLog(...info);
  }
  var navigator = typeof window !== "undefined" ? window.navigator : void 0;
  var isMiniProgram = /miniProgram/i.test(typeof navigator !== "undefined" ? navigator.userAgent : "");
  var isMiniProgramWebview = false;
  (() => {
    if (typeof document === "undefined") {
      return;
    }
    document.addEventListener("WeixinJSBridgeReady", () => {
      isMiniProgramWebview = window.__wxjs_environment === "miniprogram";
    });
  })();
  function isAndroid(userAgent) {
    userAgent = (userAgent || (navigator == null ? void 0 : navigator.userAgent) || "").toLowerCase();
    return /android|adr/i.test(userAgent);
  }
  function isIOS(userAgent) {
    userAgent = (userAgent || (navigator == null ? void 0 : navigator.userAgent) || "").toLowerCase();
    return /iphone|ipad|ipod/i.test(userAgent);
  }
  var env = {
    isAndroid: isAndroid(),
    isIOS: isIOS(),
    desc: `${isAndroid() ? "Android" : isIOS() ? "iOS" : "Unknown"} Mini Program`
  };

  // ../miniprogram-elements-report/src/buildDomTree.ts
  initGetEventListeners();

  // ../utils/src/constants.ts
  var props = [
    "id",
    "xpath",
    "textContent",
    "attributes",
    "wxEvents",
    "isVisible",
    "isInteractive",
    "listeners"
  ];

  // src/decode/xml.ts
  function jsonToXMLTree(nodes) {
    var _a3, _b;
    const virtualRoot = {
      tagName: "root",
      attributes: {},
      children: []
    };
    for (const node of nodes) {
      if (typeof node.id === "undefined") {
        continue;
      }
      const segments = (_a3 = node.xpath) == null ? void 0 : _a3.split("/").filter((s) => s.length > 0);
      if (!segments || segments.length === 0) {
        console.log("\u8282\u70B9\u6CA1\u6709 xpath", node);
        continue;
      }
      let currentNode = virtualRoot;
      const value = node.textContent || "";
      for (let i = 0; i < segments.length; i++) {
        const isLast = i === segments.length - 1;
        const segment = segments[i];
        if (segment.startsWith("@")) {
          currentNode.attributes = node.attributes || {};
          break;
        }
        let childNode = (_b = currentNode.children) == null ? void 0 : _b.find((n) => n.tagName === segment);
        if (!childNode) {
          childNode = {
            tagName: segment,
            attributes: {},
            children: []
          };
          currentNode.children.push(childNode);
        }
        currentNode = childNode;
        if (isLast) {
          props.forEach((prop) => {
            if (node[prop] !== void 0) {
              childNode[prop] = node[prop];
            }
          });
        }
      }
    }
    return virtualRoot.children[0];
  }

  // src/index.ts
  function decodeReportDataToJSON(csv) {
    const json = decodeReportCSV(csv);
    return json;
  }
  function decodeReportDataToXML(csv) {
    const json = decodeReportCSV(csv);
    const jsonTree = jsonToXMLTree(json);
    const xml = miniNodeToXml(jsonTree);
    return xml;
  }
  function jsonToXML(json) {
    const jsonTree = jsonToXMLTree(json);
    const xml = miniNodeToXml(jsonTree);
    return xml;
  }
  function jsonStrToXML(jsonString) {
    const json = JSON.parse(jsonString);
    return jsonToXML(json);
  }
  var decodeReportData = {
    toJSON: decodeReportDataToJSON,
    toXML: decodeReportDataToXML,
    jsonToXML,
    jsonStrToXML
  };
  return __toCommonJS(index_exports);
})();
/*! Bundled license information:

papaparse/papaparse.js:
  (* @license
  Papa Parse
  v5.5.3
  https://github.com/mholt/PapaParse
  License: MIT
  *)
*/