#!/usr/bin/env python3
"""
测试优化版本的正确性和性能
创建测试数据，验证优化前后结果一致性，并提供详细的性能分析

使用示例:
  # 标准测试
  python test_optimization.py --num_sessions=1000 --avg_session_length=5

  # 大规模测试
  python test_optimization.py --large_test

  # 生成性能图表
  python test_optimization.py --plot --num_sessions=500

  # 保留测试文件
  python test_optimization.py --keep_files --num_sessions=100
"""

import pandas as pd
import json
import os
import tempfile
import argparse
from datetime import datetime


def create_test_data(num_sessions=1000, avg_session_length=5):
    """创建测试数据"""
    print(f"创建测试数据: {num_sessions} 个sessions, 平均长度 {avg_session_length}")
    
    data = []
    session_id_base = "hash=123456&ts=1743774812155&host=&version=671103404&device=2"
    
    for session_idx in range(num_sessions):
        session_length = max(1, avg_session_length + (session_idx % 10) - 5)  # 变化长度
        
        for event_idx in range(session_length):
            timestamp = 1743774845601 + session_idx * 1000 + event_idx * 100
            data.append({
                'sessionid_': f"{session_id_base}#{session_idx}#{timestamp}",
                'appid_': f"app_{session_idx % 10}",
                'clickitem_': f"item_{event_idx}",
                'timestamp': timestamp,
                'other_data': f"data_{session_idx}_{event_idx}"
            })
    
    df = pd.DataFrame(data)
    print(f"生成数据: {len(df)} 行")
    return df


def save_test_pickle(df, filename):
    """保存测试数据为pickle文件"""
    df.to_pickle(filename)
    print(f"保存测试数据到: {filename}")


def run_test_script(script_name, input_dir, pickle_file, output_file):
    """运行测试脚本并记录详细的性能指标"""
    import subprocess
    import time
    import psutil
    import threading

    if script_name == "group_by_session_id_ultra_optimized.py":
        cmd = [
            "python", script_name,
            f"--input_pickle_dir={input_dir}",
            f"--target_pickles={pickle_file}",
            "--trajectory_length_min=1",
            "--trajectory_length_max=20",
            f"--output_json_path={output_file}"
        ]
    else:
        cmd = [
            "python", script_name,
            "--function=group_by_session_id",
            f"--input_pickle_dir={input_dir}",
            f"--target_pickles={pickle_file}",
            "--trajectory_length_min=1",
            "--trajectory_length_max=20",
            f"--output_json_path={output_file}"
        ]

    print(f"运行命令: {' '.join(cmd)}")

    # 性能监控变量
    performance_metrics = {
        'start_time': None,
        'end_time': None,
        'duration': 0,
        'max_memory_mb': 0,
        'avg_cpu_percent': 0,
        'cpu_samples': [],
        'memory_samples': [],
        'monitoring': True
    }

    def monitor_performance(process, metrics):
        """监控进程性能的线程函数"""
        try:
            while metrics['monitoring'] and process.poll() is None:
                try:
                    # 获取CPU使用率
                    cpu_percent = process.cpu_percent()
                    metrics['cpu_samples'].append(cpu_percent)

                    # 获取内存使用
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
                    metrics['memory_samples'].append(memory_mb)
                    metrics['max_memory_mb'] = max(metrics['max_memory_mb'], memory_mb)

                    time.sleep(0.5)  # 每0.5秒采样一次
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    break
        except Exception as e:
            print(f"性能监控异常: {e}")

    try:
        # 记录开始时间
        performance_metrics['start_time'] = time.time()
        start_datetime = datetime.now()
        print(f"⏰ 开始时间: {start_datetime.strftime('%H:%M:%S')}")

        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 获取psutil进程对象用于监控
        try:
            psutil_process = psutil.Process(process.pid)
        except psutil.NoSuchProcess:
            psutil_process = None

        # 启动性能监控线程
        monitor_thread = None
        if psutil_process:
            monitor_thread = threading.Thread(
                target=monitor_performance,
                args=(psutil_process, performance_metrics)
            )
            monitor_thread.daemon = True
            monitor_thread.start()

        # 等待进程完成
        try:
            stdout, stderr = process.communicate(timeout=300)  # 5分钟超时
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
            performance_metrics['monitoring'] = False
            if monitor_thread:
                monitor_thread.join(timeout=1)
            return False, "Timeout", performance_metrics

        # 停止监控
        performance_metrics['monitoring'] = False
        if monitor_thread:
            monitor_thread.join(timeout=1)

        # 记录结束时间
        performance_metrics['end_time'] = time.time()
        performance_metrics['duration'] = performance_metrics['end_time'] - performance_metrics['start_time']

        # 计算平均CPU使用率
        if performance_metrics['cpu_samples']:
            performance_metrics['avg_cpu_percent'] = sum(performance_metrics['cpu_samples']) / len(performance_metrics['cpu_samples'])

        end_datetime = datetime.now()
        print(f"⏰ 结束时间: {end_datetime.strftime('%H:%M:%S')}")
        print(f"⏱️  总耗时: {performance_metrics['duration']:.2f} 秒")
        print(f"🧠 最大内存: {performance_metrics['max_memory_mb']:.1f} MB")
        print(f"💻 平均CPU: {performance_metrics['avg_cpu_percent']:.1f}%")

        if process.returncode == 0:
            print(f"✅ {script_name} 执行成功")
            return True, stdout, performance_metrics
        else:
            print(f"❌ {script_name} 执行失败")
            print(f"错误: {stderr}")
            return False, stderr, performance_metrics

    except Exception as e:
        performance_metrics['monitoring'] = False
        print(f"❌ {script_name} 执行异常: {e}")
        return False, str(e), performance_metrics


def parse_output_file(filename):
    """解析输出文件"""
    if not os.path.exists(filename):
        return {}
    
    sessions = {}
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split('\t*#&\t')
            if len(parts) == 2:
                session_id, json_data = parts
                try:
                    data = json.loads(json_data)
                    sessions[session_id] = data
                except json.JSONDecodeError:
                    print(f"警告: 无法解析JSON数据: {json_data[:100]}...")
    
    return sessions


def compare_performance(performance_data):
    """比较性能数据"""
    print(f"\n{'='*80}")
    print("📈 性能对比分析")
    print(f"{'='*80}")

    if not performance_data:
        print("⚠️  没有性能数据可比较")
        return

    # 创建性能表格
    print(f"{'版本':<20} {'耗时(秒)':<12} {'内存(MB)':<12} {'CPU(%)':<10} {'效率评分':<10}")
    print("-" * 80)

    # 计算效率评分（基于耗时和内存使用的综合评分）
    scored_results = []

    for name, metrics in performance_data.items():
        duration = metrics.get('duration', 0)
        max_memory = metrics.get('max_memory_mb', 0)
        avg_cpu = metrics.get('avg_cpu_percent', 0)

        # 效率评分：时间越短、内存越少、CPU利用率适中得分越高
        # 基础分100，根据性能调整
        efficiency_score = 100
        if duration > 0:
            efficiency_score -= min(duration * 2, 50)  # 时间惩罚
        if max_memory > 100:
            efficiency_score -= min((max_memory - 100) * 0.1, 20)  # 内存惩罚
        if avg_cpu < 20:  # CPU利用率太低也不好
            efficiency_score -= (20 - avg_cpu) * 0.5

        efficiency_score = max(0, efficiency_score)
        scored_results.append((name, metrics, efficiency_score))

        print(f"{name:<20} {duration:<12.2f} {max_memory:<12.1f} {avg_cpu:<10.1f} {efficiency_score:<10.1f}")

    # 排序并显示排名
    scored_results.sort(key=lambda x: x[2], reverse=True)

    print(f"\n🏆 性能排名:")
    for i, (name, metrics, score) in enumerate(scored_results, 1):
        duration = metrics.get('duration', 0)
        print(f"  {i}. {name} (评分: {score:.1f}, 耗时: {duration:.2f}秒)")

    # 详细对比分析
    if len(scored_results) >= 2:
        best = scored_results[0]
        worst = scored_results[-1]

        best_time = best[1].get('duration', 0)
        worst_time = worst[1].get('duration', 0)

        if worst_time > 0 and best_time > 0:
            speedup = worst_time / best_time
            time_saved = worst_time - best_time

            print(f"\n📊 详细对比:")
            print(f"  最快版本: {best[0]} ({best_time:.2f}秒)")
            print(f"  最慢版本: {worst[0]} ({worst_time:.2f}秒)")
            print(f"  加速比: {speedup:.2f}x")
            print(f"  节省时间: {time_saved:.2f}秒")
            print(f"  性能提升: {(speedup-1)*100:.1f}%")

            # 内存对比
            best_memory = best[1].get('max_memory_mb', 0)
            worst_memory = worst[1].get('max_memory_mb', 0)
            if worst_memory > 0:
                memory_ratio = worst_memory / best_memory if best_memory > 0 else 1
                print(f"  内存效率: {best[0]} 比 {worst[0]} 节省 {(1-1/memory_ratio)*100:.1f}% 内存")

def compare_results(result1, result2, name1, name2):
    """比较两个结果的正确性"""
    print(f"\n{'='*60}")
    print(f"🔍 正确性验证: {name1} vs {name2}")
    print(f"{'='*60}")

    # 比较session数量
    print(f"{name1} sessions: {len(result1)}")
    print(f"{name2} sessions: {len(result2)}")

    if len(result1) != len(result2):
        print("❌ Session数量不一致")
        return False

    # 比较session内容
    mismatches = 0
    total_sessions = len(result1)

    for session_id in result1:
        if session_id not in result2:
            print(f"❌ Session {session_id} 在 {name2} 中不存在")
            mismatches += 1
            continue

        data1 = result1[session_id]
        data2 = result2[session_id]

        if len(data1) != len(data2):
            print(f"❌ Session {session_id} 长度不一致: {len(data1)} vs {len(data2)}")
            mismatches += 1
            continue

        # 比较数据内容（忽略顺序）
        # 这里简化比较，只比较长度和第一个元素
        if data1 and data2:
            if set(data1[0].keys()) != set(data2[0].keys()):
                print(f"❌ Session {session_id} 字段不一致")
                mismatches += 1

    accuracy = (total_sessions - mismatches) / total_sessions * 100 if total_sessions > 0 else 0

    if mismatches == 0:
        print("✅ 结果完全一致 (100% 准确率)")
        return True
    else:
        print(f"❌ 发现 {mismatches} 个不一致 ({accuracy:.1f}% 准确率)")
        return False


def plot_performance_chart(performance_data, save_path=None):
    """绘制性能对比图表（可选功能）"""
    try:
        import matplotlib.pyplot as plt

        if not performance_data:
            return

        names = list(performance_data.keys())
        durations = [metrics.get('duration', 0) for metrics in performance_data.values()]
        memories = [metrics.get('max_memory_mb', 0) for metrics in performance_data.values()]

        _, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

        # 耗时对比
        bars1 = ax1.bar(names, durations, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        ax1.set_ylabel('耗时 (秒)')
        ax1.set_title('处理耗时对比')
        ax1.tick_params(axis='x', rotation=45)

        # 在柱子上显示数值
        for bar, duration in zip(bars1, durations):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{duration:.2f}s', ha='center', va='bottom')

        # 内存使用对比
        bars2 = ax2.bar(names, memories, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        ax2.set_ylabel('内存使用 (MB)')
        ax2.set_title('内存使用对比')
        ax2.tick_params(axis='x', rotation=45)

        # 在柱子上显示数值
        for bar, memory in zip(bars2, memories):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{memory:.1f}MB', ha='center', va='bottom')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 性能图表已保存到: {save_path}")
        else:
            plt.show()

    except ImportError:
        print("📊 matplotlib未安装，跳过图表生成")
        print("   安装命令: pip install matplotlib")
    except Exception as e:
        print(f"📊 图表生成失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="测试优化版本的正确性和性能")
    parser.add_argument("--num_sessions", type=int, default=100, help="测试session数量")
    parser.add_argument("--avg_session_length", type=int, default=5, help="平均session长度")
    parser.add_argument("--keep_files", action="store_true", help="保留测试文件")
    parser.add_argument("--plot", action="store_true", help="生成性能对比图表")
    parser.add_argument("--large_test", action="store_true", help="大规模测试模式")
    
    args = parser.parse_args()

    # 大规模测试模式调整参数
    if args.large_test:
        args.num_sessions = 10000
        args.avg_session_length = 8
        print("🚀 大规模测试模式已启用")

    print("=" * 80)
    print("Group By Session 优化版本性能与正确性测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now()}")
    print(f"测试参数: {args.num_sessions} sessions, 平均长度 {args.avg_session_length}")
    print(f"预估数据量: {args.num_sessions * args.avg_session_length} 行")
    print(f"测试模式: {'大规模测试' if args.large_test else '标准测试'}")
    print()
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"临时目录: {temp_dir}")
        
        # 1. 创建测试数据
        df = create_test_data(args.num_sessions, args.avg_session_length)
        
        # 2. 保存测试数据
        pickle_file = "test_data.pickle"
        pickle_path = os.path.join(temp_dir, pickle_file)
        save_test_pickle(df, pickle_path)
        
        # 3. 测试不同版本
        test_configs = [
            {
                "name": "优化版本",
                "script": "group_by_session_id.py",
                "output": os.path.join(temp_dir, "output_optimized.json")
            },
            {
                "name": "超级优化版本", 
                "script": "group_by_session_id_ultra_optimized.py",
                "output": os.path.join(temp_dir, "output_ultra.json")
            }
        ]
        
        results = {}
        performance_data = {}

        for config in test_configs:
            if not os.path.exists(config["script"]):
                print(f"跳过 {config['name']}: 脚本不存在")
                continue

            print(f"\n{'='*50}")
            print(f"🧪 测试 {config['name']}")
            print(f"{'='*50}")

            success, output, metrics = run_test_script(
                config["script"],
                temp_dir,
                pickle_file,
                config["output"]
            )

            # 保存性能数据
            performance_data[config["name"]] = metrics

            if success:
                result_data = parse_output_file(config["output"])
                results[config["name"]] = result_data
                print(f"📊 解析结果: {len(result_data)} 个sessions")

                # 检查输出文件大小
                if os.path.exists(config["output"]):
                    file_size = os.path.getsize(config["output"])
                    print(f"📁 输出文件大小: {file_size / 1024:.1f} KB")
            else:
                print(f"❌ 测试失败: {output[:200]}...")
        
        # 4. 性能分析
        print(f"\n{'='*80}")
        print("📋 测试总结")
        print(f"{'='*80}")

        if performance_data:
            compare_performance(performance_data)

        # 5. 正确性验证
        if len(results) >= 2:
            result_names = list(results.keys())
            compare_results(
                results[result_names[0]],
                results[result_names[1]],
                result_names[0],
                result_names[1]
            )
        else:
            print("\n⚠️  无法进行正确性比较，测试版本不足")

        # 6. 生成测试报告
        generate_test_report(performance_data, results, args)

        # 7. 生成性能图表（如果需要）
        if args.plot and performance_data:
            chart_path = f"performance_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plot_performance_chart(performance_data, chart_path)

        # 8. 保留文件（如果需要）
        if args.keep_files:
            import shutil
            keep_dir = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copytree(temp_dir, keep_dir)
            print(f"\n📁 测试文件已保存到: {keep_dir}")
    
    print("\n✅ 正确性测试完成")


def generate_test_report(performance_data, results, args):
    """生成详细的测试报告"""
    print(f"\n{'='*80}")
    print("📄 测试报告")
    print(f"{'='*80}")

    # 基本信息
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试参数:")
    print(f"  - Sessions数量: {args.num_sessions}")
    print(f"  - 平均Session长度: {args.avg_session_length}")
    print(f"  - 预估总数据行数: {args.num_sessions * args.avg_session_length}")

    # 性能汇总
    if performance_data:
        print(f"\n📊 性能汇总:")
        fastest_time = float('inf')
        slowest_time = 0
        fastest_name = ""
        slowest_name = ""

        for name, metrics in performance_data.items():
            duration = metrics.get('duration', 0)
            if duration > 0:
                if duration < fastest_time:
                    fastest_time = duration
                    fastest_name = name
                if duration > slowest_time:
                    slowest_time = duration
                    slowest_name = name

        if fastest_time < float('inf') and slowest_time > 0:
            speedup = slowest_time / fastest_time
            print(f"  - 最快版本: {fastest_name} ({fastest_time:.2f}秒)")
            print(f"  - 最慢版本: {slowest_name} ({slowest_time:.2f}秒)")
            print(f"  - 性能提升: {speedup:.2f}x ({(speedup-1)*100:.1f}%)")

            # 推算大数据性能
            data_scale_factor = 10000000 / (args.num_sessions * args.avg_session_length)  # 1000万行的倍数
            if data_scale_factor > 1:
                estimated_time = fastest_time * data_scale_factor
                print(f"\n🔮 1000万行数据预估:")
                print(f"  - 最优版本预估耗时: {estimated_time:.0f}秒 ({estimated_time/60:.1f}分钟)")
                if estimated_time < 3600:
                    print(f"  - 预估结果: ✅ 优秀 (小于1小时)")
                elif estimated_time < 7200:
                    print(f"  - 预估结果: ⚠️  可接受 (1-2小时)")
                else:
                    print(f"  - 预估结果: ❌ 需要进一步优化 (超过2小时)")

    # 正确性汇总
    if results:
        print(f"\n✅ 正确性验证:")
        print(f"  - 测试版本数: {len(results)}")
        print(f"  - 输出Sessions数: {len(list(results.values())[0]) if results else 0}")
        print(f"  - 数据一致性: {'通过' if len(set(len(r) for r in results.values())) <= 1 else '存在差异'}")

    # 建议
    print(f"\n💡 优化建议:")
    if performance_data:
        best_version = min(performance_data.items(), key=lambda x: x[1].get('duration', float('inf')))
        print(f"  - 推荐使用: {best_version[0]}")

        max_memory = max(metrics.get('max_memory_mb', 0) for metrics in performance_data.values())
        if max_memory > 1000:
            print(f"  - 内存使用较高 ({max_memory:.0f}MB)，建议在内存充足的环境运行")
        elif max_memory > 500:
            print(f"  - 内存使用适中 ({max_memory:.0f}MB)，适合大多数环境")
        else:
            print(f"  - 内存使用较低 ({max_memory:.0f}MB)，适合资源受限环境")


if __name__ == "__main__":
    main()
