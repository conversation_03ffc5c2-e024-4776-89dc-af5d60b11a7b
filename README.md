# Report Processor

## 环境要求
- Python 3.9+

## 快速开始

### 1. 环境配置
```bash
# 创建并激活虚拟环境（推荐）
python -m venv venv
# macOS/Linux
source venv/bin/activate

# 安装依赖
# 方式一：使用 requirements.txt（推荐）
pip install -r requirements.txt

# 方式二：单独安装各个包
pip install pandas rawins py_mini_racer pycryptodome pandarallel \
    --index-url https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple \
    --extra-index-url https://mirrors.tencent.com/pypi/simple
```

### 2. 启动脚本
```bash
python scheduler_day_allapp.py
```

## 注意事项
- 请确保在腾讯内部网络环境中运行