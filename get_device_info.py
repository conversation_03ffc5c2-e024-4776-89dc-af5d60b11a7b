import requests
import json

url = 'http://mt.woa.com/epuiat/external/miniapp_ai'
headers = {
    'X-App-Id': '1',
    'Content-Type': 'application/json'
}
data = {
    "args": None,
    "kwargs": {
        "cloud_id": 638
    },
    "logic": "MiniAppAiLogic",
    "method": "get_cloud_device_infos"
}

response = requests.post(url, headers=headers, data=json.dumps(data))

print("Status Code:", response.status_code)

# 用户提供的device_id列表
input_ids = set([
    4722, 3762, 3807, 3509, 3809, 3834, 3956, 3771, 2737, 2841, 2267, 2260, 2749, 4983, 2250, 6987, 4726, 6982, 13459, 2022, 2198, 2218, 2269, 2745, 2802, 2843, 3805, 3820, 3822, 3825, 3907, 3995, 4696, 4730, 4731, 4732, 4945, 4967, 4969, 4977, 4981, 4986, 4987, 6355, 6359, 13461, 1352, 2077, 2732, 2750, 2862, 2863, 2864, 3464, 3479, 3516, 3823, 3839, 3869, 3873, 3918, 3921, 3922, 3982, 3983, 3984, 3985, 3987, 3988, 3989, 3994, 4250, 4251, 4252, 4253, 4254, 4255, 4256, 4257, 4694, 4701, 4702, 4703, 4704, 4705, 4706, 4707, 4708, 4711, 4712, 4779, 6363, 15868, 16611, 2865, 2859, 2860, 2861, 2870, 3925, 3919, 2189, 2252
])

try:
    resp_json = response.json()
    device_infos = resp_json.get("device_infos", [])
    found_ids = set()
    invalid_ids = set()
    all_ids = set()
    for info in device_infos:
        device_id = info.get("device_id")
        model = info.get("device", {}).get("model_alias")
        all_ids.add(device_id)
        if device_id in input_ids:
            found_ids.add(device_id)
            # 判断device是否合法（这里假设model_alias为None或空为不合法）
            if not model:
                invalid_ids.add(device_id)
    invalid_ids = input_ids - found_ids
    missing_ids = all_ids - found_ids
    print(f"缺失的device_id: {sorted(missing_ids)}")
    print(f"不合法的device_id: {sorted(invalid_ids)}")
    # 输出缺失的device_id及其model_alias
    print("缺失的device_id及model_alias:")
    for info in device_infos:
        device_id = info.get("device_id")
        model = info.get("device", {}).get("model_alias")
        if device_id in missing_ids:
            print(f"device_id: {device_id}, model_alias: {model}")
    # 输出不合法的device_id及其model_alias
    print("不合法的device_id及model_alias:")
    for info in device_infos:
        device_id = info.get("device_id")
        model = info.get("device", {}).get("model_alias")
        if device_id in invalid_ids:
            print(f"device_id: {device_id}, model_alias: {model}")
except Exception:
    print("Response Text:", response.text) 