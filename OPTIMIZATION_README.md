# 数据处理性能优化方案

## 概述

针对 `xml_decode_b_understand_all_app.py` 程序数据量过大导致1天跑不完的问题，实施了多进程并行优化方案。

## 性能瓶颈分析

### 原始程序的主要瓶颈：

1. **数据库查询瓶颈**：一次性查询大量数据，内存占用高
2. **CPU密集型操作**：
   - RSA解密操作（每行数据需要解密密钥）
   - AES解密操作（多个字段需要解密）
   - JavaScript解码操作（通过 py_mini_racer）
3. **串行处理**：虽然使用了 pandarallel，但整体流程仍是串行的
4. **内存管理**：大量数据同时加载到内存中

## 优化方案

### 1. 分批数据查询
- 实现 `get_data_in_batches()` 函数
- 将大查询分解为多个小批次
- 减少内存峰值使用量
- 支持进度监控

### 2. 多进程数据处理
- 实现 `process_dataframe_multiprocess()` 函数
- 使用 `ProcessPoolExecutor` 进行并行处理
- 将数据分块，每个进程处理一个数据块
- 自动根据CPU核心数调整进程数

### 3. 多进程JavaScript解码
- 实现 `decode_csv_with_js_multiprocess()` 函数
- JavaScript解码是CPU密集型操作，适合并行化
- 每个进程独立初始化JavaScript环境

### 4. 内存管理优化
- 添加内存使用监控
- 定期执行垃圾回收
- 分批处理减少内存峰值

### 5. 配置参数化
- 支持调整批次大小 (`chunk_size`)
- 支持调整最大进程数 (`max_workers`)
- 支持启用/禁用多进程模式

## 使用方法

### 基本用法（启用多进程）
```bash
python xml_decode_b_understand_all_app.py \
    --output_file_name=output.pickle \
    --start_time="2025-06-05 00:00:00" \
    --end_time="2025-06-05 07:59:59" \
    --session_cgi_label=cgi7 \
    --use_multiprocess=True
```

### 高级配置
```bash
python xml_decode_b_understand_all_app.py \
    --output_file_name=output.pickle \
    --start_time="2025-06-05 00:00:00" \
    --end_time="2025-06-05 07:59:59" \
    --session_cgi_label=cgi7 \
    --use_multiprocess=True \
    --chunk_size=2000 \
    --max_workers=8
```

### 使用优化版调度器
```bash
# 单次运行
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7 \
    --use_multiprocess \
    --max_workers=8 \
    --chunk_size=2000 \
    --run_once

# 定时任务
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7 \
    --use_multiprocess \
    --start_time=01:00 \
    --end_time=01:15
```

## 性能测试

### 运行性能测试
```bash
# 快速测试
python performance_test.py --quick

# 完整测试
python performance_test.py --test_size=medium

# 大规模测试
python performance_test.py --test_size=large
```

## 预期性能提升

根据系统配置和数据量，预期性能提升：

- **4核CPU系统**：2-3倍加速
- **8核CPU系统**：3-5倍加速
- **16核CPU系统**：4-8倍加速

实际效果取决于：
- CPU核心数
- 内存大小
- 数据量大小
- I/O性能

## 参数调优建议

### chunk_size（批次大小）
- **小数据量**：500-1000
- **中等数据量**：1000-2000
- **大数据量**：2000-5000

### max_workers（最大进程数）
- **建议值**：CPU核心数的50%-100%
- **内存充足**：可以设置为CPU核心数
- **内存紧张**：设置为CPU核心数的50%

### 内存考虑
- 每个进程大约需要额外的内存
- 监控内存使用情况，避免OOM
- 如果内存不足，减少 `max_workers` 或 `chunk_size`

## 兼容性

- 保留了原始的单进程处理函数作为备选
- 可以通过 `--use_multiprocess=False` 回退到原始模式
- 所有原有功能保持不变

## 监控和调试

程序会输出详细的性能信息：
- 各阶段耗时
- 内存使用情况
- 处理进度
- 平均处理速度

## 注意事项

1. **首次运行**：建议先用小数据量测试
2. **内存监控**：注意监控系统内存使用
3. **进程数限制**：不要设置过多进程，可能导致系统负载过高
4. **错误处理**：多进程环境下错误处理更复杂，注意查看日志

## 故障排除

### 常见问题

1. **内存不足**
   - 减少 `chunk_size`
   - 减少 `max_workers`
   - 使用分批查询

2. **进程启动失败**
   - 检查系统资源
   - 减少 `max_workers`
   - 检查权限设置

3. **性能提升不明显**
   - 数据量可能太小
   - CPU已经饱和
   - I/O成为瓶颈

### 调试模式
```bash
# 禁用多进程，使用原始模式
python xml_decode_b_understand_all_app.py \
    --use_multiprocess=False \
    --output_file_name=debug.pickle
```

## 总结

通过多进程并行优化，预期可以将原本需要1天的处理时间缩短到几个小时，具体效果取决于系统配置和数据特征。建议根据实际环境调整参数以获得最佳性能。
