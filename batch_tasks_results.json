[{"app_name": "﻿滴滴出行丨打车骑行火车租车代驾", "query": "帮我打车从 XX 到XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2025, "success": true}}, "success": true}, {"app_name": "收钱吧", "query": "帮我点一份XX（外卖），跟XX（时间）点的一样，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2026, "success": true}}, "success": true}, {"app_name": "美团丨外卖团购特价美食酒店电影", "query": "帮我点一份 XX，跟上次一样，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2027, "success": true}}, "success": true}, {"app_name": "快团团", "query": "上次团的 XX，再来一份", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2028, "success": true}}, "success": true}, {"app_name": "蜜雪冰城", "query": "帮我点一杯 XX，跟上次一样，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2029, "success": true}}, "success": true}, {"app_name": "京东购物丨点外卖领国补", "query": "帮我再买一次 xx，跟上次一样，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2030, "success": true}}, "success": true}, {"app_name": "luckincoffee瑞幸咖啡", "query": "帮我买一杯 XXX，到XX店取", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2031, "success": true}}, "success": true}, {"app_name": "生活缴费", "query": "帮我缴下这个月的电费", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2032, "success": true}}, "success": true}, {"app_name": "美团外卖丨外卖美食奶茶咖啡水果", "query": "帮我点一份 XX，跟上次一样，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2033, "success": true}}, "success": true}, {"app_name": "美团拼好饭", "query": "帮我拼一份 XX，跟上次一样，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2034, "success": true}}, "success": true}, {"app_name": "多多买菜", "query": "帮我刚买的 XX 操作下退款，理由是拍多了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2035, "success": true}}, "success": true}, {"app_name": "腾讯手机充值服务", "query": "给 XXXX（手机号）充 XX 话费", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2036, "success": true}}, "success": true}, {"app_name": "美团优选 明日达超市 真的真的省", "query": "帮我买 XX，去 XX 自提", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2037, "success": true}}, "success": true}, {"app_name": "腾讯出行服务", "query": "帮我打车从 XX 去 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2038, "success": true}}, "success": true}, {"app_name": "中国移动10086+", "query": "给 XXXX（手机号）充 XX 话费", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2039, "success": true}}, "success": true}, {"app_name": "泡泡玛特", "query": "帮我买个 XX，到 XX 店里取", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2040, "success": true}}, "success": true}, {"app_name": "饿了么 l 外卖美食超市买菜水果", "query": "帮我点一份 XX，跟上次一样，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2041, "success": true}}, "success": true}, {"app_name": "大众点评美食电影运动旅游门票", "query": "帮我团一个 XXX 店的 XX 券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2042, "success": true}}, "success": true}, {"app_name": "麦当劳", "query": "帮我再点一份上次点的 XX 套餐，外卖送到 XXX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2043, "success": true}}, "success": true}, {"app_name": "拼多多福利券", "query": "帮我再拼单个 XXX，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2044, "success": true}}, "success": true}, {"app_name": "拼多多", "query": "帮我再拼单个 XXX，送到 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2045, "success": true}}, "success": true}, {"app_name": "哈啰丨单车助力车打车顺风车租车", "query": "帮我打车从 XX 去 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2046, "success": true}}, "success": true}, {"app_name": "同程旅行订酒店机票火车拼车门票", "query": "帮我打车从 XX 去 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2047, "success": true}}, "success": true}, {"app_name": "花小猪打车 打车顺风车代驾", "query": "帮我打车从 XX 去 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2048, "success": true}}, "success": true}, {"app_name": "PP停车", "query": "帮我缴下停车费", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2049, "success": true}}, "success": true}, {"app_name": "猫眼电影演出玩乐I电影票演唱会", "query": "帮我在 XX 影院定两张 XX（电影）的票，时间今晚 7 点后都行", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2050, "success": true}}, "success": true}, {"app_name": "霸王茶姬", "query": "帮我买个 XX，送到 XXX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2051, "success": true}}, "success": true}, {"app_name": "微信支付有优惠", "query": "帮我兑换 100 元提现免费券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2052, "success": true}}, "success": true}, {"app_name": "花小猪打车 打车顺风车代驾", "query": "帮我打车，从 XX 去 XX", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2053, "success": true}}, "success": true}, {"app_name": "美团丨外卖团购特价美食酒店电影", "query": "帮我查下上周买的 XX 的价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2054, "success": true}}, "success": true}, {"app_name": "滴滴出行丨打车骑行火车租车代驾", "query": "帮我查下 XX（时间）打车到 XX 的订单价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2055, "success": true}}, "success": true}, {"app_name": "快团团", "query": "帮我找下之前买 XX 的订单", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2056, "success": true}}, "success": true}, {"app_name": "京东购物丨点外卖领国补", "query": "帮我查下买的 XX 快递送到哪里了？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2057, "success": true}}, "success": true}, {"app_name": "拼多多", "query": "帮我查下退货订单的进度", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2058, "success": true}}, "success": true}, {"app_name": "我的账单与服务", "query": "帮我查下这个月花了多少钱", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2059, "success": true}}, "success": true}, {"app_name": "京东快递", "query": "帮我查下从 XX 寄来的快递送到哪里了？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2060, "success": true}}, "success": true}, {"app_name": "美团外卖丨外卖美食奶茶咖啡水果", "query": "帮我查下买的 xx 还有多久到", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2061, "success": true}}, "success": true}, {"app_name": "生活缴费", "query": "我上个月电费多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2062, "success": true}}, "success": true}, {"app_name": "微信支付有优惠", "query": "帮我查下我现在能兑换什么优惠", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2063, "success": true}}, "success": true}, {"app_name": "美团拼好饭", "query": "查下我上次拼的 XXX（商品）订单", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2064, "success": true}}, "success": true}, {"app_name": "金山文档丨WPS云文档", "query": "帮我打开记录日常 todo 的文档", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2065, "success": true}}, "success": true}, {"app_name": "中通快递", "query": "帮我查下最近一个快递的详情", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2066, "success": true}}, "success": true}, {"app_name": "美团优选 明日达超市 真的真的省", "query": "帮我查下上次买的 XX 还有货吗", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2067, "success": true}}, "success": true}, {"app_name": "同程旅行订酒店机票火车拼车门票", "query": "帮我查下去 XX 的航班/高铁 班号", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2068, "success": true}}, "success": true}, {"app_name": "多多买菜", "query": "查下我上次买的 XXX（商品）订单", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2069, "success": true}}, "success": true}, {"app_name": "接龙管家", "query": "查下我发起的接龙有多少人参与了？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2070, "success": true}}, "success": true}, {"app_name": "丰巢丨寄快递丨存包丨洗护丨家政", "query": "查下我的待取快递取件码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2071, "success": true}}, "success": true}, {"app_name": "腾讯文档", "query": "帮我查下之前记的 XX 文档", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2072, "success": true}}, "success": true}, {"app_name": "微信记账本", "query": "帮我查下这个月收入多少钱", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2073, "success": true}}, "success": true}, {"app_name": "泡泡玛特抽盒机", "query": "帮我查下买的盲盒发货了吗", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2074, "success": true}}, "success": true}, {"app_name": "群报数丨接龙报名预约统计", "query": "查下我参与的接龙有多少人参与了？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2075, "success": true}}, "success": true}, {"app_name": "美团点餐", "query": "我之前在 XX（某个店）点了什么菜？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2076, "success": true}}, "success": true}, {"app_name": "哈啰丨单车助力车打车顺风车租车", "query": "查下上次骑单车的骑行时间", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2077, "success": true}}, "success": true}, {"app_name": "luckincoffee瑞幸咖啡", "query": "查下我上周末买了什么", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2078, "success": true}}, "success": true}, {"app_name": "圆通速递", "query": "查下上次收快递的事件", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2079, "success": true}}, "success": true}, {"app_name": "51麻将计分", "query": "看下进行中的比赛，我前 X 局分别得了多少分？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2080, "success": true}}, "success": true}, {"app_name": "拼团呀", "query": "有多少人加入了我的拼团？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2081, "success": true}}, "success": true}, {"app_name": "农夫山泉送水到府+", "query": "我买的水送到哪里了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2082, "success": true}}, "success": true}, {"app_name": "三角洲行动", "query": "查下我的 xxx比赛战绩", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2083, "success": true}}, "success": true}, {"app_name": "饿了么 l 外卖美食超市买菜水果", "query": "查下我上周日晚上点了什么外卖", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2084, "success": true}}, "success": true}, {"app_name": "爱回收", "query": "我之前回收的 xx 价格是多少", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2085, "success": true}}, "success": true}, {"app_name": "蜜雪冰城", "query": "查下我点的 xxx 送到哪里了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2086, "success": true}}, "success": true}, {"app_name": "EMS中国邮政速递物流", "query": "查下我买的 xx 的物流信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2087, "success": true}}, "success": true}, {"app_name": "腾讯出行服务", "query": "帮我查下待出行的行程", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2088, "success": true}}, "success": true}, {"app_name": "群接龙", "query": "查下上次 XX的群接龙，有多少人参与", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2089, "success": true}}, "success": true}, {"app_name": "唯品会特卖", "query": "查下我昨天退的订单，进度到哪一步了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2090, "success": true}}, "success": true}, {"app_name": "贝壳找房丨二手房新房租房装修", "query": "帮我查下当前租房合同的有效期", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2091, "success": true}}, "success": true}, {"app_name": "申通快递", "query": "帮我查下 xxx 的快递到哪了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2092, "success": true}}, "success": true}, {"app_name": "货拉拉丨拉货搬家跑腿货运物流", "query": "帮我查下我预约的车，车牌号是多少", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2093, "success": true}}, "success": true}, {"app_name": "网易第五人格", "query": "帮我查下XXX的战绩", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2094, "success": true}}, "success": true}, {"app_name": "朴朴超市", "query": "帮我查下上次买的 xx 的价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2095, "success": true}}, "success": true}, {"app_name": "滴滴青桔", "query": "查一下我上次骑车停在了哪里", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2096, "success": true}}, "success": true}, {"app_name": "德邦快递", "query": "帮我查下 xx 快递的物流信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2097, "success": true}}, "success": true}, {"app_name": "快递100", "query": "帮我查下 xx 快递的物流信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2098, "success": true}}, "success": true}, {"app_name": "百度网盘", "query": "帮我找下之前存的 XXX 资源", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2099, "success": true}}, "success": true}, {"app_name": "小桔充电", "query": "查下我XX 时间充电的费用", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2100, "success": true}}, "success": true}, {"app_name": "小象超市丨原美团买菜", "query": "查下 XX买的XX 的价格是多少", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2101, "success": true}}, "success": true}, {"app_name": "韵达快递", "query": "帮我查下 XX快递 的物流", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2102, "success": true}}, "success": true}, {"app_name": "易捷加油", "query": "查下我XX 加油的费用", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2103, "success": true}}, "success": true}, {"app_name": "BOSS直聘丨求职招聘找工作", "query": "我沟通过多少家公司？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2104, "success": true}}, "success": true}, {"app_name": "大众点评美食电影运动旅游门票", "query": "查下 XX 订单的有效期", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2105, "success": true}}, "success": true}, {"app_name": "腾讯微保", "query": "我现在买了哪些保险？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2106, "success": true}}, "success": true}, {"app_name": "塔斯汀+", "query": "我 XXX 时候买了哪些餐品", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2107, "success": true}}, "success": true}, {"app_name": "接龙管家 I 打卡问卷报名投票", "query": "我发的接龙XXX 填了吗", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2108, "success": true}}, "success": true}, {"app_name": "麻将计分", "query": "刚才比赛 XX 得了多少分", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2109, "success": true}}, "success": true}, {"app_name": "怀旧WCL", "query": "查下 XX 的比赛战绩", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2110, "success": true}}, "success": true}, {"app_name": "泡泡玛特", "query": "帮我查下我买的 XX 物流送到哪里了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2111, "success": true}}, "success": true}, {"app_name": "携程旅行订酒店机票火车汽车门票", "query": "帮我查下去 XX 订的酒店是哪一家", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2112, "success": true}}, "success": true}, {"app_name": "炉石传说", "query": "查看我的近期战绩", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2113, "success": true}}, "success": true}, {"app_name": "拼多多福利券", "query": "帮我找下之前买XX 的订单", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2114, "success": true}}, "success": true}, {"app_name": "麦当劳", "query": "XX（时间）点餐点了哪些东西？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2115, "success": true}}, "success": true}, {"app_name": "微快递", "query": "帮我查下 XX 快递", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2116, "success": true}}, "success": true}, {"app_name": "沃尔玛", "query": "上次买 XX 花了多少钱", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2117, "success": true}}, "success": true}, {"app_name": "美的Midea", "query": "查下我买的 XX 送到哪里了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2118, "success": true}}, "success": true}, {"app_name": "腾讯理财通", "query": "查下我今日账户的盈亏", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2119, "success": true}}, "success": true}, {"app_name": "微信发票助手", "query": "查下我 xx（时间段）的发票记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2120, "success": true}}, "success": true}, {"app_name": "和成天下会员中心", "query": "帮我查下买的 XX 的物流", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2121, "success": true}}, "success": true}, {"app_name": "途虎养车I轮胎保养蓄电池洗车", "query": "帮我查下 XX 的服务进度以及养护记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2122, "success": true}}, "success": true}, {"app_name": "打牌记账", "query": "我进行中的比赛得分是多少", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2123, "success": true}}, "success": true}, {"app_name": "美团圈圈", "query": "帮我查下 XX（时间）买的 XX 的信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2124, "success": true}}, "success": true}, {"app_name": "群打卡", "query": "查下今天 XXX 我打卡了吗", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2125, "success": true}}, "success": true}, {"app_name": "小鹅通学员版", "query": "帮我找下 XX 课程", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2126, "success": true}}, "success": true}, {"app_name": "怪兽充电EnergyMonster", "query": "帮我查下从几点开始借的当前充电宝", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2127, "success": true}}, "success": true}, {"app_name": "猫眼电影演出玩乐I电影票演唱会", "query": "帮我查下之前看 XXX 电影是在哪个影院看的？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2128, "success": true}}, "success": true}, {"app_name": "闪动FlashX", "query": "查下我还在进行中的活动有哪些", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2129, "success": true}}, "success": true}, {"app_name": "远方好物", "query": "查下买的 XX 送到哪里了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2130, "success": true}}, "success": true}, {"app_name": "肯德基+", "query": "查下我上次吃 XX 是什么时间", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2131, "success": true}}, "success": true}, {"app_name": "社康通", "query": "帮我查下我XX（时间/医院）的挂号/就医记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2132, "success": true}}, "success": true}, {"app_name": "一块走", "query": "帮我查下的捐步进展", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2133, "success": true}}, "success": true}, {"app_name": "驴充充+", "query": "帮我查下我 XXX 的充电记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2134, "success": true}}, "success": true}, {"app_name": "兔喜生活", "query": "帮我查下我的待取包裹有哪些", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2135, "success": true}}, "success": true}, {"app_name": "乘车码", "query": "帮我查下最近一周的乘车记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2136, "success": true}}, "success": true}, {"app_name": "商陆花电子小票", "query": "查下我 xx（时间段）的电子小票记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2137, "success": true}}, "success": true}, {"app_name": "华住会酒店预订汉庭全季桔子", "query": "帮我查下去 XX 住的什么酒店", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2138, "success": true}}, "success": true}, {"app_name": "霸王茶姬", "query": "帮我查下买的 XXX 配送到哪里了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2139, "success": true}}, "success": true}, {"app_name": "喜茶GO", "query": "帮我查下买的 XXX 配送到哪里了", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2140, "success": true}}, "success": true}, {"app_name": "所有女生会员服务中心", "query": "查下我买 XX 的购物记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2141, "success": true}}, "success": true}, {"app_name": "群投票", "query": "帮我总结下 XX 的投票结果", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2142, "success": true}}, "success": true}, {"app_name": "兴盛优选", "query": "帮我查下买的 XX 商品的信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2143, "success": true}}, "success": true}, {"app_name": "南网在线", "query": "帮我查下 XX 时间段的电费记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2144, "success": true}}, "success": true}, {"app_name": "菜鸟裹裹商家寄件", "query": "帮我查下 XXX 快递物流信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2145, "success": true}}, "success": true}, {"app_name": "闲鱼闲置交易二手买卖估价回收", "query": "帮我查下 XXX 交易的信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2146, "success": true}}, "success": true}, {"app_name": "中科大附一院安徽省立医院", "query": "帮我查下上次在 XX 科室挂的医生是谁", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2147, "success": true}}, "success": true}, {"app_name": "信用卡还款", "query": "查一下我 XX 信用卡本月待还金额", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2148, "success": true}}, "success": true}, {"app_name": "微信支付有优惠", "query": "我的微信支付金币能兑换什么", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2149, "success": true}}, "success": true}, {"app_name": "生活缴费", "query": "帮我查下我的优惠券有哪些", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2150, "success": true}}, "success": true}, {"app_name": "腾讯理财通", "query": "帮我查下我的账户股票持仓情况", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2151, "success": true}}, "success": true}, {"app_name": "惠省 I 吃喝玩乐小助手", "query": "帮我查下我惠省账户里的优惠券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2152, "success": true}}, "success": true}, {"app_name": "京东购物丨点外卖领国补", "query": "帮我查下我京东账户里的优惠券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2153, "success": true}}, "success": true}, {"app_name": "饿了么 l 外卖美食超市买菜水果", "query": "帮我查下我饿了么账户里的优惠券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2154, "success": true}}, "success": true}, {"app_name": "我的医保凭证", "query": "查下我的医保账户余额", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2155, "success": true}}, "success": true}, {"app_name": "工银微金融", "query": "帮我查下工行银行卡余额", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2156, "success": true}}, "success": true}, {"app_name": "luckincoffee瑞幸咖啡", "query": "帮我查下我账户里的优惠券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2157, "success": true}}, "success": true}, {"app_name": "中国建设银行", "query": "帮我查下建行银行卡余额", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2158, "success": true}}, "success": true}, {"app_name": "东鹏饮料+", "query": "帮我查下我的东鹏饮料会员福利", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2159, "success": true}}, "success": true}, {"app_name": "粤省事", "query": "帮我查下我的公积金余额", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2160, "success": true}}, "success": true}, {"app_name": "腾讯健康", "query": "帮我查下我的医保账户余额", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2161, "success": true}}, "success": true}, {"app_name": "龙湖天街", "query": "帮我查下账户里的停车券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2162, "success": true}}, "success": true}, {"app_name": "口味王会员中心", "query": "查一下我的口味王会员权益", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2163, "success": true}}, "success": true}, {"app_name": "巴奴毛肚火锅", "query": "帮我看下我在巴奴火锅的记分能换什么菜品", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2164, "success": true}}, "success": true}, {"app_name": "农夫山泉送水到府+", "query": "帮我查下我的账户优惠券", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2165, "success": true}}, "success": true}, {"app_name": "美团丨外卖团购特价美食酒店电影", "query": "查一下 XX 店的团购套餐有哪些？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2166, "success": true}}, "success": true}, {"app_name": "同程旅行订酒店机票火车拼车门票", "query": "查一下明天上午从北京飞往上海的航班，有没有低于500块钱的机票？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2167, "success": true}}, "success": true}, {"app_name": "车来了精准实时公交", "query": "XX路公交车还有几分钟到XX站？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2168, "success": true}}, "success": true}, {"app_name": "京东购物丨点外卖领国补", "query": "查询一下iPhone 15 Pro 256G现在在京东上的价格是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2169, "success": true}}, "success": true}, {"app_name": "贝壳找房丨二手房新房租房装修", "query": "帮我查一下XXX 位置附近两室一厅的房源，看看最新的租金报价。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2170, "success": true}}, "success": true}, {"app_name": "美团外卖丨外卖美食奶茶咖啡水果", "query": "查一下附近销量最高的小龙虾外卖，看看大家的评价怎么样？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2171, "success": true}}, "success": true}, {"app_name": "大众点评美食电影运动旅游门票", "query": "查一下 XXX附近人均消费100元左右、评价最好的西餐厅。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2172, "success": true}}, "success": true}, {"app_name": "快团团", "query": "看看现在有什么新鲜水果的团购，价格是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2173, "success": true}}, "success": true}, {"app_name": "美团优选 明日达超市 真的真的省", "query": "查询一下今天鸡蛋和牛奶的价格。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2174, "success": true}}, "success": true}, {"app_name": "携程旅行订酒店机票火车汽车门票", "query": "查询一下本周六上海外滩附近五星级酒店哪些能定", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2175, "success": true}}, "success": true}, {"app_name": "美团拼好饭", "query": "看看现在有什么10元以下的黄焖鸡可以拼单？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2176, "success": true}}, "success": true}, {"app_name": "多多买菜", "query": "查询一下今天糯米糍荔枝的价格。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2177, "success": true}}, "success": true}, {"app_name": "拼多多", "query": "帮我查一下XXX 百亿补贴后的价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2178, "success": true}}, "success": true}, {"app_name": "滴滴出行丨打车骑行火车租车代驾", "query": "现在从我这里打车到首都机场T3航站楼，预估费用是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2179, "success": true}}, "success": true}, {"app_name": "朴朴超市", "query": "查下 XX 的价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2180, "success": true}}, "success": true}, {"app_name": "小象超市丨原美团买菜", "query": "帮我查下今天的限时秒杀有什么商品", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2181, "success": true}}, "success": true}, {"app_name": "饿了么 l 外卖美食超市买菜水果", "query": "找一家附近评分最高的麻辣烫", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2182, "success": true}}, "success": true}, {"app_name": "美团圈圈", "query": "查一下附近有什么适合双人的火锅店优惠套餐？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2183, "success": true}}, "success": true}, {"app_name": "猫眼电影演出玩乐I电影票演唱会", "query": "查一下XX 电影预计上映时间", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2184, "success": true}}, "success": true}, {"app_name": "中国体育彩票", "query": "查询上一期超级大乐透的开奖号码。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2185, "success": true}}, "success": true}, {"app_name": "中免日上", "query": "查询一下雅诗兰黛小棕瓶精华100ml的免税价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2186, "success": true}}, "success": true}, {"app_name": "泡泡玛特抽盒机", "query": "帮我查下 XXX （商品）有货吗", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2187, "success": true}}, "success": true}, {"app_name": "腾讯地图丨查路线公交步行搜周边", "query": "帮我查下附近最近的裁缝店", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2188, "success": true}}, "success": true}, {"app_name": "腾讯出行服务", "query": "查询从人民广场到上海火车站的实时公交地铁换乘方案。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2189, "success": true}}, "success": true}, {"app_name": "唯品会特卖", "query": "查询一下Nike air jordan 今天有什么特卖活动？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2190, "success": true}}, "success": true}, {"app_name": "安居客买房新房二手房租房房价", "query": "查询一下XX小区最近的二手房成交价格是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2191, "success": true}}, "success": true}, {"app_name": "转转丨买卖回收二手手机电脑数码", "query": "查询一下九成新的iPhone 15 Pro 256G现在的价格是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2192, "success": true}}, "success": true}, {"app_name": "懂车帝", "query": "查询一下特斯拉Model Y目前的真实车主成交价范围。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2193, "success": true}}, "success": true}, {"app_name": "去哪儿旅行订酒店机票火车票门票", "query": "查询XX（时间）从深圳北到长沙南的高铁二等座还有没有票？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2194, "success": true}}, "success": true}, {"app_name": "叮咚买菜 美食水果蔬菜海鲜早餐", "query": "查询一下今天的活虾是什么价格？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2195, "success": true}}, "success": true}, {"app_name": "掌上公交", "query": "查询 XX路公交车的首末班车时间。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2196, "success": true}}, "success": true}, {"app_name": "惠省 I 吃喝玩乐小助手", "query": "帮我找一下附近洗车服务的优惠券。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2197, "success": true}}, "success": true}, {"app_name": "寿司郎", "query": "查询一下寿司郎 XX 店 现在的排队情况。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2198, "success": true}}, "success": true}, {"app_name": "看个比赛", "query": "查询一下 XXX 比赛的票价", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2199, "success": true}}, "success": true}, {"app_name": "贝壳惠居", "query": "查询一下XX附近合租主卧独卫的租房价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2200, "success": true}}, "success": true}, {"app_name": "所有女生会员服务中心", "query": "查看一下李佳琦直播间今晚会上哪些美妆产品？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2201, "success": true}}, "success": true}, {"app_name": "微商相册", "query": "我关注的微商 XXX，今天有没有上新？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2202, "success": true}}, "success": true}, {"app_name": "华住会酒店预订汉庭全季桔子", "query": "查询一下北京西站附近的全季酒店今晚还有大床房吗，价格是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2203, "success": true}}, "success": true}, {"app_name": "墨迹天气", "query": "查询一下明天北京的空气质量和紫外线强度预报。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2204, "success": true}}, "success": true}, {"app_name": "铁路12306", "query": "查询一下本周五从上海到南京的高铁票，还有哪些车次有二等座？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2205, "success": true}}, "success": true}, {"app_name": "意钓鱼票", "query": "查询x月 x日崇文湖国际垂钓中心三万斤鱼票的售票信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2206, "success": true}}, "success": true}, {"app_name": "汽车之家", "query": "查一下奥迪 Q5L的提车价评价", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2207, "success": true}}, "success": true}, {"app_name": "飞常准查航班", "query": "查下今天国航CA1501航班的行李转盘", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2208, "success": true}}, "success": true}, {"app_name": "下厨房+", "query": "搜索一下“番茄炒蛋”评分最高的菜谱。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2209, "success": true}}, "success": true}, {"app_name": "广州交通行讯通", "query": "查询 去 XX 可以坐哪些公交", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2210, "success": true}}, "success": true}, {"app_name": "我爱我家买房二手房租房新房房价", "query": "查下 XX 小区的测评报告", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2211, "success": true}}, "success": true}, {"app_name": "优剪", "query": "查一下 XXX（位置）的优剪门店现在理发需要排队吗？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2212, "success": true}}, "success": true}, {"app_name": "智行火车票特价机票酒店汽车门票", "query": "帮我查下明天 G104 车次有没有可行的中转票", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2213, "success": true}}, "success": true}, {"app_name": "惜食魔法袋", "query": "看看现在附近有没有临期优惠的面包可以买？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2214, "success": true}}, "success": true}, {"app_name": "山姆会员商店SamsClub", "query": "查询一下山姆会员商店买 HR 黑绷带面霜的价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2215, "success": true}}, "success": true}, {"app_name": "美味不用等 大牌优惠", "query": "查询一下 XX 店现在的排队情况，大概要等多久？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2216, "success": true}}, "success": true}, {"app_name": "小桔充电", "query": "查一下 XXX 充电站现在有几个快充空闲？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2217, "success": true}}, "success": true}, {"app_name": "DT生活", "query": "查看一下 XX 店现在有什么优惠套餐", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2218, "success": true}}, "success": true}, {"app_name": "光宇科斯特换电租赁", "query": "查一下我附近最近有电的换电地点在哪？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2219, "success": true}}, "success": true}, {"app_name": "海底捞", "query": "查询一下海底捞的最新菜单和菜品价格。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2220, "success": true}}, "success": true}, {"app_name": "大润发优鲜", "query": "查一下大润发今天猪肉的价格是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2221, "success": true}}, "success": true}, {"app_name": "潮汐表精灵", "query": "查询一下深圳大鹏湾今天什么时间最适合赶海？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2222, "success": true}}, "success": true}, {"app_name": "微博", "query": "看看现在微博热搜榜第一名是什么话题？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2223, "success": true}}, "success": true}, {"app_name": "盒马NB 天天低价件件爆款", "query": "查一下盒马今天的日日鲜蔬菜价格。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2224, "success": true}}, "success": true}, {"app_name": "海底捞特色服务排号", "query": "查一下海底捞王府井店现在大桌排队到多少号了？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2225, "success": true}}, "success": true}, {"app_name": "大麦l演唱会话剧音乐比赛门票", "query": "查询一下张学友演唱会北京站还有没有余票？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2226, "success": true}}, "success": true}, {"app_name": "58同城丨招聘兼职家政租房二手车", "query": "查一下附近有没有正在出租的一室一厅，月租大概多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2227, "success": true}}, "success": true}, {"app_name": "金钩集", "query": "查下 XX（地点）XX（日期）收鱼价格", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2228, "success": true}}, "success": true}, {"app_name": "兴盛优选", "query": "查看一下兴盛优选今天的秒杀水果有哪些？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2229, "success": true}}, "success": true}, {"app_name": "乘车码", "query": "帮我打开乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2230, "success": true}}, "success": true}, {"app_name": "深圳通+", "query": "帮我打开乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2231, "success": true}}, "success": true}, {"app_name": "我的医保凭证", "query": "帮我打开医保码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2232, "success": true}}, "success": true}, {"app_name": "城市通", "query": "帮我打开乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2233, "success": true}}, "success": true}, {"app_name": "腾讯健康", "query": "帮我打开医保码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2234, "success": true}}, "success": true}, {"app_name": "医保支付", "query": "帮我打开医保码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2235, "success": true}}, "success": true}, {"app_name": "医保移动支付", "query": "帮我打开医保码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2236, "success": true}}, "success": true}, {"app_name": "腾讯出行服务", "query": "帮我打开乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2237, "success": true}}, "success": true}, {"app_name": "老婆大人", "query": "出示老婆大人会员码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2238, "success": true}}, "success": true}, {"app_name": "三江云菜", "query": "出示三江云菜会员码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2239, "success": true}}, "success": true}, {"app_name": "粤省事", "query": "出示医保电子凭证", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2240, "success": true}}, "success": true}, {"app_name": "羊城通乘车码", "query": "帮我打开乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2241, "success": true}}, "success": true}, {"app_name": "交运通卡", "query": "帮我打开重庆公交乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2242, "success": true}}, "success": true}, {"app_name": "滴滴出行丨打车骑行火车租车代驾", "query": "帮我打开乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2243, "success": true}}, "success": true}, {"app_name": "好想来品牌官方", "query": "出示好想来会员码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2244, "success": true}}, "success": true}, {"app_name": "成都地铁乘车码", "query": "帮我打开成都地铁乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2245, "success": true}}, "success": true}, {"app_name": "YH永辉生活丨买菜水果鸡蛋牛奶", "query": "出示永辉超市会员码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2246, "success": true}}, "success": true}, {"app_name": "美宜佳便利店", "query": "出示美宜佳超市的会员码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2247, "success": true}}, "success": true}, {"app_name": "自助易行", "query": "帮我打开小区的通行码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2248, "success": true}}, "success": true}, {"app_name": "微警认证", "query": "帮我打开粤居码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2249, "success": true}}, "success": true}, {"app_name": "广州地铁乘车码", "query": "帮我打开乘车码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2250, "success": true}}, "success": true}, {"app_name": "粤居码丨粤治安", "query": "帮我打开粤居码", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2251, "success": true}}, "success": true}, {"app_name": "京通", "query": "北京市申请办理居住证需要哪些材料和流程？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2252, "success": true}}, "success": true}, {"app_name": "粤省事", "query": "如何办理港澳通行证个人旅游签注？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2253, "success": true}}, "success": true}, {"app_name": "粤居码丨粤治安", "query": "广东如何办理临时身份证？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2254, "success": true}}, "success": true}, {"app_name": "随申办", "query": "查询上海市最新的 XXX政策。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2255, "success": true}}, "success": true}, {"app_name": "i龙华", "query": "查询深圳市龙华区XXX政策。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2256, "success": true}}, "success": true}, {"app_name": "掌上高考App", "query": "查询一下北京大学去年在广东理科录取分数是多少？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2257, "success": true}}, "success": true}, {"app_name": "团中央智慧团建云平台", "query": "如何办理团组织关系转接手续", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2258, "success": true}}, "success": true}, {"app_name": "西安交警预约业务", "query": "如何在西安办理机动车驾驶证期满换证", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2259, "success": true}}, "success": true}, {"app_name": "移民局12367", "query": "护照过期了如何换一个新的？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2260, "success": true}}, "success": true}, {"app_name": "国家政务服务平台", "query": "广东省如何注销律师", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2261, "success": true}}, "success": true}, {"app_name": "掌上公交", "query": "查看 XX （地点）附近的公交线路", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2262, "success": true}}, "success": true}, {"app_name": "企查查", "query": "查询 XX 公司的工商注册信息。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2263, "success": true}}, "success": true}, {"app_name": "我的社保卡", "query": "广州如何申请失业金", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2264, "success": true}}, "success": true}, {"app_name": "国务院客户端", "query": "老板欠我薪水怎么反馈？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2265, "success": true}}, "success": true}, {"app_name": "电子社保卡", "query": "如何认证社会保险待遇的领取资格？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2266, "success": true}}, "success": true}, {"app_name": "学信网小程序", "query": "如何在线申请学历证书电子注册备案表？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2267, "success": true}}, "success": true}, {"app_name": "广州电动自行车上牌", "query": "查询广州市电动自行车登记上牌的流程。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2268, "success": true}}, "success": true}, {"app_name": "一证通查", "query": "查询本机号码关联的互联网账号服务记录", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2269, "success": true}}, "success": true}, {"app_name": "广州交通行讯通", "query": "查询 XXX附近的公交线路", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2270, "success": true}}, "success": true}, {"app_name": "城市通", "query": "查询 XXX附近的公交线路", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2271, "success": true}}, "success": true}, {"app_name": "苏智出行", "query": "查询从嘉兴站到苏州博物馆的换乘优惠政策。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2272, "success": true}}, "success": true}, {"app_name": "中山教体服务", "query": "如何办理中山市入学积分申请？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2273, "success": true}}, "success": true}, {"app_name": "启信宝商业搜索", "query": "查下郑州航空港兴港电力有限公司的工商信息", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2274, "success": true}}, "success": true}, {"app_name": "贝壳找房丨二手房新房租房装修", "query": "查询当前北京市的购房限购政策。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2275, "success": true}}, "success": true}, {"app_name": "广州地铁乘车码", "query": "查看广州地铁从 XX 到 XX 的换乘地铁线路图", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2276, "success": true}}, "success": true}, {"app_name": "学信网报告在线验证", "query": "如何在线验证《教育部学籍在线验证报告》的真伪？", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2277, "success": true}}, "success": true}, {"app_name": "甬e行", "query": "宁波 XX（位置）附近有哪些公交线路", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2278, "success": true}}, "success": true}, {"app_name": "天眼查企业工商查询", "query": "查询滴滴出行科技有限公司长沙分公司的电话", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2279, "success": true}}, "success": true}, {"app_name": "四川e就业", "query": "四川如何办理失业登记", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2280, "success": true}}, "success": true}, {"app_name": "赛伦100蛇伤防治", "query": "被蛇咬了应该去哪个医院", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2281, "success": true}}, "success": true}, {"app_name": "广州微警务", "query": "查询广州市户口迁移的办事指南。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2282, "success": true}}, "success": true}, {"app_name": "交运通卡", "query": "查询重庆市公共交通票价的优惠政策。", "status_code": 200, "response": {"code": 0, "msg": "success", "data": {"task_id": 2283, "success": true}}, "success": true}]