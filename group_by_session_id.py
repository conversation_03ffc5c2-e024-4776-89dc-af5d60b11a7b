import pandas as pd
import json
from datetime import date, datetime
from collections import Counter
import os
import argparse
import gc
import time
import unicodedata
import numpy as np
from concurrent.futures import ProcessPoolExecutor
import multiprocessing as mp

from utils import send_msg_to_wecom_group

def clean_unicode_string(text):
    """清理字符串中的问题Unicode字符，保持可读性"""
    if not isinstance(text, str):
        return text

    # 方法1: 移除或替换代理字符
    # 检测并处理代理字符对
    cleaned = ""
    i = 0
    while i < len(text):
        char = text[i]
        # 检查是否是代理字符
        if 0xD800 <= ord(char) <= 0xDFFF:
            # 这是一个代理字符，尝试处理
            if i + 1 < len(text):
                next_char = text[i + 1]
                if 0xDC00 <= ord(next_char) <= 0xDFFF:
                    # 这是一个完整的代理对，尝试转换
                    try:
                        # 尝试编码测试
                        surrogate_pair = char + next_char
                        surrogate_pair.encode('utf-8')
                        cleaned += surrogate_pair
                        i += 2
                        continue
                    except UnicodeEncodeError:
                        # 代理对有问题，替换为占位符
                        cleaned += "�"  # Unicode替换字符
                        i += 2
                        continue
            # 单独的代理字符，替换为占位符
            cleaned += "�"
            i += 1
        else:
            # 正常字符，直接添加
            try:
                char.encode('utf-8')
                cleaned += char
            except UnicodeEncodeError:
                cleaned += "�"
            i += 1

    return cleaned

def convert_to_json_serializable(item):
    if isinstance(item, (date, pd.Timestamp)):
        return str(item)
    elif isinstance(item, dict):
        return {key: convert_to_json_serializable(value) for key, value in item.items()}
    elif isinstance(item, list):
        return [convert_to_json_serializable(sub_item) for sub_item in item]
    elif isinstance(item, str):
        # 清理Unicode问题字符，保持可读性
        return clean_unicode_string(item)
    return item

def process_session_chunk(session_ids, df_chunk, args, temp_output_path):
    """处理一个 session chunk 的数据"""
    print(f"开始处理包含 {len(session_ids)} 个 sessions 的数据块...")
    
    length_dist = Counter()
    processed_sessions = 0
    written_sessions = 0

    with open(temp_output_path, 'w', encoding='utf-8', errors='replace') as fout:
        current_session = None
        current_group = []

        # 使用 itertuples 提高性能
        columns = df_chunk.columns.tolist()
        
        for row_tuple in df_chunk.itertuples(index=False, name=None):
            row_dict = {col: row_tuple[i] for i, col in enumerate(columns)}
            session_id = row_dict['short_sessionid_']

            if current_session != session_id:
                # 处理上一个 session 组
                if current_group:
                    length = len(current_group)
                    length_dist[length] += 1

                    if args.trajectory_length_min <= length <= args.trajectory_length_max:
                        group_records = [convert_to_json_serializable(record) for record in current_group]
                        serialized = json.dumps(group_records, ensure_ascii=False, separators=(',', ':'))
                        fout.write(f"{current_session}\t*#&\t{serialized}\n")
                        written_sessions += 1

                    processed_sessions += 1
                    if processed_sessions % 1000 == 0:  # 减少打印频率
                        print(f"进程处理进度: {processed_sessions} sessions, 写入 {written_sessions} 个有效sessions")

                current_session = session_id
                current_group = [convert_to_json_serializable(row_dict)]
            else:
                current_group.append(convert_to_json_serializable(row_dict))

        # 处理最后一个 session 组
        if current_group:
            length = len(current_group)
            length_dist[length] += 1

            if args.trajectory_length_min <= length <= args.trajectory_length_max:
                serialized = json.dumps(current_group, ensure_ascii=False, separators=(',', ':'))
                fout.write(f"{current_session}\t*#&\t{serialized}\n")
                written_sessions += 1

            processed_sessions += 1

    return {
        'length_dist': length_dist,
        'processed_sessions': processed_sessions,
        'written_sessions': written_sessions,
        'temp_output_path': temp_output_path
    }

def get_user_operation_data_parallel(df, output_json_path, args):
    """并行处理版本的数据处理函数"""
    print(f"开始并行处理数据，总行数: {len(df)}")
    start_time = time.time()

    # 1. 排序数据
    print("步骤1: 排序数据...")
    df_sorted = df.sort_values(['short_sessionid_', 'timestamp_']).reset_index(drop=True)

    # 2. 获取唯一的 session_ids 并分块
    print("步骤2: 划分数据块...")
    unique_sessions = df_sorted['short_sessionid_'].unique()
    
    # 确保 num_workers 是有效的整数
    cpu_count = min(mp.cpu_count(), 8)  # 限制最大进程数
    num_workers = max(1, cpu_count)  # 默认至少用1个进程
    
    # 确保分块数不超过唯一session数
    num_chunks = min(num_workers, len(unique_sessions))
    session_chunks = np.array_split(unique_sessions, num_chunks)

    # 3. 并行处理每个数据块
    print(f"步骤3: 开始并行处理（{num_chunks} 个进程）...")
    
    results = []
    temp_files = []
    
    with ProcessPoolExecutor(max_workers=num_workers, mp_context=mp.get_context('spawn')) as executor:
        futures = []
        
        for i, chunk_sessions in enumerate(session_chunks):
            # 获取该 chunk 的数据
            chunk_df = df_sorted[df_sorted['short_sessionid_'].isin(chunk_sessions)].copy()
            temp_output = f"/tmp/temp_{output_json_path.split('.')[0]}_part_{i}.json"
            temp_files.append(temp_output)
            
            # 提交任务到进程池
            future = executor.submit(
                process_session_chunk,
                chunk_sessions,
                chunk_df,
                args,
                temp_output
            )
            futures.append(future)
        
        # 收集所有进程的结果
        for future in futures:
            results.append(future.result())

    # 4. 合并结果和统计信息
    print("步骤4: 合并结果...")
    total_length_dist = Counter()
    total_processed = 0
    total_written = 0
    
    # 合并统计信息
    for result in results:
        total_length_dist.update(result['length_dist'])
        total_processed += result['processed_sessions']
        total_written += result['written_sessions']

    # 合并所有临时文件
    with open(output_json_path, 'w', encoding='utf-8') as fout:
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                with open(temp_file, 'r', encoding='utf-8') as fin:
                    for line in fin:
                        fout.write(line)
                os.remove(temp_file)  # 删除临时文件

    end_time = time.time()
    print(f"\n处理完成，总耗时: {end_time - start_time:.2f}秒")
    print(f"总共处理了 {total_processed} 个sessions")
    print(f"写入了 {total_written} 个有效sessions")

    # 输出长度分布（只显示前20个最常见的长度）
    print("\nSession长度分布（前20个）:")
    sorted_lengths = sorted(total_length_dist.items(), key=lambda x: x[1], reverse=True)[:20]
    for length, count in sorted_lengths:
        print(f"  长度 {length}: {count} 个sessions")

    if total_processed > 0:
        valid_ratio = total_written / total_processed * 100
        print(f"\n有效session比例: {valid_ratio:.1f}% ({total_written}/{total_processed})")

def get_user_operation_data(df, output_json_path, args):
    """统一使用流式处理的数据处理函数"""
    data_size = len(df)
    print(f"数据量: {data_size} 行，使用流式处理模式")
    return get_user_operation_data_parallel(df, output_json_path, args)

def get_session_id(s):
    #hash=802408136&ts=1743774812155&host=&version=671103404&device=2#109385745#1743774845601
    return s.strip().split("#")[0]


def get_timestamp(s):
    return int(s.strip().split("#")[-2])


def process_pickle_file_optimized(file_path, output_json_path, args):
    """优化的单文件处理函数"""
    print(f"Processing ... {file_path}")

    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size / (1024*1024*1024):.2f} GB")

    try:
        df = pd.read_pickle(file_path)
        print("数据基本信息：")
        df.info(memory_usage='deep')

        if len(df) > 0:
            print("提取session信息...")
            # 使用 pd.Series 明确转换类型
            df['short_sessionid_'] = pd.Series(df['sessionid_']).apply(get_session_id)
            df['timestamp_'] = pd.Series(df['sessionid_']).apply(get_timestamp)

            get_user_operation_data_parallel(df, output_json_path, args)
        else:
            print("警告: 数据文件为空")

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        raise

def group_by_session_id(args):
    pickle_file_path = args.input_pickle_dir
    output_json_path = args.output_json_path

    target_pickles_str = args.target_pickles
    target_pickles = []
    if target_pickles_str == "all":
        target_pickles = [f for f in os.listdir(pickle_file_path) if f.endswith("pickle")]
    else:
        target_pickles = target_pickles_str.strip().split(",")

    print("target_pickles is ", target_pickles)

    # 清空输出文件
    if os.path.exists(output_json_path):
        os.remove(output_json_path)

    # pickle数据group by 之后保存成json格式
    file_list = [os.path.join(pickle_file_path, f) for f in target_pickles]

    total_start_time = time.time()
    for i, file_path in enumerate(file_list):
        print(f"\n=== 处理文件 {i+1}/{len(file_list)} ===")
        file_start_time = time.time()

        process_pickle_file_optimized(file_path, output_json_path, args)

        file_end_time = time.time()
        print(f"文件处理完成，耗时: {file_end_time - file_start_time:.2f}秒")

        # 强制垃圾回收
        gc.collect()

    total_end_time = time.time()
    print(f"\n=== 所有文件处理完成 ===")
    print(f"总耗时: {total_end_time - total_start_time:.2f}秒")
    send_msg_to_wecom_group(f"<font color=\"info\">[{datetime.now().strftime('%Y-%m-%d')}] Group By Session 完成</font>\n> 耗时: <font color=\"comment\">{total_end_time - total_start_time:.2f}秒</font>\n> 输出文件: <font color=\"comment\">{output_json_path}</font>\n> 输出文件大小: <font color=\"comment\">{os.path.getsize(output_json_path) / (1024**3):.2f} GB</font>", msg_type="markdown")


def split_txt_file(args):
    num_parts = args.split_num
    file_path = args.output_json_path
    try:
        # 读取源文件的所有行
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 计算总行数
        total_lines = len(lines)

        # 计算每个小文件应包含的行数
        lines_per_part = total_lines // num_parts

        # 分割文件
        for i in range(num_parts):
            start_index = i * lines_per_part
            end_index = start_index + lines_per_part if i < num_parts - 1 else total_lines

            # 确定小文件的文件名
            output_file_path = os.path.join(args.split_target_dir, f'part_{i + 1}.txt')

            # 写入小文件
            with open(output_file_path, 'w', encoding='utf-8') as output_file:
                output_file.writelines(lines[start_index:end_index])

        print(f'文件已成功分割成 {num_parts} 个小文件。')
    except FileNotFoundError:
        print(f"错误：未找到文件 {file_path}。")
    except Exception as e:
        print(f"发生未知错误：{e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="MiniP data Group by session")
    parser.add_argument("--input_pickle_dir", type=str, help="input_pickle_dir", default="")
    parser.add_argument("--target_pickles", type=str, help="all|file1,file2,file3",
                        default="all")

    parser.add_argument("--trajectory_length_min", type=int, help="trajectory_length_min",
                        default=1)
    parser.add_argument("--trajectory_length_max", type=int, help="trajectory_length_max",
                        default=20)
    parser.add_argument("--output_json_path", type=str, help="output_json_path",
                        default="")

    parser.add_argument("--split_num", type=int, help="split_num",
                        default=10)
    parser.add_argument("--split_target_dir", type=str, help="split_target_dir",
                        default="")
    parser.add_argument("--function", type=str, help="function",
                        default="group_by_session_id")


    print("Start....")
    args = parser.parse_args()
    print("args is : ")
    print(args)

    if args.function == "group_by_session_id":
        if os.path.exists(args.output_json_path):
            print(f"{args.output_json_path} has been exists, please make sure")
            exit(0)
        group_by_session_id(args)
    elif args.function == "split_txt_file":
        if os.path.exists(args.split_target_dir):
            print(f"{args.split_target_dir} has been exists, please make sure")
            exit(0)
        os.makedirs(args.split_target_dir)
        split_txt_file(args)
    else:
        print("not support function")
        exit(0)