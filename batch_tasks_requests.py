#!/usr/bin/env python3
"""
批量录入 tasks.csv 到注释服务，app_id 通过 app_ids.json 获取
"""

import requests
import json
import time
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_tasks_requests.log'),
        logging.StreamHandler()
    ]
)

class TaskBatchProcessor:
    def __init__(self, dry_run: bool = False):
        self.base_url = "http://mmfinderdrannotationsvr.polaris:8080/annotations/tasks"
        self.headers = {
            'Content-Type': 'application/json'
        }
        self.dry_run = dry_run
        self.app_ids = self.load_app_ids()

    def load_app_ids(self) -> Dict[str, str]:
        with open('app_ids.json', 'r', encoding='utf-8') as f:
            return json.load(f)

    def parse_tasks_csv(self, filename: str = 'tasks.csv') -> List[Dict[str, str]]:
        tasks = []
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or ',' not in line:
                    continue
                app_name, query = line.split(',', 1)
                app_name = app_name.strip()
                query = query.strip()
                tasks.append({'app_name': app_name, 'query': query})
        return tasks

    def create_request_payload(self, app_name: str, query: str) -> Dict[str, Any]:
        app_id = self.app_ids.get(app_name.strip().lstrip('\ufeff'))
        return {
            "app_id": app_id or "NOT_FOUND",
            "app_name": app_name,
            "query": query,
            "tag": "横向评估集"
        }

    def send_single_request(self, app_name: str, query: str) -> Dict[str, Any]:
        payload = self.create_request_payload(app_name, query)
        if not payload["app_id"] or payload["app_id"] == "NOT_FOUND":
            logging.warning(f"未找到 app_id: {app_name}")
            return {
                'app_name': app_name,
                'query': query,
                'status_code': None,
                'error': 'app_id not found',
                'success': False
            }
        if self.dry_run:
            logging.info(f"[DRY RUN] 模拟发送: {app_name} | {query}")
            return {
                'app_name': app_name,
                'query': query,
                'status_code': 200,
                'response': {'dry_run': True, 'payload': payload},
                'success': True
            }
        try:
            logging.info(f"发送请求: {app_name} | {query}")
            response = requests.post(
                self.base_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            response.raise_for_status()
            result = {
                'app_name': app_name,
                'query': query,
                'status_code': response.status_code,
                'response': response.json() if response.content else {},
                'success': True
            }
            logging.info(f"请求成功: {app_name} | 状态码: {response.status_code}")
            return result
        except requests.exceptions.RequestException as e:
            error_result = {
                'app_name': app_name,
                'query': query,
                'status_code': getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                'error': str(e),
                'success': False
            }
            logging.error(f"请求失败: {app_name} | 错误: {str(e)}")
            return error_result

    def batch_process(self, tasks: List[Dict[str, str]], delay: float = 1.0) -> List[Dict[str, Any]]:
        results = []
        total = len(tasks)
        logging.info(f"开始批量处理 {total} 个任务")
        for i, task in enumerate(tasks, 1):
            app_name = task['app_name']
            query = task['query']
            logging.info(f"处理进度: {i}/{total}")
            result = self.send_single_request(app_name, query)
            results.append(result)
            if i < total and delay > 0 and not self.dry_run:
                time.sleep(delay)
        successful = sum(1 for r in results if r['success'])
        failed = total - successful
        logging.info(f"批量处理完成: 成功 {successful}, 失败 {failed}")
        return results

    def save_results(self, results: List[Dict[str, Any]], filename: str = "batch_tasks_results.json"):
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logging.info(f"结果已保存到: {filename}")
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")

def main():
    dry_run = False  # 如需 dry_run，改为 True
    processor = TaskBatchProcessor(dry_run=dry_run)
    tasks = processor.parse_tasks_csv('tasks.csv')
    results = processor.batch_process(tasks, delay=0.3)
    processor.save_results(results)
    successful = sum(1 for r in results if r['success'])
    print(f"\n处理完成: 总数: {len(results)} 成功: {successful} 失败: {len(results) - successful}")

if __name__ == "__main__":
    main() 