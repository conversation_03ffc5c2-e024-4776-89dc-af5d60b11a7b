# Python virtual environment
venv/
.venv/
env/
.env/

# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/

# IDE settings
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs and databases
*.log
*.sqlite3
*.db

# Environment variables
.env
.env.local

# Testing
.pytest_cache/
.coverage
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints

# Local development settings
local_settings.py

# Temporary files
*.tmp
*.bak
*.pid