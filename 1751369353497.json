[{"id": 4007, "rtx": "v_min<PERSON><PERSON><PERSON>", "appId": "wx6c03ed6dfa30c735", "isDeleted": 0, "targetId": "2358", "userId": "3191454962", "instruction": "我是Ella，第13洞我打了0，Bob打了Double Par，小白打了信天翁", "note": "", "submitTime": "2025-07-01 11:27:43", "operations": [{"id": 42486, "isDeleted": 0, "type": "start", "markConfig": {"uuid": "", "x": 0.26944444444444443, "y": 0.36363636363636365, "width": 0.6111111111111112, "height": 0.029411764705882353, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369254982.jpg", "domXML": "<html>\n  <body>\n    <wx-view id=\"2\" event=\"['tap']\">\n      <wx-view id=\"1\">请您完善个人信息</wx-view>\n    </wx-view>\n    <wx-view[2] id=\"8\" event=\"['touchend', 'touchmove', 'touchstart']\">\n      <wx-view>\n        <wx-view id=\"4\">预订</wx-view>\n      </wx-view>\n    </wx-view[2]>\n    <wx-view[5]>\n      <wx-view>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['touchstart']\">您有1 条未读消息</wx-view>\n          <wx-view[2] id=\"66\" event=\"['tap']\">\n            <wx-view>\n              <wx-view id=\"23\" event=\"['tap']\" />\n            </wx-view>\n            <wx-view[2]>\n              <wx-view>\n                <wx-view id=\"25\" event=\"['tap']\" />\n              </wx-view>\n              <wx-view[2] id=\"33\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                <wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"29\">?</wx-view>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view>\n              </wx-view[2]>\n              <wx-view[3] id=\"37\" event=\"['tap']\">\n                <wx-image id=\"36\" aria-label=\"识别结果:others;置信度:0.5610547\" />\n              </wx-view[3]>\n              <wx-view[4]>\n                <wx-swiper id=\"46\" event=\"['wxPositioningTargetReady', 'longpress', 'change']\" listener=\"['wheel']\">\n                  <div>\n                    <div>\n                      <div>\n                        <wx-swiper-item id=\"40\" event=\"['wxPositioningTargetReady']\">\n                          <wx-view id=\"39\">GolfLive</wx-view>\n                        </wx-swiper-item>\n                      </div>\n                    </div>\n                  </div>\n                </wx-swiper>\n              </wx-view[4]>\n            </wx-view[2]>\n            <wx-view[3]>\n              <wx-view id=\"57\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                <wx-image id=\"51\" aria-label=\"识别结果:close;置信度:0.5079489\" />\n                <wx-view>\n                  <wx-view id=\"53\">1.9万</wx-view>\n                </wx-view>\n                <wx-view[2] id=\"56\">赛事广场</wx-view[2]>\n              </wx-view>\n              <wx-view[3] id=\"64\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                <wx-image id=\"61\" aria-label=\"识别结果:close;置信度:0.5167549\" />\n                <wx-view id=\"63\">比赛/报名</wx-view>\n              </wx-view[3]>\n            </wx-view[3]>\n          </wx-view[2]>\n          <wx-view[4]>\n            <wx-view[2] id=\"78\" event=\"['touchstart']\">\n              <wx-image id=\"77\" aria-label=\"识别结果:close;置信度:0.5000903\" />\n            </wx-view[2]>\n          </wx-view[4]>\n          <wx-scroll-view id=\"215\" event=\"['wxPositioningTargetReady', 'longpress', 'touchmove', 'touchstart']\" listener=\"['scroll']\">\n            <div>\n              <div id=\"211\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                <div>\n                  <wx-view[3]>\n                    <wx-view id=\"157\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">-  今日比赛(1)</wx-view>\n                  </wx-view[3]>\n                  <wx-view[4] id=\"208\" event=\"['touchmove', 'touchstart']\">\n                    <wx-view>\n                      <wx-view id=\"204\" event=\"['longpress', 'tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-swiper id=\"177\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n                            <div>\n                              <div>\n                                <div>\n                                  <wx-swiper-item id=\"162\" event=\"['wxPositioningTargetReady']\">\n                                    <wx-image id=\"161\" id=\",9268145\" aria-label=\"识别结果:others;置信度:0.6354687\" event=\"['tap']\" />\n                                  </wx-swiper-item>\n                                </div>\n                              </div>\n                            </div>\n                          </wx-swiper>\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view>\n                            <wx-view id=\"180\">高尔夫比赛 小白 ella bob</wx-view>\n                            <wx-view[2] id=\"182\">(4)</wx-view[2]>\n                          </wx-view>\n                          <wx-view[2]>\n                            <wx-image id=\"191\" aria-label=\"识别结果:others;置信度:0.5693023\" />\n                            <wx-view id=\"193\" listener=\"['touchend', 'touchcancel']\">广州华美麓湖高尔夫球乡村俱乐部 (前九),(后九)</wx-view>\n                          </wx-view[2]>\n                          <wx-view[3]>\n                            <wx-image id=\"197\" aria-label=\"识别结果:others;置信度:0.62208587\" />\n                            <wx-view id=\"199\">07-01 星期二 18:24</wx-view>\n                            <wx-view[2] id=\"201\">正在进行</wx-view[2]>\n                          </wx-view[3]>\n                        </wx-view[2]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view[4]>\n                </div>\n              </div>\n            </div>\n          </wx-scroll-view>\n        </wx-view>\n      </wx-view>\n    </wx-view[5]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"[{\\\"rect\\\":{\\\"x\\\":0,\\\"y\\\":697.3333,\\\"width\\\":360,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"tab-bar\\\"},{\\\"rect\\\":{\\\"x\\\":0,\\\"y\\\":697.3333,\\\"width\\\":360,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":0,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":21.333334,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":23.333334,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":21.333334,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":41.333332,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":40.666668,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":21.333334,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":72,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":93.333336,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":95.333336,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":93.333336,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":113.333336,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":112.666664,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":93.333336,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":144,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":165.33333,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":167.33333,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":165.33333,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":185.33333,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":184.66667,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":165.33333,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":216,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":237.33333,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":239.33333,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":237.33333,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":257.33334,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":256.66666,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":237.33333,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":288,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":309.33334,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":311.33334,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":309.33334,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":329.33334,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":328.66666,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":309.33334,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"}]\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[5]/wx-view/wx-view/wx-scroll-view/div/div/div/wx-view[4]/wx-view/wx-view\", \"xpath\": \"/wx-view[2]/wx-view[2]/wx-view\", \"textContent\": \"小雨33℃高尔夫比赛小白ellabob(4)广州华美麓湖高尔夫球乡村俱乐部(前九),(后九)07-01星期二18:24正在进行\", \"pos\": {\"x\": 178, \"y\": 285}, \"center\": {\"x\": 178, \"y\": 285}, \"target\": {\"rect\": {\"x\": 97, \"y\": 272, \"left\": 97, \"top\": 272, \"width\": 220, \"height\": 22}, \"parentRect\": {\"x\": 10, \"y\": 240, \"left\": 10, \"top\": 240, \"width\": 346, \"height\": 85}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native><tab-bar>\r\n  <container>\r\n    <container id=\"261272600\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>首页</text>\r\n      </container>\r\n    </container>\r\n    <container[2] id=\"48240954\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>视频</text>\r\n      </container>\r\n    </container[2]>\r\n    <container[3] id=\"249191267\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>球友</text>\r\n      </container>\r\n    </container[3]>\r\n    <container[4] id=\"240505819\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>历史</text>\r\n      </container>\r\n    </container[4]>\r\n    <container[5] id=\"46719827\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>我的</text>\r\n      </container>\r\n    </container[5]>\r\n  </container>\r\n</tab-bar></native>", "parent": "html/body/wx-view[5]/wx-view/wx-view/wx-scroll-view/div/div/div/wx-view[4]/wx-view/wx-view", "path_": "pages/Home/Home.html", "xpath": "/wx-view[2]/wx-view[2]/wx-view"}}, {"id": 42487, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.26944444444444443, "y": 0.36363636363636365, "width": 0.6111111111111112, "height": 0.029411764705882353, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369254982.jpg", "domXML": "<html>\n  <body>\n    <wx-view id=\"2\" event=\"['tap']\">\n      <wx-view id=\"1\">请您完善个人信息</wx-view>\n    </wx-view>\n    <wx-view[2] id=\"8\" event=\"['touchend', 'touchmove', 'touchstart']\">\n      <wx-view>\n        <wx-view id=\"4\">预订</wx-view>\n      </wx-view>\n    </wx-view[2]>\n    <wx-view[5]>\n      <wx-view>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['touchstart']\">您有1 条未读消息</wx-view>\n          <wx-view[2] id=\"66\" event=\"['tap']\">\n            <wx-view>\n              <wx-view id=\"23\" event=\"['tap']\" />\n            </wx-view>\n            <wx-view[2]>\n              <wx-view>\n                <wx-view id=\"25\" event=\"['tap']\" />\n              </wx-view>\n              <wx-view[2] id=\"33\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                <wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"29\">?</wx-view>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view>\n              </wx-view[2]>\n              <wx-view[3] id=\"37\" event=\"['tap']\">\n                <wx-image id=\"36\" aria-label=\"识别结果:others;置信度:0.5610547\" />\n              </wx-view[3]>\n              <wx-view[4]>\n                <wx-swiper id=\"46\" event=\"['wxPositioningTargetReady', 'longpress', 'change']\" listener=\"['wheel']\">\n                  <div>\n                    <div>\n                      <div>\n                        <wx-swiper-item id=\"40\" event=\"['wxPositioningTargetReady']\">\n                          <wx-view id=\"39\">GolfLive</wx-view>\n                        </wx-swiper-item>\n                      </div>\n                    </div>\n                  </div>\n                </wx-swiper>\n              </wx-view[4]>\n            </wx-view[2]>\n            <wx-view[3]>\n              <wx-view id=\"57\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                <wx-image id=\"51\" aria-label=\"识别结果:close;置信度:0.5079489\" />\n                <wx-view>\n                  <wx-view id=\"53\">1.9万</wx-view>\n                </wx-view>\n                <wx-view[2] id=\"56\">赛事广场</wx-view[2]>\n              </wx-view>\n              <wx-view[3] id=\"64\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                <wx-image id=\"61\" aria-label=\"识别结果:close;置信度:0.5167549\" />\n                <wx-view id=\"63\">比赛/报名</wx-view>\n              </wx-view[3]>\n            </wx-view[3]>\n          </wx-view[2]>\n          <wx-view[4]>\n            <wx-view[2] id=\"78\" event=\"['touchstart']\">\n              <wx-image id=\"77\" aria-label=\"识别结果:close;置信度:0.5000903\" />\n            </wx-view[2]>\n          </wx-view[4]>\n          <wx-scroll-view id=\"215\" event=\"['wxPositioningTargetReady', 'longpress', 'touchmove', 'touchstart']\" listener=\"['scroll']\">\n            <div>\n              <div id=\"211\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                <div>\n                  <wx-view[3]>\n                    <wx-view id=\"157\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">-  今日比赛(1)</wx-view>\n                  </wx-view[3]>\n                  <wx-view[4] id=\"208\" event=\"['touchmove', 'touchstart']\">\n                    <wx-view>\n                      <wx-view id=\"204\" event=\"['longpress', 'tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-swiper id=\"177\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n                            <div>\n                              <div>\n                                <div>\n                                  <wx-swiper-item id=\"162\" event=\"['wxPositioningTargetReady']\">\n                                    <wx-image id=\"161\" id=\",9268145\" aria-label=\"识别结果:others;置信度:0.6354687\" event=\"['tap']\" />\n                                  </wx-swiper-item>\n                                </div>\n                              </div>\n                            </div>\n                          </wx-swiper>\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view>\n                            <wx-view id=\"180\">高尔夫比赛 小白 ella bob</wx-view>\n                            <wx-view[2] id=\"182\">(4)</wx-view[2]>\n                          </wx-view>\n                          <wx-view[2]>\n                            <wx-image id=\"191\" aria-label=\"识别结果:others;置信度:0.5693023\" />\n                            <wx-view id=\"193\" listener=\"['touchend', 'touchcancel']\">广州华美麓湖高尔夫球乡村俱乐部 (前九),(后九)</wx-view>\n                          </wx-view[2]>\n                          <wx-view[3]>\n                            <wx-image id=\"197\" aria-label=\"识别结果:others;置信度:0.62208587\" />\n                            <wx-view id=\"199\">07-01 星期二 18:24</wx-view>\n                            <wx-view[2] id=\"201\">正在进行</wx-view[2]>\n                          </wx-view[3]>\n                        </wx-view[2]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view[4]>\n                </div>\n              </div>\n            </div>\n          </wx-scroll-view>\n        </wx-view>\n      </wx-view>\n    </wx-view[5]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"[{\\\"rect\\\":{\\\"x\\\":0,\\\"y\\\":697.3333,\\\"width\\\":360,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"tab-bar\\\"},{\\\"rect\\\":{\\\"x\\\":0,\\\"y\\\":697.3333,\\\"width\\\":360,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":0,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":21.333334,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":23.333334,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":21.333334,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":41.333332,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":40.666668,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":21.333334,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":72,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":93.333336,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":95.333336,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":93.333336,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":113.333336,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":112.666664,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":93.333336,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":144,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":165.33333,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":167.33333,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":165.33333,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":185.33333,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":184.66667,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":165.33333,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":216,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":237.33333,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":239.33333,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":237.33333,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":257.33334,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":256.66666,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":237.33333,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"},{\\\"rect\\\":{\\\"x\\\":288,\\\"y\\\":697.3333,\\\"width\\\":72,\\\"height\\\":50.666668},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":309.33334,\\\"y\\\":702.6667,\\\"width\\\":29.333334,\\\"height\\\":40},\\\"tagName\\\":\\\"container\\\"},{\\\"rect\\\":{\\\"x\\\":311.33334,\\\"y\\\":702.6667,\\\"width\\\":25.333334,\\\"height\\\":25.333334},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":309.33334,\\\"y\\\":728,\\\"width\\\":29.333334,\\\"height\\\":14.666667},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":329.33334,\\\"y\\\":702.6667,\\\"width\\\":19,\\\"height\\\":19},\\\"tagName\\\":\\\"text\\\"},{\\\"rect\\\":{\\\"x\\\":328.66666,\\\"y\\\":703.6667,\\\"width\\\":10.666667,\\\"height\\\":10.666667},\\\"tagName\\\":\\\"image\\\"},{\\\"rect\\\":{\\\"x\\\":309.33334,\\\"y\\\":744.3333,\\\"width\\\":29.333334,\\\"height\\\":3.6666667},\\\"tagName\\\":\\\"view\\\"}]\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[5]/wx-view/wx-view/wx-scroll-view/div/div/div/wx-view[4]/wx-view/wx-view\", \"xpath\": \"/wx-view[2]/wx-view[2]/wx-view\", \"textContent\": \"小雨33℃高尔夫比赛小白ellabob(4)广州华美麓湖高尔夫球乡村俱乐部(前九),(后九)07-01星期二18:24正在进行\", \"pos\": {\"x\": 178, \"y\": 285}, \"center\": {\"x\": 178, \"y\": 285}, \"target\": {\"rect\": {\"x\": 97, \"y\": 272, \"left\": 97, \"top\": 272, \"width\": 220, \"height\": 22}, \"parentRect\": {\"x\": 10, \"y\": 240, \"left\": 10, \"top\": 240, \"width\": 346, \"height\": 85}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native><tab-bar>\r\n  <container>\r\n    <container id=\"261272600\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>首页</text>\r\n      </container>\r\n    </container>\r\n    <container[2] id=\"48240954\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>视频</text>\r\n      </container>\r\n    </container[2]>\r\n    <container[3] id=\"249191267\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>球友</text>\r\n      </container>\r\n    </container[3]>\r\n    <container[4] id=\"240505819\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>历史</text>\r\n      </container>\r\n    </container[4]>\r\n    <container[5] id=\"46719827\" event=\"tap\">\r\n      <container>\r\n        <image />\r\n        <text>我的</text>\r\n      </container>\r\n    </container[5]>\r\n  </container>\r\n</tab-bar></native>", "parent": "html/body/wx-view[5]/wx-view/wx-view/wx-scroll-view/div/div/div/wx-view[4]/wx-view/wx-view", "path_": "pages/Home/Home.html", "xpath": "/wx-view[2]/wx-view[2]/wx-view"}}, {"id": 42488, "isDeleted": 0, "type": "scroll", "markConfig": {"uuid": "", "x": 0.8138888888888889, "y": 0.6831550802139037, "width": 0, "height": 0, "name": "", "color": ""}, "direction": "right", "length": 108, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369255525.jpg", "domXML": "<html>\n  <body id=\"717\" event=\"['touchstart', 'touchmove']\">\n    <wx-view>\n      <wx-view id=\"12\" event=\"['tap']\">\n        <wx-image id=\"11\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"16\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"15\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"19\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view>\n    <wx-view[2] id=\"27\" event=\"['tap']\">\n      <wx-view[2] id=\"26\">球道</wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-scroll-view id=\"709\" event=\"['wxPositioningTargetReady', 'scrolltolower', 'touchstart']\" listener=\"['scroll']\">\n        <div>\n          <div id=\"705\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n            <div>\n              <wx-view>\n                <wx-view[2]>\n                  <wx-view>\n                    <wx-view id=\"45\" event=\"['tap']\">\n                      <wx-view>\n                        <wx-view id=\"32\" event=\"['tap']\">\n                          <wx-image id=\"31\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view id=\"38\" event=\"['tap']\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"43\" event=\"['longpress', 'tap']\">\n                          <wx-image id=\"42\" aria-label=\"识别结果:others;置信度:0.5111935\" />\n                        </wx-view[3]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"49\" event=\"['tap']\">记分组</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"52\" event=\"['tap']\">照片墙</wx-view[2]>\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view[2]>\n                <wx-view[3]>\n                  <wx-view>\n                    <wx-view[2]>\n                      <wx-view id=\"65\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"61\" aria-label=\"识别结果:others;置信度:0.54767895\" />\n                        </wx-view>\n                        <wx-view[2] id=\"64\">人工算分</wx-view[2]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view id=\"73\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"69\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"72\">PK规则</wx-view[2]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </wx-view>\n                </wx-view[3]>\n                <wx-view[4]>\n                  <wx-view>\n                    <wx-view id=\"82\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"80\" aria-label=\"识别结果:others;置信度:0.5463394\" />\n                    </wx-view>\n                    <wx-view[3] id=\"88\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"86\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[3]>\n                    <wx-view[5] id=\"93\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"92\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[5]>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"96\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">广州华美麓湖高尔夫球乡村俱乐部 ((前九),(后九))</wx-view>\n                      <wx-view[2] id=\"98\">小雨 33℃ 风力:4-5级 紫外线:弱</wx-view[2]>\n                      <wx-view[3] id=\"100\">2025-07-01  星期二  18:24</wx-view[3]>\n                      <wx-view[4] id=\"102\">比赛进行中</wx-view[4]>\n                    </wx-view>\n                  </wx-view[2]>\n                  <wx-view[3]>\n                    <wx-view>\n                      <wx-view id=\"108\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"107\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view>\n                      <wx-view[2] id=\"112\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"111\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[3]>\n                </wx-view[4]>\n                <wx-view[5]>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"118\" event=\"['tap']\">导出图片</wx-view>\n                      </wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"122\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"121\">攻略</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"128\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"124\">成绩和推杆</wx-view>\n                            <wx-image id=\"127\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          </wx-view[2]>\n                        </wx-view>\n                      </wx-view[2]>\n                      <wx-view[3]>\n                        <wx-view>\n                          <wx-image id=\"133\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        </wx-view>\n                        <wx-view[2] id=\"143\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"137\">ella</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"139\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[3]>\n                      <wx-view[4]>\n                        <wx-view>\n                          <wx-image id=\"147\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"157\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"151\">小白</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"153\">76</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[4]>\n                      <wx-view[5]>\n                        <wx-view>\n                          <wx-image id=\"161\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"171\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"165\">bob</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"167\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[5]>\n                      <wx-view[6]>\n                        <wx-view id=\"178\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"175\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          <wx-view id=\"177\">球手</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"182\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"181\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"185\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"184\">Stableford</wx-view>\n                        </wx-view[3]>\n                        <wx-view[4] id=\"188\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"187\">人数限制:4</wx-view>\n                        </wx-view[4]>\n                        <wx-view[5] id=\"191\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"190\">改球场</wx-view>\n                        </wx-view[5]>\n                      </wx-view[6]>\n                    </wx-view>\n                    <wx-scroll-view id=\"699\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n                      <div>\n                        <div id=\"695\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                          <div>\n                            <wx-view>\n                              <wx-view>\n                                <wx-view>\n                                  <wx-view id=\"195\">(前九)</wx-view>\n                                </wx-view>\n                              </wx-view>\n                              <wx-view[2]>\n                                <wx-view[3]>\n                                  <wx-view id=\"211\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"210\">3</wx-view>\n                                  </wx-view>\n                                </wx-view[3]>\n                                <wx-view[4]>\n                                  <wx-view id=\"215\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"214\">4</wx-view>\n                                  </wx-view>\n                                </wx-view[4]>\n                                <wx-view[5]>\n                                  <wx-view id=\"219\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"218\">5</wx-view>\n                                  </wx-view>\n                                </wx-view[5]>\n                                <wx-view[6]>\n                                  <wx-view id=\"223\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"222\">6</wx-view>\n                                  </wx-view>\n                                </wx-view[6]>\n                                <wx-view[7]>\n                                  <wx-view id=\"227\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"226\">7</wx-view>\n                                  </wx-view>\n                                </wx-view[7]>\n                                <wx-view[8]>\n                                  <wx-view id=\"231\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"230\">8</wx-view>\n                                  </wx-view>\n                                </wx-view[8]>\n                              </wx-view[2]>\n                              <wx-view[3]>\n                                <wx-view[3] id=\"297\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"296\">4</wx-view>\n                                </wx-view[3]>\n                                <wx-view[4] id=\"300\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"299\">4</wx-view>\n                                </wx-view[4]>\n                                <wx-view[5] id=\"303\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"302\">3</wx-view>\n                                </wx-view[5]>\n                                <wx-view[6] id=\"306\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"305\">4</wx-view>\n                                </wx-view[6]>\n                                <wx-view[7] id=\"309\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"308\">4</wx-view>\n                                </wx-view[7]>\n                                <wx-view[8] id=\"312\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"311\">3</wx-view>\n                                </wx-view[8]>\n                              </wx-view[3]>\n                              <wx-view[4]>\n                                <wx-view>\n                                  <wx-view[3] id=\"373\" event=\"['tap']\" />\n                                  <wx-view[4] id=\"378\" event=\"['tap']\" />\n                                  <wx-view[5] id=\"383\" event=\"['tap']\" />\n                                  <wx-view[6] id=\"388\" event=\"['tap']\" />\n                                  <wx-view[7] id=\"393\" event=\"['tap']\" />\n                                  <wx-view[8] id=\"398\" event=\"['tap']\" />\n                                </wx-view>\n                                <wx-view[2]>\n                                  <wx-view[3] id=\"484\" event=\"['tap']\" />\n                                  <wx-view[4] id=\"489\" event=\"['tap']\" />\n                                  <wx-view[5] id=\"494\" event=\"['tap']\" />\n                                  <wx-view[6] id=\"499\" event=\"['tap']\" />\n                                  <wx-view[7] id=\"504\" event=\"['tap']\" />\n                                  <wx-view[8] id=\"509\" event=\"['tap']\" />\n                                </wx-view[2]>\n                                <wx-view[3]>\n                                  <wx-view[3] id=\"595\" event=\"['tap']\" />\n                                  <wx-view[4] id=\"600\" event=\"['tap']\" />\n                                  <wx-view[5] id=\"605\" event=\"['tap']\" />\n                                  <wx-view[6] id=\"610\" event=\"['tap']\" />\n                                  <wx-view[7] id=\"615\" event=\"['tap']\" />\n                                  <wx-view[8] id=\"620\" event=\"['tap']\" />\n                                </wx-view[3]>\n                              </wx-view[4]>\n                            </wx-view>\n                          </div>\n                        </div>\n                      </div>\n                    </wx-scroll-view>\n                  </wx-view[2]>\n                </wx-view[5]>\n              </wx-view>\n            </div>\n          </div>\n        </div>\n      </wx-scroll-view>\n    </wx-view[3]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": null}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"touchstart\", \"xpath\": \"html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[5]/wx-view[2]/wx-view/wx-view\", \"touches\": [{\"clientX\": 293, \"clientY\": 511}], \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "", "path_": "SubPackD/Match/Match.html", "xpath": "html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[5]/wx-view[2]/wx-view/wx-view"}}, {"id": 42489, "isDeleted": 0, "type": "scroll", "markConfig": {"uuid": "", "x": 0.8611111111111112, "y": 0.6831550802139037, "width": 0, "height": 0, "name": "", "color": ""}, "direction": "right", "length": 135, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369255913.jpg", "domXML": "<html>\n  <body id=\"717\" event=\"['touchstart', 'touchmove']\">\n    <wx-view>\n      <wx-view id=\"12\" event=\"['tap']\">\n        <wx-image id=\"11\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"16\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"15\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"19\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view>\n    <wx-view[2] id=\"27\" event=\"['tap']\">\n      <wx-view[2] id=\"26\">球道</wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-scroll-view id=\"709\" event=\"['wxPositioningTargetReady', 'scrolltolower', 'touchstart']\" listener=\"['scroll']\">\n        <div>\n          <div id=\"705\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n            <div>\n              <wx-view>\n                <wx-view[2]>\n                  <wx-view>\n                    <wx-view id=\"45\" event=\"['tap']\">\n                      <wx-view>\n                        <wx-view id=\"32\" event=\"['tap']\">\n                          <wx-image id=\"31\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view id=\"38\" event=\"['tap']\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"43\" event=\"['longpress', 'tap']\">\n                          <wx-image id=\"42\" aria-label=\"识别结果:others;置信度:0.5111935\" />\n                        </wx-view[3]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"49\" event=\"['tap']\">记分组</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"52\" event=\"['tap']\">照片墙</wx-view[2]>\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view[2]>\n                <wx-view[3]>\n                  <wx-view>\n                    <wx-view[2]>\n                      <wx-view id=\"65\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"61\" aria-label=\"识别结果:others;置信度:0.54767895\" />\n                        </wx-view>\n                        <wx-view[2] id=\"64\">人工算分</wx-view[2]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view id=\"73\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"69\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"72\">PK规则</wx-view[2]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </wx-view>\n                </wx-view[3]>\n                <wx-view[4]>\n                  <wx-view>\n                    <wx-view id=\"82\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"80\" aria-label=\"识别结果:others;置信度:0.5463394\" />\n                    </wx-view>\n                    <wx-view[3] id=\"88\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"86\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[3]>\n                    <wx-view[5] id=\"93\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"92\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[5]>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"96\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">广州华美麓湖高尔夫球乡村俱乐部 ((前九),(后九))</wx-view>\n                      <wx-view[2] id=\"98\">小雨 33℃ 风力:4-5级 紫外线:弱</wx-view[2]>\n                      <wx-view[3] id=\"100\">2025-07-01  星期二  18:24</wx-view[3]>\n                      <wx-view[4] id=\"102\">比赛进行中</wx-view[4]>\n                    </wx-view>\n                  </wx-view[2]>\n                  <wx-view[3]>\n                    <wx-view>\n                      <wx-view id=\"108\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"107\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view>\n                      <wx-view[2] id=\"112\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"111\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[3]>\n                </wx-view[4]>\n                <wx-view[5]>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"118\" event=\"['tap']\">导出图片</wx-view>\n                      </wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"122\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"121\">攻略</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"128\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"124\">成绩和推杆</wx-view>\n                            <wx-image id=\"127\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          </wx-view[2]>\n                        </wx-view>\n                      </wx-view[2]>\n                      <wx-view[3]>\n                        <wx-view>\n                          <wx-image id=\"133\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        </wx-view>\n                        <wx-view[2] id=\"143\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"137\">ella</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"139\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[3]>\n                      <wx-view[4]>\n                        <wx-view>\n                          <wx-image id=\"147\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"157\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"151\">小白</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"153\">76</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[4]>\n                      <wx-view[5]>\n                        <wx-view>\n                          <wx-image id=\"161\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"171\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"165\">bob</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"167\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[5]>\n                      <wx-view[6]>\n                        <wx-view id=\"178\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"175\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          <wx-view id=\"177\">球手</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"182\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"181\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"185\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"184\">Stableford</wx-view>\n                        </wx-view[3]>\n                        <wx-view[4] id=\"188\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"187\">人数限制:4</wx-view>\n                        </wx-view[4]>\n                        <wx-view[5] id=\"191\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"190\">改球场</wx-view>\n                        </wx-view[5]>\n                      </wx-view[6]>\n                    </wx-view>\n                    <wx-scroll-view id=\"699\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n                      <div>\n                        <div id=\"695\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                          <div>\n                            <wx-view>\n                              <wx-view[2]>\n                                <wx-view[6]>\n                                  <wx-view id=\"223\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"222\">6</wx-view>\n                                  </wx-view>\n                                </wx-view[6]>\n                                <wx-view[7]>\n                                  <wx-view id=\"227\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"226\">7</wx-view>\n                                  </wx-view>\n                                </wx-view[7]>\n                                <wx-view[8]>\n                                  <wx-view id=\"231\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"230\">8</wx-view>\n                                  </wx-view>\n                                </wx-view[8]>\n                                <wx-view[9]>\n                                  <wx-view id=\"235\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"234\">9</wx-view>\n                                  </wx-view>\n                                </wx-view[9]>\n                                <wx-view[10]>\n                                  <wx-view id=\"238\">前9</wx-view>\n                                </wx-view[10]>\n                                <wx-view[11]>\n                                  <wx-view id=\"242\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"241\">10</wx-view>\n                                  </wx-view>\n                                </wx-view[11]>\n                              </wx-view[2]>\n                              <wx-view[3]>\n                                <wx-view[6] id=\"306\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"305\">4</wx-view>\n                                </wx-view[6]>\n                                <wx-view[7] id=\"309\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"308\">4</wx-view>\n                                </wx-view[7]>\n                                <wx-view[8] id=\"312\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"311\">3</wx-view>\n                                </wx-view[8]>\n                                <wx-view[9] id=\"315\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"314\">5</wx-view>\n                                </wx-view[9]>\n                                <wx-view[10] id=\"318\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"317\">36</wx-view>\n                                </wx-view[10]>\n                                <wx-view[11] id=\"321\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"320\">4</wx-view>\n                                </wx-view[11]>\n                              </wx-view[3]>\n                              <wx-view[4]>\n                                <wx-view>\n                                  <wx-view[6] id=\"388\" event=\"['tap']\" />\n                                  <wx-view[7] id=\"393\" event=\"['tap']\" />\n                                  <wx-view[8] id=\"398\" event=\"['tap']\" />\n                                  <wx-view[9] id=\"403\" event=\"['tap']\" />\n                                  <wx-view[10]>\n                                    <wx-view id=\"405\">0</wx-view>\n                                  </wx-view[10]>\n                                  <wx-view[11] id=\"411\" event=\"['tap']\" />\n                                </wx-view>\n                                <wx-view[2]>\n                                  <wx-view[6] id=\"499\" event=\"['tap']\" />\n                                  <wx-view[7] id=\"504\" event=\"['tap']\" />\n                                  <wx-view[8] id=\"509\" event=\"['tap']\" listener=\"['touchend', 'touchcancel']\" />\n                                  <wx-view[9] id=\"514\" event=\"['tap']\" />\n                                  <wx-view[10]>\n                                    <wx-view id=\"516\">0</wx-view>\n                                  </wx-view[10]>\n                                  <wx-view[11] id=\"522\" event=\"['tap']\" />\n                                </wx-view[2]>\n                                <wx-view[3]>\n                                  <wx-view[6] id=\"610\" event=\"['tap']\" />\n                                  <wx-view[7] id=\"615\" event=\"['tap']\" />\n                                  <wx-view[8] id=\"620\" event=\"['tap']\" />\n                                  <wx-view[9] id=\"625\" event=\"['tap']\" />\n                                  <wx-view[10]>\n                                    <wx-view id=\"627\">0</wx-view>\n                                  </wx-view[10]>\n                                  <wx-view[11] id=\"633\" event=\"['tap']\" />\n                                </wx-view[3]>\n                              </wx-view[4]>\n                            </wx-view>\n                          </div>\n                        </div>\n                      </div>\n                    </wx-scroll-view>\n                  </wx-view[2]>\n                </wx-view[5]>\n              </wx-view>\n            </div>\n          </div>\n        </div>\n      </wx-scroll-view>\n    </wx-view[3]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": null}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"touchstart\", \"xpath\": \"html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[8]\", \"touches\": [{\"clientX\": 310, \"clientY\": 511}], \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "", "path_": "SubPackD/Match/Match.html", "xpath": "html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[8]"}}, {"id": 42490, "isDeleted": 0, "type": "scroll", "markConfig": {"uuid": "", "x": 0.8666666666666667, "y": 0.6778074866310161, "width": 0, "height": 0, "name": "", "color": ""}, "direction": "right", "length": 182, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369256302.jpg", "domXML": "<html>\n  <body id=\"717\" event=\"['touchstart', 'touchmove']\">\n    <wx-view>\n      <wx-view id=\"12\" event=\"['tap']\">\n        <wx-image id=\"11\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"16\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"15\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"19\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view>\n    <wx-view[2] id=\"27\" event=\"['tap']\">\n      <wx-view[2] id=\"26\">球道</wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-scroll-view id=\"709\" event=\"['wxPositioningTargetReady', 'scrolltolower', 'touchstart']\" listener=\"['scroll']\">\n        <div>\n          <div id=\"705\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n            <div>\n              <wx-view>\n                <wx-view[2]>\n                  <wx-view>\n                    <wx-view id=\"45\" event=\"['tap']\">\n                      <wx-view>\n                        <wx-view id=\"32\" event=\"['tap']\">\n                          <wx-image id=\"31\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view id=\"38\" event=\"['tap']\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"43\" event=\"['longpress', 'tap']\">\n                          <wx-image id=\"42\" aria-label=\"识别结果:others;置信度:0.5111935\" />\n                        </wx-view[3]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"49\" event=\"['tap']\">记分组</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"52\" event=\"['tap']\">照片墙</wx-view[2]>\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view[2]>\n                <wx-view[3]>\n                  <wx-view>\n                    <wx-view[2]>\n                      <wx-view id=\"65\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"61\" aria-label=\"识别结果:others;置信度:0.54767895\" />\n                        </wx-view>\n                        <wx-view[2] id=\"64\">人工算分</wx-view[2]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view id=\"73\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"69\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"72\">PK规则</wx-view[2]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </wx-view>\n                </wx-view[3]>\n                <wx-view[4]>\n                  <wx-view>\n                    <wx-view id=\"82\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"80\" aria-label=\"识别结果:others;置信度:0.5463394\" />\n                    </wx-view>\n                    <wx-view[3] id=\"88\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"86\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[3]>\n                    <wx-view[5] id=\"93\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"92\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[5]>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"96\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">广州华美麓湖高尔夫球乡村俱乐部 ((前九),(后九))</wx-view>\n                      <wx-view[2] id=\"98\">小雨 33℃ 风力:4-5级 紫外线:弱</wx-view[2]>\n                      <wx-view[3] id=\"100\">2025-07-01  星期二  18:24</wx-view[3]>\n                      <wx-view[4] id=\"102\">比赛进行中</wx-view[4]>\n                    </wx-view>\n                  </wx-view[2]>\n                  <wx-view[3]>\n                    <wx-view>\n                      <wx-view id=\"108\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"107\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view>\n                      <wx-view[2] id=\"112\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"111\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[3]>\n                </wx-view[4]>\n                <wx-view[5]>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"118\" event=\"['tap']\">导出图片</wx-view>\n                      </wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"122\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"121\">攻略</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"128\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"124\">成绩和推杆</wx-view>\n                            <wx-image id=\"127\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          </wx-view[2]>\n                        </wx-view>\n                      </wx-view[2]>\n                      <wx-view[3]>\n                        <wx-view>\n                          <wx-image id=\"133\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        </wx-view>\n                        <wx-view[2] id=\"143\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"137\">ella</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"139\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[3]>\n                      <wx-view[4]>\n                        <wx-view>\n                          <wx-image id=\"147\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"157\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"151\">小白</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"153\">76</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[4]>\n                      <wx-view[5]>\n                        <wx-view>\n                          <wx-image id=\"161\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"171\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"165\">bob</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"167\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[5]>\n                      <wx-view[6]>\n                        <wx-view id=\"178\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"175\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          <wx-view id=\"177\">球手</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"182\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"181\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"185\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"184\">Stableford</wx-view>\n                        </wx-view[3]>\n                        <wx-view[4] id=\"188\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"187\">人数限制:4</wx-view>\n                        </wx-view[4]>\n                        <wx-view[5] id=\"191\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"190\">改球场</wx-view>\n                        </wx-view[5]>\n                      </wx-view[6]>\n                    </wx-view>\n                    <wx-scroll-view id=\"699\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n                      <div>\n                        <div id=\"695\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                          <div>\n                            <wx-view>\n                              <wx-view>\n                                <wx-view[2]>\n                                  <wx-view id=\"198\">(后九)</wx-view>\n                                </wx-view[2]>\n                              </wx-view>\n                              <wx-view[2]>\n                                <wx-view[15]>\n                                  <wx-view id=\"258\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"257\">14</wx-view>\n                                  </wx-view>\n                                </wx-view[15]>\n                                <wx-view[16]>\n                                  <wx-view id=\"262\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"261\">15</wx-view>\n                                  </wx-view>\n                                </wx-view[16]>\n                                <wx-view[17]>\n                                  <wx-view id=\"266\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"265\">16</wx-view>\n                                  </wx-view>\n                                </wx-view[17]>\n                                <wx-view[18]>\n                                  <wx-view id=\"270\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"269\">17</wx-view>\n                                  </wx-view>\n                                </wx-view[18]>\n                                <wx-view[19]>\n                                  <wx-view id=\"274\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"273\">18</wx-view>\n                                  </wx-view>\n                                </wx-view[19]>\n                                <wx-view[20]>\n                                  <wx-view id=\"277\">后9</wx-view>\n                                </wx-view[20]>\n                              </wx-view[2]>\n                              <wx-view[3]>\n                                <wx-view[15] id=\"333\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"332\">4</wx-view>\n                                </wx-view[15]>\n                                <wx-view[16] id=\"336\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"335\">4</wx-view>\n                                </wx-view[16]>\n                                <wx-view[17] id=\"339\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"338\">3</wx-view>\n                                </wx-view[17]>\n                                <wx-view[18] id=\"342\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"341\">5</wx-view>\n                                </wx-view[18]>\n                                <wx-view[19] id=\"345\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"344\">4</wx-view>\n                                </wx-view[19]>\n                                <wx-view[20] id=\"348\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"347\">36</wx-view>\n                                </wx-view[20]>\n                              </wx-view[3]>\n                              <wx-view[4]>\n                                <wx-view>\n                                  <wx-view[15] id=\"432\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"429\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"438\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"435\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"444\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"441\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                  <wx-view[18] id=\"450\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"447\">0</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[18]>\n                                  <wx-view[19] id=\"456\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"453\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[19]>\n                                  <wx-view[20]>\n                                    <wx-view id=\"458\">-4</wx-view>\n                                  </wx-view[20]>\n                                </wx-view>\n                                <wx-view[2]>\n                                  <wx-view[15] id=\"543\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"540\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"549\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"546\" listener=\"['touchend', 'touchcancel']\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"555\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"552\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                  <wx-view[18] id=\"561\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"558\">+2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[18]>\n                                  <wx-view[19] id=\"567\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"564\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[19]>\n                                  <wx-view[20]>\n                                    <wx-view id=\"569\">+4</wx-view>\n                                  </wx-view[20]>\n                                </wx-view[2]>\n                                <wx-view[3]>\n                                  <wx-view[15] id=\"654\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"651\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"660\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"657\">+2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"666\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"663\">-2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                  <wx-view[18] id=\"672\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"669\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[18]>\n                                  <wx-view[19] id=\"678\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"675\">-2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[19]>\n                                  <wx-view[20]>\n                                    <wx-view id=\"680\">-4</wx-view>\n                                  </wx-view[20]>\n                                </wx-view[3]>\n                              </wx-view[4]>\n                            </wx-view>\n                          </div>\n                        </div>\n                      </div>\n                    </wx-scroll-view>\n                  </wx-view[2]>\n                </wx-view[5]>\n              </wx-view>\n            </div>\n          </div>\n        </div>\n      </wx-scroll-view>\n    </wx-view[3]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": null}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"touchstart\", \"xpath\": \"html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[16]/wx-view[2]/wx-view/wx-view\", \"touches\": [{\"clientX\": 312, \"clientY\": 507}], \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "", "path_": "SubPackD/Match/Match.html", "xpath": "html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[16]/wx-view[2]/wx-view/wx-view"}}, {"id": 42491, "isDeleted": 0, "type": "scroll", "markConfig": {"uuid": "", "x": 0.3888888888888889, "y": 0.6697860962566845, "width": 0, "height": 0, "name": "", "color": ""}, "direction": "left", "length": 184, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369256790.jpg", "domXML": "<html>\n  <body id=\"717\" event=\"['touchstart', 'touchmove']\">\n    <wx-view>\n      <wx-view id=\"12\" event=\"['tap']\">\n        <wx-image id=\"11\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"16\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"15\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"19\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view>\n    <wx-view[2] id=\"27\" event=\"['tap']\">\n      <wx-view[2] id=\"26\">球道</wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-scroll-view id=\"709\" event=\"['wxPositioningTargetReady', 'scrolltolower', 'touchstart']\" listener=\"['scroll']\">\n        <div>\n          <div id=\"705\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n            <div>\n              <wx-view>\n                <wx-view[2]>\n                  <wx-view>\n                    <wx-view id=\"45\" event=\"['tap']\">\n                      <wx-view>\n                        <wx-view id=\"32\" event=\"['tap']\">\n                          <wx-image id=\"31\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view id=\"38\" event=\"['tap']\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"43\" event=\"['longpress', 'tap']\">\n                          <wx-image id=\"42\" aria-label=\"识别结果:others;置信度:0.5111935\" />\n                        </wx-view[3]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"49\" event=\"['tap']\">记分组</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"52\" event=\"['tap']\">照片墙</wx-view[2]>\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view[2]>\n                <wx-view[3]>\n                  <wx-view>\n                    <wx-view[2]>\n                      <wx-view id=\"65\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"61\" aria-label=\"识别结果:others;置信度:0.54767895\" />\n                        </wx-view>\n                        <wx-view[2] id=\"64\">人工算分</wx-view[2]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view id=\"73\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"69\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"72\">PK规则</wx-view[2]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </wx-view>\n                </wx-view[3]>\n                <wx-view[4]>\n                  <wx-view>\n                    <wx-view id=\"82\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"80\" aria-label=\"识别结果:others;置信度:0.5463394\" />\n                    </wx-view>\n                    <wx-view[3] id=\"88\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"86\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[3]>\n                    <wx-view[5] id=\"93\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"92\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[5]>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"96\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">广州华美麓湖高尔夫球乡村俱乐部 ((前九),(后九))</wx-view>\n                      <wx-view[2] id=\"98\">小雨 33℃ 风力:4-5级 紫外线:弱</wx-view[2]>\n                      <wx-view[3] id=\"100\">2025-07-01  星期二  18:24</wx-view[3]>\n                      <wx-view[4] id=\"102\">比赛进行中</wx-view[4]>\n                    </wx-view>\n                  </wx-view[2]>\n                  <wx-view[3]>\n                    <wx-view>\n                      <wx-view id=\"108\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"107\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view>\n                      <wx-view[2] id=\"112\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"111\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[3]>\n                </wx-view[4]>\n                <wx-view[5]>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"118\" event=\"['tap']\">导出图片</wx-view>\n                      </wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"122\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"121\">攻略</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"128\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"124\">成绩和推杆</wx-view>\n                            <wx-image id=\"127\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          </wx-view[2]>\n                        </wx-view>\n                      </wx-view[2]>\n                      <wx-view[3]>\n                        <wx-view>\n                          <wx-image id=\"133\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        </wx-view>\n                        <wx-view[2] id=\"143\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"137\">ella</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"139\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[3]>\n                      <wx-view[4]>\n                        <wx-view>\n                          <wx-image id=\"147\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"157\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"151\">小白</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"153\">76</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[4]>\n                      <wx-view[5]>\n                        <wx-view>\n                          <wx-image id=\"161\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"171\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"165\">bob</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"167\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[5]>\n                      <wx-view[6]>\n                        <wx-view id=\"178\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"175\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          <wx-view id=\"177\">球手</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"182\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"181\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"185\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"184\">Stableford</wx-view>\n                        </wx-view[3]>\n                        <wx-view[4] id=\"188\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"187\">人数限制:4</wx-view>\n                        </wx-view[4]>\n                        <wx-view[5] id=\"191\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"190\">改球场</wx-view>\n                        </wx-view[5]>\n                      </wx-view[6]>\n                    </wx-view>\n                    <wx-scroll-view id=\"699\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n                      <div>\n                        <div id=\"695\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                          <div>\n                            <wx-view>\n                              <wx-view[2]>\n                                <wx-view[16]>\n                                  <wx-view id=\"262\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"261\">15</wx-view>\n                                  </wx-view>\n                                </wx-view[16]>\n                                <wx-view[17]>\n                                  <wx-view id=\"266\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"265\">16</wx-view>\n                                  </wx-view>\n                                </wx-view[17]>\n                                <wx-view[18]>\n                                  <wx-view id=\"270\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"269\">17</wx-view>\n                                  </wx-view>\n                                </wx-view[18]>\n                                <wx-view[19]>\n                                  <wx-view id=\"274\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"273\">18</wx-view>\n                                  </wx-view>\n                                </wx-view[19]>\n                                <wx-view[20]>\n                                  <wx-view id=\"277\">后9</wx-view>\n                                </wx-view[20]>\n                                <wx-view[21]>\n                                  <wx-view id=\"280\">总差</wx-view>\n                                </wx-view[21]>\n                              </wx-view[2]>\n                              <wx-view[3]>\n                                <wx-view[16] id=\"336\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"335\">4</wx-view>\n                                </wx-view[16]>\n                                <wx-view[17] id=\"339\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"338\">3</wx-view>\n                                </wx-view[17]>\n                                <wx-view[18] id=\"342\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"341\">5</wx-view>\n                                </wx-view[18]>\n                                <wx-view[19] id=\"345\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"344\">4</wx-view>\n                                </wx-view[19]>\n                                <wx-view[20] id=\"348\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"347\">36</wx-view>\n                                </wx-view[20]>\n                                <wx-view[21] id=\"351\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"350\">0</wx-view>\n                                </wx-view[21]>\n                              </wx-view[3]>\n                              <wx-view[4]>\n                                <wx-view>\n                                  <wx-view[16] id=\"438\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"435\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"444\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"441\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                  <wx-view[18] id=\"450\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"447\">0</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[18]>\n                                  <wx-view[19] id=\"456\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"453\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[19]>\n                                  <wx-view[20]>\n                                    <wx-view id=\"458\">-4</wx-view>\n                                  </wx-view[20]>\n                                  <wx-view[21]>\n                                    <wx-view id=\"461\">-4</wx-view>\n                                  </wx-view[21]>\n                                </wx-view>\n                                <wx-view[2]>\n                                  <wx-view[16] id=\"549\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"546\" listener=\"['touchend', 'touchcancel']\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"555\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"552\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                  <wx-view[18] id=\"561\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"558\">+2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[18]>\n                                  <wx-view[19] id=\"567\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"564\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[19]>\n                                  <wx-view[20]>\n                                    <wx-view id=\"569\">+4</wx-view>\n                                  </wx-view[20]>\n                                  <wx-view[21]>\n                                    <wx-view id=\"572\">+4</wx-view>\n                                  </wx-view[21]>\n                                </wx-view[2]>\n                                <wx-view[3]>\n                                  <wx-view[16] id=\"660\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"657\">+2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"666\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"663\">-2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                  <wx-view[18] id=\"672\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"669\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[18]>\n                                  <wx-view[19] id=\"678\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"675\">-2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[19]>\n                                  <wx-view[20]>\n                                    <wx-view id=\"680\">-4</wx-view>\n                                  </wx-view[20]>\n                                  <wx-view[21]>\n                                    <wx-view id=\"683\">-4</wx-view>\n                                  </wx-view[21]>\n                                </wx-view[3]>\n                              </wx-view[4]>\n                            </wx-view>\n                          </div>\n                        </div>\n                      </div>\n                    </wx-scroll-view>\n                  </wx-view[2]>\n                </wx-view[5]>\n              </wx-view>\n            </div>\n          </div>\n        </div>\n      </wx-scroll-view>\n    </wx-view[3]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": null}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"touchstart\", \"xpath\": \"html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[16]/wx-view[2]/wx-view\", \"touches\": [{\"clientX\": 140, \"clientY\": 501}], \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "", "path_": "SubPackD/Match/Match.html", "xpath": "html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view[2]/wx-view[16]/wx-view[2]/wx-view"}}, {"id": 42492, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5388888888888889, "y": 0.5935828877005348, "width": 0.09444444444444444, "height": 0.0427807486631016, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369257231.jpg", "domXML": "<html>\n  <body id=\"717\" event=\"['touchstart', 'touchmove']\">\n    <wx-view>\n      <wx-view id=\"12\" event=\"['tap']\">\n        <wx-image id=\"11\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"16\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"15\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"19\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view>\n    <wx-view[2] id=\"27\" event=\"['tap']\">\n      <wx-view[2] id=\"26\">球道</wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-scroll-view id=\"709\" event=\"['wxPositioningTargetReady', 'scrolltolower', 'touchstart']\" listener=\"['scroll']\">\n        <div>\n          <div id=\"705\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n            <div>\n              <wx-view>\n                <wx-view[2]>\n                  <wx-view>\n                    <wx-view id=\"45\" event=\"['tap']\">\n                      <wx-view>\n                        <wx-view id=\"32\" event=\"['tap']\">\n                          <wx-image id=\"31\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view id=\"38\" event=\"['tap']\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"43\" event=\"['longpress', 'tap']\">\n                          <wx-image id=\"42\" aria-label=\"识别结果:others;置信度:0.5111935\" />\n                        </wx-view[3]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"49\" event=\"['tap']\">记分组</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"52\" event=\"['tap']\">照片墙</wx-view[2]>\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view[2]>\n                <wx-view[3]>\n                  <wx-view>\n                    <wx-view[2]>\n                      <wx-view id=\"65\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"61\" aria-label=\"识别结果:others;置信度:0.54767895\" />\n                        </wx-view>\n                        <wx-view[2] id=\"64\">人工算分</wx-view[2]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view id=\"73\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"69\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"72\">PK规则</wx-view[2]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </wx-view>\n                </wx-view[3]>\n                <wx-view[4]>\n                  <wx-view>\n                    <wx-view id=\"82\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"80\" aria-label=\"识别结果:others;置信度:0.5463394\" />\n                    </wx-view>\n                    <wx-view[3] id=\"88\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"86\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[3]>\n                    <wx-view[5] id=\"93\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"92\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[5]>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"96\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">广州华美麓湖高尔夫球乡村俱乐部 ((前九),(后九))</wx-view>\n                      <wx-view[2] id=\"98\">小雨 33℃ 风力:4-5级 紫外线:弱</wx-view[2]>\n                      <wx-view[3] id=\"100\">2025-07-01  星期二  18:24</wx-view[3]>\n                      <wx-view[4] id=\"102\">比赛进行中</wx-view[4]>\n                    </wx-view>\n                  </wx-view[2]>\n                  <wx-view[3]>\n                    <wx-view>\n                      <wx-view id=\"108\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"107\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view>\n                      <wx-view[2] id=\"112\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"111\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[3]>\n                </wx-view[4]>\n                <wx-view[5]>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"118\" event=\"['tap']\">导出图片</wx-view>\n                      </wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"122\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"121\">攻略</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"128\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"124\">成绩和推杆</wx-view>\n                            <wx-image id=\"127\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          </wx-view[2]>\n                        </wx-view>\n                      </wx-view[2]>\n                      <wx-view[3]>\n                        <wx-view>\n                          <wx-image id=\"133\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        </wx-view>\n                        <wx-view[2] id=\"143\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"137\">ella</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"139\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[3]>\n                      <wx-view[4]>\n                        <wx-view>\n                          <wx-image id=\"147\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"157\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"151\">小白</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"153\">76</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[4]>\n                      <wx-view[5]>\n                        <wx-view>\n                          <wx-image id=\"161\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"171\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"165\">bob</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"167\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[5]>\n                      <wx-view[6]>\n                        <wx-view id=\"178\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"175\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          <wx-view id=\"177\">球手</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"182\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"181\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"185\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"184\">Stableford</wx-view>\n                        </wx-view[3]>\n                        <wx-view[4] id=\"188\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"187\">人数限制:4</wx-view>\n                        </wx-view[4]>\n                        <wx-view[5] id=\"191\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"190\">改球场</wx-view>\n                        </wx-view[5]>\n                      </wx-view[6]>\n                    </wx-view>\n                    <wx-scroll-view id=\"699\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n                      <div>\n                        <div id=\"695\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                          <div>\n                            <wx-view>\n                              <wx-view>\n                                <wx-view[2]>\n                                  <wx-view id=\"198\">(后九)</wx-view>\n                                </wx-view[2]>\n                              </wx-view>\n                              <wx-view[2]>\n                                <wx-view[12]>\n                                  <wx-view id=\"246\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"245\">11</wx-view>\n                                  </wx-view>\n                                </wx-view[12]>\n                                <wx-view[13]>\n                                  <wx-view id=\"250\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"249\">12</wx-view>\n                                  </wx-view>\n                                </wx-view[13]>\n                                <wx-view[14]>\n                                  <wx-view id=\"254\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"253\">13</wx-view>\n                                  </wx-view>\n                                </wx-view[14]>\n                                <wx-view[15]>\n                                  <wx-view id=\"258\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"257\">14</wx-view>\n                                  </wx-view>\n                                </wx-view[15]>\n                                <wx-view[16]>\n                                  <wx-view id=\"262\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"261\">15</wx-view>\n                                  </wx-view>\n                                </wx-view[16]>\n                                <wx-view[17]>\n                                  <wx-view id=\"266\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"265\">16</wx-view>\n                                  </wx-view>\n                                </wx-view[17]>\n                              </wx-view[2]>\n                              <wx-view[3]>\n                                <wx-view[12] id=\"324\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"323\">4</wx-view>\n                                </wx-view[12]>\n                                <wx-view[13] id=\"327\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"326\">3</wx-view>\n                                </wx-view[13]>\n                                <wx-view[14] id=\"330\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"329\">5</wx-view>\n                                </wx-view[14]>\n                                <wx-view[15] id=\"333\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"332\">4</wx-view>\n                                </wx-view[15]>\n                                <wx-view[16] id=\"336\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"335\">4</wx-view>\n                                </wx-view[16]>\n                                <wx-view[17] id=\"339\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"338\">3</wx-view>\n                                </wx-view[17]>\n                              </wx-view[3]>\n                              <wx-view[4]>\n                                <wx-view>\n                                  <wx-view[12] id=\"416\" event=\"['tap']\" />\n                                  <wx-view[13] id=\"421\" event=\"['tap']\" />\n                                  <wx-view[14] id=\"426\" event=\"['tap']\" />\n                                  <wx-view[15] id=\"432\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"429\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"438\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"435\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"444\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"441\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                </wx-view>\n                                <wx-view[2]>\n                                  <wx-view[12] id=\"527\" event=\"['tap']\" />\n                                  <wx-view[13] id=\"532\" event=\"['tap']\" />\n                                  <wx-view[14] id=\"537\" event=\"['tap']\" />\n                                  <wx-view[15] id=\"543\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"540\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"549\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"546\" listener=\"['touchend', 'touchcancel']\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"555\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"552\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                </wx-view[2]>\n                                <wx-view[3]>\n                                  <wx-view[12] id=\"638\" event=\"['tap']\" />\n                                  <wx-view[13] id=\"643\" event=\"['tap']\" />\n                                  <wx-view[14] id=\"648\" event=\"['tap']\" />\n                                  <wx-view[15] id=\"654\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"651\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"660\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"657\">+2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"666\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"663\">-2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                </wx-view[3]>\n                              </wx-view[4]>\n                            </wx-view>\n                          </div>\n                        </div>\n                      </div>\n                    </wx-scroll-view>\n                  </wx-view[2]>\n                </wx-view[5]>\n              </wx-view>\n            </div>\n          </div>\n        </div>\n      </wx-scroll-view>\n    </wx-view[3]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view/wx-view[14]\", \"xpath\": \"/wx-view[2]/wx-view/wx-view\", \"textContent\": \"\", \"pos\": {\"x\": 214, \"y\": 457}, \"center\": {\"x\": 214, \"y\": 457}, \"target\": {\"rect\": {\"x\": 194, \"y\": 444, \"left\": 194, \"top\": 444, \"width\": 34, \"height\": 32}, \"parentRect\": {\"x\": 189, \"y\": 437, \"left\": 189, \"top\": 437, \"width\": 44, \"height\": 44}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[3]/wx-scroll-view/div/div/div/wx-view/wx-view[5]/wx-view[2]/wx-scroll-view/div/div/div/wx-view/wx-view[4]/wx-view/wx-view[14]", "path_": "SubPackD/Match/Match.html", "xpath": "/wx-view[2]/wx-view/wx-view"}}, {"id": 42493, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5861111111111111, "y": 0.5040106951871658, "width": 0.18333333333333332, "height": 0.053475935828877004, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369257548.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[7] id=\"129\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"131\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"133\">+3</wx-view[9]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[5] id=\"264\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"266\">0</wx-view[6]>\n                                      <wx-view[7] id=\"268\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"399\">-3</wx-view[3]>\n                                      <wx-view[4] id=\"401\">-2</wx-view[4]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view/wx-view/wx-view[3]/wx-view[2]/wx-picker-view/div/wx-picker-view-column\", \"xpath\": \"/div/div[3]/wx-view[2]\", \"textContent\": \"清除-3-2-10+1+2+3+4+5+6+7+8+9+10+11+12+13+14+15+16+17+18+19+20+21+22+23+24+25+26+27+28+29+30+31+32+33\", \"pos\": {\"x\": 249, \"y\": 399}, \"center\": {\"x\": 249, \"y\": 399}, \"target\": {\"rect\": {\"x\": 211, \"y\": 377, \"left\": 211, \"top\": 377, \"width\": 66, \"height\": 40}, \"parentRect\": {\"x\": 211, \"y\": 368, \"left\": 211, \"top\": 368, \"width\": 66, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view/wx-view/wx-view[3]/wx-view[2]/wx-picker-view/div/wx-picker-view-column", "path_": "SubPackD/Match/Match.html", "xpath": "/div/div[3]/wx-view[2]"}}, {"id": 42494, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5861111111111111, "y": 0.589572192513369, "width": 0.18333333333333332, "height": 0.053475935828877004, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369257868.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[5] id=\"264\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"266\">0</wx-view[6]>\n                                      <wx-view[7] id=\"268\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"399\">-3</wx-view[3]>\n                                      <wx-view[4] id=\"401\">-2</wx-view[4]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view[2]/wx-picker-view/div/wx-picker-view-column\", \"xpath\": \"/div/div[3]/wx-view[2]\", \"textContent\": \"清除-3-2-10+1+2+3+4+5+6+7+8+9+10+11+12+13+14+15+16+17+18+19+20+21+22+23+24+25+26+27+28+29+30+31+32+33\", \"pos\": {\"x\": 252, \"y\": 464}, \"center\": {\"x\": 252, \"y\": 464}, \"target\": {\"rect\": {\"x\": 211, \"y\": 441, \"left\": 211, \"top\": 441, \"width\": 66, \"height\": 40}, \"parentRect\": {\"x\": 211, \"y\": 432, \"left\": 211, \"top\": 432, \"width\": 66, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view[2]/wx-picker-view/div/wx-picker-view-column", "path_": "SubPackD/Match/Match.html", "xpath": "/div/div[3]/wx-view[2]"}}, {"id": 42495, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5027777777777778, "y": 0.5975935828877005, "width": 0.08333333333333333, "height": 0.040106951871657755, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369258761.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\" listener=\"['touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"266\">0</wx-view[6]>\n                                      <wx-view[9] id=\"272\">+3</wx-view[9]>\n                                      <wx-view[10] id=\"274\">+4</wx-view[10]>\n                                      <wx-view[11] id=\"276\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"278\">+6</wx-view[12]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"399\">-3</wx-view[3]>\n                                      <wx-view[4] id=\"401\">-2</wx-view[4]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view\", \"xpath\": \"/wx-view\", \"textContent\": \"-\", \"pos\": {\"x\": 201, \"y\": 464}, \"center\": {\"x\": 201, \"y\": 464}, \"target\": {\"rect\": {\"x\": 181, \"y\": 447, \"left\": 181, \"top\": 447, \"width\": 30, \"height\": 30}, \"parentRect\": {\"x\": 181, \"y\": 432, \"left\": 181, \"top\": 432, \"width\": 30, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view", "path_": "SubPackD/Match/Match.html", "xpath": "/wx-view"}}, {"id": 42496, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5027777777777778, "y": 0.5975935828877005, "width": 0.08333333333333333, "height": 0.040106951871657755, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369259537.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[5] id=\"264\">-1</wx-view[5]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"272\">+3</wx-view[9]>\n                                      <wx-view[10] id=\"274\">+4</wx-view[10]>\n                                      <wx-view[11] id=\"276\">+5</wx-view[11]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"399\">-3</wx-view[3]>\n                                      <wx-view[4] id=\"401\">-2</wx-view[4]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view\", \"xpath\": \"/wx-view\", \"textContent\": \"-\", \"pos\": {\"x\": 196, \"y\": 473}, \"center\": {\"x\": 196, \"y\": 473}, \"target\": {\"rect\": {\"x\": 181, \"y\": 447, \"left\": 181, \"top\": 447, \"width\": 30, \"height\": 30}, \"parentRect\": {\"x\": 181, \"y\": 432, \"left\": 181, \"top\": 432, \"width\": 30, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view", "path_": "SubPackD/Match/Match.html", "xpath": "/wx-view"}}, {"id": 42497, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5027777777777778, "y": 0.5975935828877005, "width": 0.08333333333333333, "height": 0.040106951871657755, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369260361.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[4] id=\"262\">-2</wx-view[4]>\n                                      <wx-view[7] id=\"268\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"272\">+3</wx-view[9]>\n                                      <wx-view[10] id=\"274\">+4</wx-view[10]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"399\">-3</wx-view[3]>\n                                      <wx-view[4] id=\"401\">-2</wx-view[4]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view\", \"xpath\": \"/wx-view\", \"textContent\": \"-\", \"pos\": {\"x\": 200, \"y\": 464}, \"center\": {\"x\": 200, \"y\": 464}, \"target\": {\"rect\": {\"x\": 181, \"y\": 447, \"left\": 181, \"top\": 447, \"width\": 30, \"height\": 30}, \"parentRect\": {\"x\": 181, \"y\": 432, \"left\": 181, \"top\": 432, \"width\": 30, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[2]/wx-view/wx-view[3]/wx-view", "path_": "SubPackD/Match/Match.html", "xpath": "/wx-view"}}, {"id": 42498, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5027777777777778, "y": 0.6831550802139037, "width": 0.08333333333333333, "height": 0.040106951871657755, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369261216.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"260\">-3</wx-view[3]>\n                                      <wx-view[6] id=\"266\">0</wx-view[6]>\n                                      <wx-view[7] id=\"268\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"272\">+3</wx-view[9]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\" listener=\"['touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                      <wx-view[7] id=\"407\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"409\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"411\">+3</wx-view[9]>\n                                      <wx-view[10] id=\"413\">+4</wx-view[10]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[3]/wx-view/wx-view[3]/wx-view\", \"xpath\": \"/wx-view\", \"textContent\": \"-\", \"pos\": {\"x\": 199, \"y\": 529}, \"center\": {\"x\": 199, \"y\": 529}, \"target\": {\"rect\": {\"x\": 181, \"y\": 511, \"left\": 181, \"top\": 511, \"width\": 30, \"height\": 30}, \"parentRect\": {\"x\": 181, \"y\": 496, \"left\": 181, \"top\": 496, \"width\": 30, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[3]/wx-view/wx-view[3]/wx-view", "path_": "SubPackD/Match/Match.html", "xpath": "/wx-view"}}, {"id": 42499, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5027777777777778, "y": 0.6831550802139037, "width": 0.08333333333333333, "height": 0.040106951871657755, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369262098.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"260\">-3</wx-view[3]>\n                                      <wx-view[6] id=\"266\">0</wx-view[6]>\n                                      <wx-view[7] id=\"268\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"272\">+3</wx-view[9]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                      <wx-view[7] id=\"407\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"409\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"411\">+3</wx-view[9]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[3]/wx-view/wx-view[3]/wx-view\", \"xpath\": \"/wx-view\", \"textContent\": \"-\", \"pos\": {\"x\": 202, \"y\": 528}, \"center\": {\"x\": 202, \"y\": 528}, \"target\": {\"rect\": {\"x\": 181, \"y\": 511, \"left\": 181, \"top\": 511, \"width\": 30, \"height\": 30}, \"parentRect\": {\"x\": 181, \"y\": 496, \"left\": 181, \"top\": 496, \"width\": 30, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[3]/wx-view/wx-view[3]/wx-view", "path_": "SubPackD/Match/Match.html", "xpath": "/wx-view"}}, {"id": 42500, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.5027777777777778, "y": 0.6831550802139037, "width": 0.08333333333333333, "height": 0.040106951871657755, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369262853.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"260\">-3</wx-view[3]>\n                                      <wx-view[6] id=\"266\">0</wx-view[6]>\n                                      <wx-view[7] id=\"268\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"272\">+3</wx-view[9]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[4] id=\"401\">-2</wx-view[4]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                      <wx-view[7] id=\"407\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"409\">+2</wx-view[8]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": \"\"}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[3]/wx-view/wx-view[3]/wx-view\", \"xpath\": \"/wx-view\", \"textContent\": \"-\", \"pos\": {\"x\": 203, \"y\": 531}, \"center\": {\"x\": 203, \"y\": 531}, \"target\": {\"rect\": {\"x\": 181, \"y\": 511, \"left\": 181, \"top\": 511, \"width\": 30, \"height\": 30}, \"parentRect\": {\"x\": 181, \"y\": 496, \"left\": 181, \"top\": 496, \"width\": 30, \"height\": 57}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-view[4]/wx-view/wx-scroll-view/div/div/div/wx-view[3]/wx-view/wx-view[3]/wx-view", "path_": "SubPackD/Match/Match.html", "xpath": "/wx-view"}}, {"id": 42501, "isDeleted": 0, "type": "click", "markConfig": {"uuid": "", "x": 0.019444444444444445, "y": 0.9371657754010695, "width": 0.9611111111111111, "height": 0.05748663101604278, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369263318.jpg", "domXML": "<html>\n  <body id=\"1254\" event=\"['touchstart', 'touchmove']\">\n    <wx-view[2] id=\"536\" event=\"['transitionend', 'touchmove']\">\n      <wx-view>\n        <wx-view>\n          <wx-icon id=\"3\" event=\"['tap']\" />\n        </wx-view>\n        <wx-view[2]>\n          <wx-view id=\"6\" event=\"['tap']\">Tee</wx-view>\n          <wx-view[2] id=\"8\" event=\"['tap']\">多人</wx-view[2]>\n          <wx-view[3] id=\"10\" event=\"['tap']\">技术分</wx-view[3]>\n          <wx-view[4] id=\"12\" event=\"['tap']\">天气</wx-view[4]>\n        </wx-view[2]>\n      </wx-view>\n      <wx-view[2]>\n        <wx-view>\n          <wx-view id=\"18\" event=\"['tap']\">\n            <wx-image id=\"17\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view>\n          <wx-view[2]>\n            <wx-swiper id=\"96\" event=\"['wxPositioningTargetReady', 'change']\" listener=\"['wheel']\">\n              <div>\n                <div>\n                  <div>\n                    <wx-view>\n                      <wx-swiper-item[13] id=\"57\" event=\"['wxPositioningTargetReady']\">\n                        <wx-view id=\"56\">Hole 13 / Par 5</wx-view>\n                      </wx-swiper-item[13]>\n                    </wx-view>\n                  </div>\n                </div>\n              </div>\n            </wx-swiper>\n          </wx-view[2]>\n          <wx-view[3] id=\"101\" event=\"['tap']\">\n            <wx-image id=\"100\" aria-label=\"识别结果:未知;置信度:0\" />\n          </wx-view[3]>\n        </wx-view>\n        <wx-view[4]>\n          <wx-view>\n            <wx-scroll-view id=\"527\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n              <div>\n                <div id=\"523\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                  <div>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"106\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"109\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        <wx-view[2] id=\"111\">ella</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"114\" event=\"['touchstart']\">\n                            <wx-view id=\"113\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"233\" event=\"['tap']\">\n                            <wx-picker-view id=\"232\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"230\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[6] id=\"127\">0</wx-view[6]>\n                                      <wx-view[11] id=\"137\">+5</wx-view[11]>\n                                      <wx-view[12] id=\"139\">+6</wx-view[12]>\n                                      <wx-view[13] id=\"141\">+7</wx-view[13]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"236\" event=\"['touchstart']\">\n                            <wx-view id=\"235\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"240\" id=\"0\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view>\n                    <wx-view[2]>\n                      <wx-view>\n                        <wx-view id=\"245\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"248\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"250\">小白</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"253\" event=\"['touchstart']\">\n                            <wx-view id=\"252\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"372\" event=\"['tap']\">\n                            <wx-picker-view id=\"371\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"369\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"260\">-3</wx-view[3]>\n                                      <wx-view[6] id=\"266\">0</wx-view[6]>\n                                      <wx-view[7] id=\"268\">+1</wx-view[7]>\n                                      <wx-view[8] id=\"270\">+2</wx-view[8]>\n                                      <wx-view[9] id=\"272\">+3</wx-view[9]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"375\" event=\"['touchstart']\">\n                            <wx-view id=\"374\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"379\" id=\"1\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view>\n                        <wx-view id=\"384\" event=\"['tap']\">TEE</wx-view>\n                        <wx-image id=\"387\" aria-label=\"识别结果:未知;置信度:0\" />\n                        <wx-view[2] id=\"389\">bob</wx-view[2]>\n                        <wx-view[3]>\n                          <wx-view id=\"392\" event=\"['touchstart']\">\n                            <wx-view id=\"391\" listener=\"['touchend', 'touchcancel', 'touchend', 'touchcancel', 'touchend', 'touchcancel']\">-</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"511\" event=\"['tap']\">\n                            <wx-picker-view id=\"510\" event=\"['wxPickerColumnValueChanged', 'wxPickerColumnValueChangeStart', 'wxPickerColumnValueChangeEnd', 'change', 'pickstart', 'pickend']\">\n                              <div>\n                                <wx-picker-view-column id=\"508\" event=\"['tap']\">\n                                  <div>\n                                    <div[3]>\n                                      <wx-view[3] id=\"399\">-3</wx-view[3]>\n                                      <wx-view[4] id=\"401\">-2</wx-view[4]>\n                                      <wx-view[5] id=\"403\">-1</wx-view[5]>\n                                      <wx-view[6] id=\"405\">0</wx-view[6]>\n                                      <wx-view[7] id=\"407\">+1</wx-view[7]>\n                                    </div[3]>\n                                  </div>\n                                </wx-picker-view-column>\n                              </div>\n                            </wx-picker-view>\n                          </wx-view[2]>\n                          <wx-view[3] id=\"514\" event=\"['touchstart']\">\n                            <wx-view id=\"513\">+</wx-view>\n                          </wx-view[3]>\n                        </wx-view[3]>\n                        <wx-view[4]>\n                          <wx-image id=\"518\" id=\"2\" aria-label=\"识别结果:others;置信度:0.5323778\" event=\"['tap']\" />\n                        </wx-view[4]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </div>\n                </div>\n              </div>\n            </wx-scroll-view>\n          </wx-view>\n        </wx-view[4]>\n        <wx-form id=\"534\" event=\"['formSubmit', 'formReset', 'formSubmitToGroup', 'submit']\">\n          <span>\n            <wx-view>\n              <wx-button id=\"531\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\" listener=\"['touchend', 'touchcancel']\">确认</wx-button>\n            </wx-view>\n          </span>\n        </wx-form>\n      </wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-view id=\"549\" event=\"['tap']\">\n        <wx-image id=\"548\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"553\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"552\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"556\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view[3]>\n    <wx-view[4] id=\"564\" event=\"['tap']\">\n      <wx-view[2] id=\"563\">球道</wx-view[2]>\n    </wx-view[4]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": null}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"tap\", \"parent\": \"html/body/wx-view[2]/wx-view[2]/wx-form/span/wx-view/wx-button\", \"xpath\": \"\", \"textContent\": \"确认\", \"pos\": {\"x\": 172, \"y\": 723}, \"center\": {\"x\": 172, \"y\": 723}, \"target\": {\"rect\": {\"x\": 7, \"y\": 701, \"left\": 7, \"top\": 701, \"width\": 346, \"height\": 43}, \"parentRect\": {\"x\": 7, \"y\": 701, \"left\": 7, \"top\": 701, \"width\": 346, \"height\": 43}}, \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "html/body/wx-view[2]/wx-view[2]/wx-form/span/wx-view/wx-button", "path_": "SubPackD/Match/Match.html", "xpath": ""}}, {"id": 42502, "isDeleted": 0, "type": "finish", "markConfig": {"uuid": "", "x": 0, "y": 0, "width": 0, "height": 0, "name": "", "color": ""}, "url": "https://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/img/annotation/3191454962_1751369263782.jpg", "domXML": "<html>\n  <body id=\"727\" event=\"['touchstart', 'touchmove']\">\n    <wx-view>\n      <wx-view id=\"12\" event=\"['tap']\">\n        <wx-image id=\"11\" aria-label=\"识别结果:others;置信度:0.5188657\" />\n      </wx-view>\n      <wx-view[2]>\n        <wx-button id=\"16\" aria-disabled=\"false\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n          <wx-image id=\"15\" aria-label=\"识别结果:others;置信度:0.6141933\" />\n        </wx-button>\n        <wx-image id=\"19\" aria-label=\"识别结果:others;置信度:0.5866187\" event=\"['tap']\" />\n      </wx-view[2]>\n    </wx-view>\n    <wx-view[2] id=\"27\" event=\"['tap']\">\n      <wx-view[2] id=\"26\">球道</wx-view[2]>\n    </wx-view[2]>\n    <wx-view[3]>\n      <wx-scroll-view id=\"712\" event=\"['wxPositioningTargetReady', 'scrolltolower', 'touchstart']\" listener=\"['scroll']\">\n        <div>\n          <div id=\"708\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n            <div>\n              <wx-view>\n                <wx-view[2]>\n                  <wx-view>\n                    <wx-view id=\"45\" event=\"['tap']\">\n                      <wx-view>\n                        <wx-view id=\"32\" event=\"['tap']\">\n                          <wx-image id=\"31\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2]>\n                          <wx-view id=\"38\" event=\"['tap']\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"43\" event=\"['longpress', 'tap']\">\n                          <wx-image id=\"42\" aria-label=\"识别结果:others;置信度:0.5111935\" />\n                        </wx-view[3]>\n                      </wx-view>\n                    </wx-view>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"49\" event=\"['tap']\">记分组</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"52\" event=\"['tap']\">照片墙</wx-view[2]>\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[2]>\n                </wx-view[2]>\n                <wx-view[3]>\n                  <wx-view>\n                    <wx-view[2]>\n                      <wx-view id=\"65\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"61\" aria-label=\"识别结果:others;置信度:0.54767895\" />\n                        </wx-view>\n                        <wx-view[2] id=\"64\">人工算分</wx-view[2]>\n                      </wx-view>\n                    </wx-view[2]>\n                    <wx-view[3]>\n                      <wx-view id=\"73\" event=\"['touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-view>\n                          <wx-image id=\"69\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"72\">PK规则</wx-view[2]>\n                      </wx-view>\n                    </wx-view[3]>\n                  </wx-view>\n                </wx-view[3]>\n                <wx-view[4]>\n                  <wx-view>\n                    <wx-view id=\"82\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"80\" aria-label=\"识别结果:others;置信度:0.5463394\" />\n                    </wx-view>\n                    <wx-view[3] id=\"88\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"86\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[3]>\n                    <wx-view[5] id=\"93\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                      <wx-image id=\"92\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                    </wx-view[5]>\n                  </wx-view>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view id=\"96\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">广州华美麓湖高尔夫球乡村俱乐部 ((前九),(后九))</wx-view>\n                      <wx-view[2] id=\"98\">小雨 33℃ 风力:4-5级 紫外线:弱</wx-view[2]>\n                      <wx-view[3] id=\"100\">2025-07-01  星期二  18:24</wx-view[3]>\n                      <wx-view[4] id=\"102\">比赛进行中</wx-view[4]>\n                    </wx-view>\n                  </wx-view[2]>\n                  <wx-view[3]>\n                    <wx-view>\n                      <wx-view id=\"108\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"107\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view>\n                      <wx-view[2] id=\"112\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                        <wx-image id=\"111\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                      </wx-view[2]>\n                    </wx-view>\n                  </wx-view[3]>\n                </wx-view[4]>\n                <wx-view[5]>\n                  <wx-view[2]>\n                    <wx-view>\n                      <wx-view>\n                        <wx-view id=\"118\" event=\"['tap']\">导出图片</wx-view>\n                      </wx-view>\n                      <wx-view[2]>\n                        <wx-view>\n                          <wx-view id=\"122\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"121\">攻略</wx-view>\n                          </wx-view>\n                          <wx-view[2] id=\"128\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                            <wx-view id=\"124\">成绩和推杆</wx-view>\n                            <wx-image id=\"127\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          </wx-view[2]>\n                        </wx-view>\n                      </wx-view[2]>\n                      <wx-view[3]>\n                        <wx-view>\n                          <wx-image id=\"133\" aria-label=\"识别结果:close;置信度:0.5202086\" />\n                        </wx-view>\n                        <wx-view[2] id=\"143\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"137\">ella</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"139\">68</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[3]>\n                      <wx-view[4]>\n                        <wx-view>\n                          <wx-image id=\"147\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"157\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"151\">小白</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"153\">73</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[4]>\n                      <wx-view[5]>\n                        <wx-view>\n                          <wx-image id=\"161\" aria-label=\"识别结果:未知;置信度:0\" />\n                        </wx-view>\n                        <wx-view[2] id=\"171\" event=\"['tap']\">\n                          <wx-view[2]>\n                            <wx-view id=\"165\">bob</wx-view>\n                            <wx-view[2]>\n                              <wx-view id=\"167\">65</wx-view>\n                            </wx-view[2]>\n                          </wx-view[2]>\n                        </wx-view[2]>\n                      </wx-view[5]>\n                      <wx-view[6]>\n                        <wx-view id=\"178\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"175\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                          <wx-view id=\"177\">球手</wx-view>\n                        </wx-view>\n                        <wx-view[2] id=\"182\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-image id=\"181\" aria-label=\"识别结果:others;置信度:0.5145607\" />\n                        </wx-view[2]>\n                        <wx-view[3] id=\"185\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"184\">Stableford</wx-view>\n                        </wx-view[3]>\n                        <wx-view[4] id=\"188\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"187\">人数限制:4</wx-view>\n                        </wx-view[4]>\n                        <wx-view[5] id=\"191\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                          <wx-view id=\"190\">改球场</wx-view>\n                        </wx-view[5]>\n                      </wx-view[6]>\n                    </wx-view>\n                    <wx-scroll-view id=\"702\" event=\"['wxPositioningTargetReady', 'touchstart']\" listener=\"['scroll']\">\n                      <div>\n                        <div id=\"698\" event=\"['scrollend', 'touchstart', 'touchmove', 'touchend', 'touchcancel']\" listener=\"['touchstart', 'touchmove', 'touchend', 'scroll']\">\n                          <div>\n                            <wx-view>\n                              <wx-view>\n                                <wx-view[2]>\n                                  <wx-view id=\"198\">(后九)</wx-view>\n                                </wx-view[2]>\n                              </wx-view>\n                              <wx-view[2]>\n                                <wx-view[12]>\n                                  <wx-view id=\"246\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"245\">11</wx-view>\n                                  </wx-view>\n                                </wx-view[12]>\n                                <wx-view[13]>\n                                  <wx-view id=\"250\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"249\">12</wx-view>\n                                  </wx-view>\n                                </wx-view[13]>\n                                <wx-view[14]>\n                                  <wx-view id=\"254\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"253\">13</wx-view>\n                                  </wx-view>\n                                </wx-view[14]>\n                                <wx-view[15]>\n                                  <wx-view id=\"258\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"257\">14</wx-view>\n                                  </wx-view>\n                                </wx-view[15]>\n                                <wx-view[16]>\n                                  <wx-view id=\"262\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"261\">15</wx-view>\n                                  </wx-view>\n                                </wx-view[16]>\n                                <wx-view[17]>\n                                  <wx-view id=\"266\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                    <wx-view id=\"265\">16</wx-view>\n                                  </wx-view>\n                                </wx-view[17]>\n                              </wx-view[2]>\n                              <wx-view[3]>\n                                <wx-view[12] id=\"324\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"323\">4</wx-view>\n                                </wx-view[12]>\n                                <wx-view[13] id=\"327\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"326\">3</wx-view>\n                                </wx-view[13]>\n                                <wx-view[14] id=\"330\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"329\">5</wx-view>\n                                </wx-view[14]>\n                                <wx-view[15] id=\"333\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"332\">4</wx-view>\n                                </wx-view[15]>\n                                <wx-view[16] id=\"336\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"335\">4</wx-view>\n                                </wx-view[16]>\n                                <wx-view[17] id=\"339\" event=\"['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']\">\n                                  <wx-view id=\"338\">3</wx-view>\n                                </wx-view[17]>\n                              </wx-view[3]>\n                              <wx-view[4]>\n                                <wx-view>\n                                  <wx-view[12] id=\"416\" event=\"['tap']\" />\n                                  <wx-view[13] id=\"421\" event=\"['tap']\" />\n                                  <wx-view[14] id=\"427\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"424\">0</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[14]>\n                                  <wx-view[15] id=\"433\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"430\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"439\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"436\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"445\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"442\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                </wx-view>\n                                <wx-view[2]>\n                                  <wx-view[12] id=\"528\" event=\"['tap']\" />\n                                  <wx-view[13] id=\"533\" event=\"['tap']\" />\n                                  <wx-view[14] id=\"539\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"536\">-3</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[14]>\n                                  <wx-view[15] id=\"545\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"542\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"551\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"548\" listener=\"['touchend', 'touchcancel']\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"557\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"554\">+1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                </wx-view[2]>\n                                <wx-view[3]>\n                                  <wx-view[12] id=\"640\" event=\"['tap']\" />\n                                  <wx-view[13] id=\"645\" event=\"['tap']\" />\n                                  <wx-view[14] id=\"651\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"648\">-3</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[14]>\n                                  <wx-view[15] id=\"657\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"654\">-1</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[15]>\n                                  <wx-view[16] id=\"663\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"660\">+2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[16]>\n                                  <wx-view[17] id=\"669\" event=\"['tap']\">\n                                    <wx-view[2]>\n                                      <wx-view>\n                                        <wx-view id=\"666\">-2</wx-view>\n                                      </wx-view>\n                                    </wx-view[2]>\n                                  </wx-view[17]>\n                                </wx-view[3]>\n                              </wx-view[4]>\n                            </wx-view>\n                          </div>\n                        </div>\n                      </div>\n                    </wx-scroll-view>\n                  </wx-view[2]>\n                </wx-view[5]>\n              </wx-view>\n            </div>\n          </div>\n        </div>\n      </wx-scroll-view>\n    </wx-view[3]>\n  </body>\n</html>\n", "allElementsRects": "{\"elementsrects_\": null, \"nativeelementsrects_\": null}", "screenWidth": 360, "screenHeight": 748, "extraInfo": {"click_html": "", "clickitem_obj_": "{\"event\": \"finish\", \"webviewOffsetY\": 0}", "nativeinfo_": "<native></native>", "parent": "", "path_": "SubPackD/Match/Match.html", "xpath": ""}}], "isEval": 0, "source": 1}]