# 多Session CGI Label调度器使用指南

## 概述

优化版调度器现在支持同时处理多个`session_cgi_label`，可以大大提高数据处理效率。通过并发执行多个标签的任务，您可以充分利用系统资源，减少总体处理时间。

## 主要功能

### 1. 多标签支持
- 支持同时处理多个`session_cgi_label`
- 用逗号分隔多个标签：`cgi7,cgi8,cgi9`
- 每个标签独立生成任务ID和日志文件

### 2. 执行模式
- **异步模式**：并发执行所有标签的任务，充分利用系统资源

### 3. 任务管理
- 智能并发控制，避免系统过载
- 任务状态跟踪和统计
- 自动清理临时文件和孤儿进程

## 使用方法

### 基本语法
```bash
python scheduler_day_allapp_optimized.py --session_cgi_label=标签1,标签2,标签3 [其他参数]
```

### 常用参数
- `--session_cgi_label`: 会话标签，支持多个（用逗号分隔）
- `--run_once`: 只运行一次，不进入定时循环
- `--max_concurrent`: 最大并发任务数（默认3）
- `--use_multiprocess`: 启用多进程加速
- `--max_workers`: 最大进程数
- `--chunk_size`: 每批处理的行数
- `--status`: 显示当前任务状态

## 使用示例

### 1. 单次运行多个标签
```bash
# 并发执行3个标签的任务
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9 \
    --run_once \
    --use_multiprocess \
    --max_workers=4 \
    --max_concurrent=5
```

### 2. 定时任务模式
```bash
# 在指定时间范围内定时执行多个标签
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9,cgi10 \
    --start_time=00:00 \
    --end_time=00:30 \
    --use_multiprocess \
    --max_workers=8 \
    --max_concurrent=6
```

### 3. 查看任务状态
```bash
# 显示所有标签的任务状态
python scheduler_day_allapp_optimized.py \
    --session_cgi_label=cgi7,cgi8,cgi9 \
    --status
```

## 输出示例

### 任务启动输出
```
优化版调度器启动
配置参数:
  会话标签: ['cgi7', 'cgi8', 'cgi9']
  多进程模式: True
  最大进程数: 4
  批次大小: 1000
  最大并发任务数: 5
  同步模式: False
  系统CPU核心数: 8

单次运行模式
正在启动标签: cgi7
[2024-07-30 10:00:01] 任务 cgi7_20240729 已启动（异步执行）
[2024-07-30 10:00:01] 日志文件: logs/cgi7_20240729.log
正在启动标签: cgi8
[2024-07-30 10:00:02] 任务 cgi8_20240729 已启动（异步执行）
[2024-07-30 10:00:02] 日志文件: logs/cgi8_20240729.log
正在启动标签: cgi9
[2024-07-30 10:00:03] 任务 cgi9_20240729 已启动（异步执行）
[2024-07-30 10:00:03] 日志文件: logs/cgi9_20240729.log
总共启动了 3 个任务: cgi7_20240729, cgi8_20240729, cgi9_20240729
```

### 任务状态输出
```
=== 任务状态摘要 ===

标签: cgi7
  正在运行: 1 个任务
    任务ID: cgi7_20240729
  已完成: 5 个
  失败: 0 个
  已取消: 0 个

标签: cgi8
  正在运行: 1 个任务
    任务ID: cgi8_20240729
  已完成: 3 个
  失败: 1 个
  已取消: 0 个

标签: cgi9
  正在运行: 0 个任务
  已完成: 2 个
  失败: 0 个
  已取消: 0 个

总并发限制: 5
当前总运行任务数: 2
==============================
```

## 日志文件

每个标签的任务都会生成独立的日志文件：
- 位置：`logs/` 目录
- 命名格式：`{session_cgi_label}_{YYYYMMDD}.log`
- 示例：`logs/cgi7_20240729.log`

## 性能优化建议

### 1. 并发设置
- `max_concurrent`: 根据系统资源设置，建议不超过CPU核心数
- `max_workers`: 每个任务的进程数，建议2-8个

### 2. 批处理设置
- `chunk_size`: 根据内存大小调整，建议500-2000

### 3. 系统资源监控
```bash
# 监控CPU和内存使用
htop

# 监控磁盘I/O
iotop

# 查看任务进程
ps aux | grep scheduler
```

## 故障排除

### 1. 任务无法启动
- 检查是否达到最大并发限制
- 确认没有同名任务正在运行
- 查看调度器日志：`scheduler.log`

### 2. 任务执行失败
- 查看具体任务的日志文件
- 检查磁盘空间是否充足
- 确认目标目录权限正确

### 3. 性能问题
- 适当调整并发参数
- 监控系统资源使用情况
- 考虑分批执行标签

## 高级用法

### 1. 自定义脚本集成
```python
from scheduler_day_allapp_optimized import scheduler, run_task_optimized

# 启动多个标签的任务
labels = ['cgi7', 'cgi8', 'cgi9']
for label in labels:
    task_id = run_task_optimized(label, use_multiprocess=True, max_workers=4)
    if task_id:
        print(f"任务 {task_id} 已启动")
```

### 2. 状态监控脚本
```python
from scheduler_day_allapp_optimized import scheduler

# 获取任务状态
labels = ['cgi7', 'cgi8', 'cgi9']
summary = scheduler.get_task_summary(labels)
running_tasks = scheduler.get_running_tasks_by_labels(labels)

# 处理状态信息
for label in labels:
    print(f"{label}: {summary[label]['running']} 运行中, {summary[label]['completed']} 已完成")
```

## 注意事项

1. **资源管理**：合理设置并发数，避免系统过载
2. **磁盘空间**：确保有足够空间存储生成的文件和日志
3. **网络连接**：确保到目标存储路径的网络连接稳定
4. **权限设置**：确保对目标目录有写入权限
5. **监控告警**：建议设置监控告警，及时发现异常情况

## 示例脚本

运行提供的示例脚本来了解更多用法：
```bash
# 查看功能说明
python multi_session_example.py features

# 查看使用示例
python multi_session_example.py examples

# 运行演示
python multi_session_example.py run
```
