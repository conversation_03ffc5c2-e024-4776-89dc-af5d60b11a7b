#!/usr/bin/env python3
"""
批量生成注释任务的脚本
基于提供的curl命令转换为requests实现
"""

import requests
import json
import time
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_requests.log'),
        logging.StreamHandler()
    ]
)

class AnnotationBatchProcessor:
    def __init__(self, dry_run: bool = False):
        self.base_url = "http://mmfinderdrannotationsvr.polaris:8080/annotations/tasks"
        self.headers = {
            'Content-Type': 'application/json'
        }
        # 固定的app信息
        # self.app_id = "wx3dcca19d0aa51755"
        # self.app_name = "星巴克"
        self.app_id = "wx6c03ed6dfa30c735"
        self.app_name = "golflive"
        self.dry_run = dry_run
    
    def create_request_payload(self, query: str) -> Dict[str, Any]:
        """创建请求负载"""
        return {
            "app_id": self.app_id,
            "app_name": self.app_name,
            "query": query
        }
    
    def send_single_request(self, query: str) -> Dict[str, Any]:
        """发送单个请求，支持 dry_run"""
        payload = self.create_request_payload(query)
        
        if self.dry_run:
            logging.info(f"[DRY RUN] 模拟发送请求: {query}")
            return {
                'query': query,
                'status_code': 200,
                'response': {'dry_run': True, 'payload': payload},
                'success': True
            }
        try:
            logging.info(f"发送请求: {query}")
            response = requests.post(
                self.base_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            result = {
                'query': query,
                'status_code': response.status_code,
                'response': response.json() if response.content else {},
                'success': True
            }
            
            logging.info(f"请求成功: {query} - 状态码: {response.status_code}")
            return result
            
        except requests.exceptions.RequestException as e:
            error_result = {
                'query': query,
                'status_code': getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                'error': str(e),
                'success': False
            }
            logging.error(f"请求失败: {query} - 错误: {str(e)}")
            return error_result
    
    def batch_process(self, queries: List[str], delay: float = 1.0) -> List[Dict[str, Any]]:
        """批量处理查询列表，支持原始格式（带次数）或已展开格式"""
        # 自动展开逻辑
        expanded_queries = []
        for line in queries:
            line = line.strip()
            if not line:
                continue
            # 尝试用分号分割，取最后一个为次数
            if ';' in line:
                parts = line.rsplit(';', 1)
            else:
                parts = line.rsplit(None, 1)
            if len(parts) == 2:
                query, num_str = parts
                try:
                    num = int(num_str)
                except ValueError:
                    num = 1
                expanded_queries.extend([query.strip()] * num)
            else:
                expanded_queries.append(line)

        results = []
        total_queries = len(expanded_queries)
        logging.info(f"开始批量处理 {total_queries} 个查询")
        for i, query in enumerate(expanded_queries, 1):
            logging.info(f"处理进度: {i}/{total_queries}")
            result = self.send_single_request(query.strip())
            results.append(result)
            # 添加延迟避免请求过于频繁
            if i < total_queries and delay > 0 and not self.dry_run:
                time.sleep(delay)
        # 统计结果
        successful = sum(1 for r in results if r['success'])
        failed = total_queries - successful
        logging.info(f"批量处理完成: 成功 {successful}, 失败 {failed}")
        return results
    
    def save_results(self, results: List[Dict[str, Any]], filename: str = "batch_results.json"):
        """保存结果到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logging.info(f"结果已保存到: {filename}")
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")


def main():
    """主函数示例"""
    # dry_run 控制开关，True 为演练模式，不实际发送请求
    dry_run = False  # 如需 dry_run，改为 True
    # 示例查询列表
    sample_queries = [str(line) for line in """我是bob， 我在第4洞打了-1
我是ella， 我在第4洞打了0 
我是ella， 我在第6洞打了0 
把ella 的第 10 洞改成-2
我是ella， 小白在第 8 洞打了老鹰 
我是小白， 帮我把第17洞改成0   
我是ella， 帮我把第7洞改成-1 
我是 bob， 帮我把第1洞改成+2
帮小白把第9洞改成-1 
把 bob第14洞改成+2
我是小白，ella在第4洞打了+2
我是ella，bob在第4洞打了-1
我是bob，我在第6洞打了保帕
我是bob，ella在第5洞打了+1
我是小白， bob在第7洞打了-2
我是小白，ella在第8洞打了柏忌
我是ella，小白在第9洞打了-1
我是ella， 我在第10洞打了+2    
我是小白，ella在第11洞打了+2
我是bob，小白在第14洞打了0""".split("\n")]

    # 创建处理器
    processor = AnnotationBatchProcessor(dry_run=dry_run)

    # 批量处理（直接传原始格式）
    results = processor.batch_process(sample_queries, delay=0.3)

    # 保存结果
    processor.save_results(results)

    # 打印摘要
    successful = sum(1 for r in results if r['success'])
    print(f"\n处理完成:")
    print(f"总数: {len(results)}")
    print(f"成功: {successful}")
    print(f"失败: {len(results) - successful}")


if __name__ == "__main__":
    main()
