import csv
import json

input_file = 'points.csv'
output_file = 'points_converted_ver2.csv'

# The relevant columns
question_col = '课题名称'
fields = ['青云课题背景', '青云课题价值', '青云课题挑战', 'BG', '部门', '中心']

with open(input_file, newline='', encoding='utf-8-sig') as infile, open(output_file, 'w', newline='', encoding='utf-8') as outfile:
    reader = csv.DictReader(infile)
    writer = csv.writer(outfile)
    writer.writerow(['问题', '答案'])
    for row in reader:
        question = row[question_col].strip()
        print(row.keys())
        answer_dict = {field: row[field].strip() for field in fields}
        answer_json = json.dumps(answer_dict, ensure_ascii=False)
        writer.writerow([question, answer_json]) 