#!/usr/bin/env python3
"""
测试new_process_dataframe多进程实现的脚本
"""

import pandas as pd
import time
import sys
import os

# 添加当前目录到Python路径，以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xml_decode_b_understand_all_app import new_process_dataframe, process_row

def create_test_data(num_rows=10000):
    """创建测试数据"""
    print(f"创建 {num_rows} 行测试数据...")
    
    data = []
    for i in range(num_rows):
        # 创建一些有native_info的行和一些没有的行
        if i % 3 == 0:  # 1/3的行有native_info且clickitem_为空
            row = {
                'appid_': f'app_{i}',
                'sessionid_': f'session_{i}',
                'clickitem_': '',  # 空字符串
                'length_': 100 + i,
                'nativeinfo_': f'<native><tab-bar id="tab_{i}">content_{i}</tab-bar></native>',
                'decoded': f'<div>decoded_content_{i}</div>',
                'nickname_': f'user_{i}',
                'signature_': f'sig_{i}',
                'categories_': f'cat_{i}',
                'path_': f'/path/{i}',
                'scene_': f'scene_{i}'
            }
        else:  # 2/3的行没有native_info或有clickitem_
            row = {
                'appid_': f'app_{i}',
                'sessionid_': f'session_{i}',
                'clickitem_': f'click_{i}' if i % 2 == 0 else '',
                'length_': 100 + i,
                'nativeinfo_': f'<native>info_{i}</native>' if i % 4 == 0 else '',
                'decoded': f'<div>decoded_content_{i}</div>',
                'nickname_': f'user_{i}',
                'signature_': f'sig_{i}',
                'categories_': f'cat_{i}',
                'path_': f'/path/{i}',
                'scene_': f'scene_{i}'
            }
        data.append(row)
    
    return pd.DataFrame(data)

def test_single_process(df):
    """测试单进程处理"""
    print("\n=== 测试单进程处理 ===")
    df_copy = df.copy()
    
    start_time = time.time()
    result_single = new_process_dataframe(df_copy, use_multiprocess=False)
    single_time = time.time() - start_time
    
    print(f"单进程处理时间: {single_time:.2f} 秒")
    print(f"处理速度: {len(df) / single_time:.2f} 行/秒")
    print(f"结果数据形状: {result_single.shape}")
    
    return result_single, single_time

def test_multiprocess(df, max_workers=None, chunk_size=None):
    """测试多进程处理"""
    print(f"\n=== 测试多进程处理 (workers={max_workers}, chunk_size={chunk_size}) ===")
    df_copy = df.copy()
    
    start_time = time.time()
    result_multi = new_process_dataframe(df_copy, use_multiprocess=True, 
                                       max_workers=max_workers, chunk_size=chunk_size)
    multi_time = time.time() - start_time
    
    print(f"多进程处理时间: {multi_time:.2f} 秒")
    print(f"处理速度: {len(df) / multi_time:.2f} 行/秒")
    print(f"结果数据形状: {result_multi.shape}")
    
    return result_multi, multi_time

def compare_results(result1, result2):
    """比较两个结果是否一致"""
    print("\n=== 结果比较 ===")
    
    # 检查形状
    if result1.shape != result2.shape:
        print(f"❌ 形状不一致: {result1.shape} vs {result2.shape}")
        return False
    
    # 检查列名
    if not result1.columns.equals(result2.columns):
        print(f"❌ 列名不一致: {result1.columns.tolist()} vs {result2.columns.tolist()}")
        return False
    
    # 重新排序以确保比较的准确性（因为多进程可能改变顺序）
    result1_sorted = result1.sort_values('appid_').reset_index(drop=True)
    result2_sorted = result2.sort_values('appid_').reset_index(drop=True)
    
    # 检查关键列的内容
    key_columns = ['decoded', 'clickitem_']
    for col in key_columns:
        if not result1_sorted[col].equals(result2_sorted[col]):
            print(f"❌ 列 '{col}' 内容不一致")
            # 显示前几个不同的值
            diff_mask = result1_sorted[col] != result2_sorted[col]
            if diff_mask.any():
                print("前几个不同的值:")
                diff_indices = diff_mask[diff_mask].index[:5]
                for idx in diff_indices:
                    print(f"  索引 {idx}: '{result1_sorted.loc[idx, col]}' vs '{result2_sorted.loc[idx, col]}'")
            return False
    
    print("✅ 结果一致!")
    return True

def main():
    """主测试函数"""
    print("开始测试new_process_dataframe多进程实现")
    
    # 创建测试数据
    test_sizes = [1000, 5000, 10000]
    
    for size in test_sizes:
        print(f"\n{'='*50}")
        print(f"测试数据量: {size} 行")
        print(f"{'='*50}")
        
        df = create_test_data(size)
        
        # 测试单进程
        result_single, time_single = test_single_process(df)
        
        # 测试多进程（默认参数）
        result_multi_default, time_multi_default = test_multiprocess(df)
        
        # 测试多进程（自定义参数）
        result_multi_custom, time_multi_custom = test_multiprocess(df, max_workers=4, chunk_size=500)
        
        # 比较结果
        print(f"\n--- 单进程 vs 多进程(默认) ---")
        is_consistent_default = compare_results(result_single, result_multi_default)
        
        print(f"\n--- 单进程 vs 多进程(自定义) ---")
        is_consistent_custom = compare_results(result_single, result_multi_custom)
        
        # 性能比较
        print(f"\n--- 性能比较 ---")
        print(f"单进程时间: {time_single:.2f}s")
        print(f"多进程(默认)时间: {time_multi_default:.2f}s")
        print(f"多进程(自定义)时间: {time_multi_custom:.2f}s")
        
        if time_single > 0:
            speedup_default = time_single / time_multi_default
            speedup_custom = time_single / time_multi_custom
            print(f"多进程(默认)加速比: {speedup_default:.2f}x")
            print(f"多进程(自定义)加速比: {speedup_custom:.2f}x")
        
        # 检查一些具体的转换结果
        print(f"\n--- 转换结果检查 ---")
        # 找到应该被转换的行（有native_info且clickitem_为空）
        should_transform = (df['nativeinfo_'] != '') & (df['clickitem_'] == '')
        transformed_count = should_transform.sum()
        print(f"应该被转换的行数: {transformed_count}")
        
        # 检查转换后的结果
        if transformed_count > 0:
            sample_idx = should_transform[should_transform].index[0]
            original_decoded = df.loc[sample_idx, 'decoded']
            original_native = df.loc[sample_idx, 'nativeinfo_']
            result_decoded = result_single.loc[sample_idx, 'decoded']
            
            print(f"样本转换检查 (索引 {sample_idx}):")
            print(f"  原始decoded: {original_decoded[:50]}...")
            print(f"  原始native: {original_native[:50]}...")
            print(f"  转换后包含<page>: {'<page>' in result_decoded}")
            print(f"  转换后包含<iframe>: {'<iframe>' in result_decoded}")

if __name__ == "__main__":
    main()
