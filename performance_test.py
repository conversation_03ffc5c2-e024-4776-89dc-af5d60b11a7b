#!/usr/bin/env python3
"""
性能测试脚本：比较group_by_session_id优化前后的处理速度
"""

import time
import subprocess
import argparse
import os
import multiprocessing as mp
from datetime import datetime
import shutil

def run_performance_test(session_cgi_label="cgi7", test_size="small"):
    """运行性能测试"""
    
    print("=" * 60)
    print("数据处理性能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"系统信息: CPU核心数={mp.cpu_count()}")
    print(f"会话标签: {session_cgi_label}")
    print(f"测试规模: {test_size}")
    print()
    
    # 根据测试规模设置参数
    if test_size == "small":
        chunk_size = 500
        max_workers = 2
    elif test_size == "medium":
        chunk_size = 1000
        max_workers = 4
    else:  # large
        chunk_size = 2000
        max_workers = min(8, mp.cpu_count())
    
    # 测试文件名
    original_file = f"test_original_{session_cgi_label}.pickle"
    optimized_file = f"test_optimized_{session_cgi_label}.pickle"
    
    results = {}
    
    # 测试1: 原始版本（单进程）
    print("🔄 测试1: 原始版本（单进程）")
    print("-" * 40)
    
    start_time = time.time()
    try:
        result = subprocess.run([
            "python", "xml_decode_b_understand_all_app.py",
            f"--output_file_name={original_file}",
            "--start_time=2025-06-05 00:00:00",
            "--end_time=2025-06-05 07:59:59",
            f"--session_cgi_label={session_cgi_label}",
            "--use_multiprocess=False"
        ], capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        end_time = time.time()
        original_time = end_time - start_time
        
        if result.returncode == 0:
            results['original'] = {
                'time': original_time,
                'success': True,
                'file_size': os.path.getsize(original_file) if os.path.exists(original_file) else 0
            }
            print(f"✅ 原始版本完成，耗时: {original_time:.2f} 秒")
            if os.path.exists(original_file):
                print(f"   文件大小: {os.path.getsize(original_file) / 1024 / 1024:.2f} MB")
        else:
            results['original'] = {'time': original_time, 'success': False, 'error': result.stderr}
            print(f"❌ 原始版本失败，耗时: {original_time:.2f} 秒")
            print(f"   错误信息: {result.stderr[:200]}...")
            
    except subprocess.TimeoutExpired:
        results['original'] = {'time': 3600, 'success': False, 'error': 'Timeout'}
        print("❌ 原始版本超时（1小时）")
    except Exception as e:
        results['original'] = {'time': 0, 'success': False, 'error': str(e)}
        print(f"❌ 原始版本异常: {e}")
    
    print()
    
    # 测试2: 优化版本（多进程）
    print("🚀 测试2: 优化版本（多进程）")
    print("-" * 40)
    
    start_time = time.time()
    try:
        result = subprocess.run([
            "python", "xml_decode_b_understand_all_app.py",
            f"--output_file_name={optimized_file}",
            "--start_time=2025-06-05 00:00:00",
            "--end_time=2025-06-05 07:59:59",
            f"--session_cgi_label={session_cgi_label}",
            "--use_multiprocess=True",
            f"--chunk_size={chunk_size}",
            f"--max_workers={max_workers}"
        ], capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        end_time = time.time()
        optimized_time = end_time - start_time
        
        if result.returncode == 0:
            results['optimized'] = {
                'time': optimized_time,
                'success': True,
                'file_size': os.path.getsize(optimized_file) if os.path.exists(optimized_file) else 0
            }
            print(f"✅ 优化版本完成，耗时: {optimized_time:.2f} 秒")
            if os.path.exists(optimized_file):
                print(f"   文件大小: {os.path.getsize(optimized_file) / 1024 / 1024:.2f} MB")
        else:
            results['optimized'] = {'time': optimized_time, 'success': False, 'error': result.stderr}
            print(f"❌ 优化版本失败，耗时: {optimized_time:.2f} 秒")
            print(f"   错误信息: {result.stderr[:200]}...")
            
    except subprocess.TimeoutExpired:
        results['optimized'] = {'time': 3600, 'success': False, 'error': 'Timeout'}
        print("❌ 优化版本超时（1小时）")
    except Exception as e:
        results['optimized'] = {'time': 0, 'success': False, 'error': str(e)}
        print(f"❌ 优化版本异常: {e}")
    
    print()
    
    # 结果分析
    print("📊 性能测试结果")
    print("=" * 60)
    
    if results.get('original', {}).get('success') and results.get('optimized', {}).get('success'):
        original_time = results['original']['time']
        optimized_time = results['optimized']['time']
        speedup = original_time / optimized_time
        time_saved = original_time - optimized_time
        
        print(f"原始版本耗时:   {original_time:.2f} 秒")
        print(f"优化版本耗时:   {optimized_time:.2f} 秒")
        print(f"加速比:         {speedup:.2f}x")
        print(f"节省时间:       {time_saved:.2f} 秒 ({time_saved/60:.1f} 分钟)")
        print(f"性能提升:       {(speedup-1)*100:.1f}%")
        
        if speedup > 1:
            print(f"🎉 优化成功！处理速度提升了 {(speedup-1)*100:.1f}%")
        else:
            print(f"⚠️  优化效果不明显，可能需要调整参数")
            
    else:
        print("⚠️  无法完成完整的性能比较，部分测试失败")
        for version, result in results.items():
            if not result.get('success'):
                print(f"   {version}: {result.get('error', 'Unknown error')}")
    
    # 清理测试文件
    print("\n🧹 清理测试文件...")
    for file in [original_file, optimized_file]:
        if os.path.exists(file):
            os.remove(file)
            print(f"   删除: {file}")
    
    print("\n✅ 性能测试完成！")
    return results

def test_group_by_session_performance(input_pickle_dir="./", target_pickles="all"):
    """专门测试group_by_session_id的性能"""

    print("=" * 80)
    print("Group By Session 性能测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"输入目录: {input_pickle_dir}")
    print(f"目标文件: {target_pickles}")
    print()

    # 备份原始版本（如果还没有备份）
    if os.path.exists("group_by_session_id.py") and not os.path.exists("group_by_session_id_original.py"):
        print("📋 备份原始版本...")
        shutil.copy("group_by_session_id.py", "group_by_session_id_original.py")

    test_configs = [
        {
            "name": "原始版本",
            "script": "group_by_session_id_original.py",
            "output": "output_original.json",
            "description": "未优化的原始版本"
        },
        {
            "name": "优化版本",
            "script": "group_by_session_id.py",
            "output": "output_optimized.json",
            "description": "智能选择处理方式的优化版本"
        },
        {
            "name": "超级优化版本",
            "script": "group_by_session_id_ultra_optimized.py",
            "output": "output_ultra.json",
            "description": "专为大数据优化的超级版本"
        }
    ]

    results = {}

    for config in test_configs:
        if not os.path.exists(config["script"]):
            print(f"⚠️  跳过 {config['name']}: 脚本 {config['script']} 不存在")
            continue

        print(f"\n🔄 测试 {config['name']}")
        print(f"   描述: {config['description']}")
        print("-" * 60)

        # 清理之前的输出文件
        if os.path.exists(config["output"]):
            os.remove(config["output"])

        # 构建命令
        if config["script"] == "group_by_session_id_ultra_optimized.py":
            cmd = [
                "python", config["script"],
                f"--input_pickle_dir={input_pickle_dir}",
                f"--target_pickles={target_pickles}",
                "--trajectory_length_min=1",
                "--trajectory_length_max=20",
                f"--output_json_path={config['output']}"
            ]
        else:
            cmd = [
                "python", config["script"],
                "--function=group_by_session_id",
                f"--input_pickle_dir={input_pickle_dir}",
                f"--target_pickles={target_pickles}",
                "--trajectory_length_min=1",
                "--trajectory_length_max=20",
                f"--output_json_path={config['output']}"
            ]

        start_time = time.time()

        try:
            print(f"   执行命令: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=7200  # 2小时超时
            )

            end_time = time.time()
            duration = end_time - start_time

            if result.returncode == 0:
                output_size = os.path.getsize(config["output"]) if os.path.exists(config["output"]) else 0
                results[config["name"]] = {
                    "success": True,
                    "duration": duration,
                    "output_size": output_size,
                    "output_file": config["output"]
                }
                print(f"✅ {config['name']} 完成")
                print(f"   耗时: {duration:.2f} 秒 ({duration/60:.2f} 分钟)")
                print(f"   输出文件大小: {output_size / (1024*1024):.2f} MB")

                # 显示部分输出
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 10:
                        print("   输出摘要:")
                        for line in lines[-5:]:  # 显示最后5行
                            print(f"     {line}")

            else:
                results[config["name"]] = {
                    "success": False,
                    "duration": duration,
                    "error": result.stderr
                }
                print(f"❌ {config['name']} 失败")
                print(f"   耗时: {duration:.2f} 秒")
                print(f"   错误: {result.stderr[:300]}...")

        except subprocess.TimeoutExpired:
            results[config["name"]] = {
                "success": False,
                "duration": 7200,
                "error": "Timeout (2 hours)"
            }
            print(f"❌ {config['name']} 超时（2小时）")

        except Exception as e:
            results[config["name"]] = {
                "success": False,
                "duration": 0,
                "error": str(e)
            }
            print(f"❌ {config['name']} 异常: {e}")

    # 结果分析
    print(f"\n📊 性能测试结果总结")
    print("=" * 80)

    successful_results = {k: v for k, v in results.items() if v.get("success")}

    if successful_results:
        print(f"{'版本':<15} {'耗时(秒)':<12} {'耗时(分钟)':<12} {'输出大小(MB)':<15} {'状态'}")
        print("-" * 80)

        for name, result in results.items():
            if result.get("success"):
                duration = result["duration"]
                size_mb = result.get("output_size", 0) / (1024*1024)
                print(f"{name:<15} {duration:<12.2f} {duration/60:<12.2f} {size_mb:<15.2f} ✅")
            else:
                print(f"{name:<15} {'失败':<12} {'失败':<12} {'N/A':<15} ❌")

        # 找出最快的版本
        if len(successful_results) > 1:
            fastest = min(successful_results.items(), key=lambda x: x[1]["duration"])
            slowest = max(successful_results.items(), key=lambda x: x[1]["duration"])

            speedup = slowest[1]["duration"] / fastest[1]["duration"]
            time_saved = slowest[1]["duration"] - fastest[1]["duration"]

            print(f"\n🏆 最快版本: {fastest[0]}")
            print(f"   耗时: {fastest[1]['duration']:.2f} 秒")
            print(f"   相比最慢版本 ({slowest[0]}) 提升:")
            print(f"     加速比: {speedup:.2f}x")
            print(f"     节省时间: {time_saved:.2f} 秒 ({time_saved/60:.2f} 分钟)")
            print(f"     性能提升: {(speedup-1)*100:.1f}%")

    else:
        print("⚠️  所有测试都失败了")
        for name, result in results.items():
            print(f"   {name}: {result.get('error', 'Unknown error')}")

    print(f"\n✅ Group By Session 性能测试完成！")
    return results

def run_quick_test():
    """快速测试：只测试小数据量"""
    print("🏃‍♂️ 快速性能测试（小数据量）")
    return run_performance_test(test_size="small")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="数据处理性能测试")
    parser.add_argument("--session_cgi_label", type=str, default="cgi7", help="会话 CGI 标签")
    parser.add_argument("--test_size", choices=["small", "medium", "large"], default="medium",
                       help="测试规模")
    parser.add_argument("--quick", action="store_true", help="快速测试模式")
    parser.add_argument("--test_group_by", action="store_true", help="测试group_by_session_id性能")
    parser.add_argument("--input_pickle_dir", type=str, default="./", help="pickle文件目录")
    parser.add_argument("--target_pickles", type=str, default="all", help="目标pickle文件")

    args = parser.parse_args()

    if args.test_group_by:
        test_group_by_session_performance(args.input_pickle_dir, args.target_pickles)
    elif args.quick:
        run_quick_test()
    else:
        run_performance_test(args.session_cgi_label, args.test_size)
