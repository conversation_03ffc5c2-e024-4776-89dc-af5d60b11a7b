import requests

def get_annotation_ids_by_tag(tag, page_size=200):
    """
    根据tag获取所有的annotation ID
    """
    # 构建URL
    url = f"http://mmfinderdrannotationsvr.polaris:8080/annotations/tasks"

    # 查询参数
    params = {
        "page_size": page_size,
        "tag": tag,
        "status": "success",
        "is_deleted": 0
    }

    annotation_ids = []
    page = 1

    try:
        while True:
            params["page"] = page
            print(f"正在获取第 {page} 页的annotation ID...")

            # 发送GET请求
            response = requests.get(url, params=params)
            response.raise_for_status()

            data = response.json()

            if data["code"] != 0:
                print(f"API返回错误: {data['msg']}")
                break

            tasks = data["data"]["tasks"]
            if not tasks:
                print("没有更多数据")
                break

            # 提取annotation ID
            for task in tasks:
                if "backfilled_annotation_id" in task and task["backfilled_annotation_id"]:
                    annotation_ids.append(task["backfilled_annotation_id"])

            print(f"第 {page} 页获取到 {len(tasks)} 个任务")

            # 检查是否还有更多页
            total = data["data"]["total"]
            current_count = page * page_size
            if current_count >= total:
                break

            page += 1

    except requests.exceptions.RequestException as e:
        print(f"获取annotation ID失败: {e}")
        return []

    print(f"总共获取到 {len(annotation_ids)} 个annotation ID")
    return annotation_ids

def get_annotation_records(annotation_ids, limit=50):
    """
    根据annotation ID列表获取对应的流水记录
    """
    base_url = "http://mmfinderdrannotationmanagersvr.production.polaris/v1/sandbox/annotations"

    all_records = []

    # 分批处理annotation ID，避免URL过长
    batch_size = 10
    for i in range(0, len(annotation_ids), batch_size):
        batch_ids = annotation_ids[i:i + batch_size]

        print(f"正在获取第 {i//batch_size + 1} 批annotation记录 (IDs: {batch_ids})")

        # 查询参数
        params = {
            "limit": limit,
            "offset": 0,
            "isDeleted": 0,
            "annotationIds": ",".join(map(str, batch_ids))  # 将ID列表转换为逗号分隔的字符串
        }

        try:
            # 发送GET请求
            response = requests.get(base_url, params=params)
            response.raise_for_status()

            data = response.json()
            if isinstance(data, list):
                all_records.extend(data)
                print(f"获取到 {len(data)} 条记录")
            else:
                print(f"意外的响应格式: {data}")

        except requests.exceptions.RequestException as e:
            print(f"获取annotation记录失败 (批次 {i//batch_size + 1}): {e}")

    return all_records

def main():
    # 配置参数
    tag = "纵向评估集重标"  # 可以修改为其他tag

    print(f"开始处理tag: {tag}")

    # 第一步：根据tag获取所有annotation ID
    annotation_ids = get_annotation_ids_by_tag(tag)

    if not annotation_ids:
        print("没有找到任何annotation ID，程序结束")
        return

    # 第二步：根据annotation ID获取流水记录
    print("\n开始获取annotation记录...")
    records = get_annotation_records(annotation_ids)

    # 输出结果
    print(f"\n总共获取到 {len(records)} 条annotation记录")
    if records:
        print("前几条记录示例:")
        for i, record in enumerate(records[:3]):  # 只显示前3条作为示例
            print(f"记录 {i+1}: {record}")

if __name__ == "__main__":
    main()